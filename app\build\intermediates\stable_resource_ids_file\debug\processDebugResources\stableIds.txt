com.example.fragmentsleam:xml/m3expressive_connected_buttons_inner_corner_size_state_list = 0x7f120005
com.example.fragmentsleam:xml/m3expressive_button_shape_state_list = 0x7f120004
com.example.fragmentsleam:xml/m3_split_button_inner_corner_size_state_list = 0x7f120003
com.example.fragmentsleam:xml/data_extraction_rules = 0x7f120001
com.example.fragmentsleam:xml/backup_rules = 0x7f120000
com.example.fragmentsleam:styleable/ViewStubCompat = 0x7f11009d
com.example.fragmentsleam:styleable/ViewBackgroundHelper = 0x7f11009b
com.example.fragmentsleam:styleable/Variant = 0x7f110099
com.example.fragmentsleam:styleable/Tooltip = 0x7f110096
com.example.fragmentsleam:styleable/ThemeEnforcement = 0x7f110094
com.example.fragmentsleam:styleable/TextInputEditText = 0x7f110092
com.example.fragmentsleam:styleable/TextEffects = 0x7f110091
com.example.fragmentsleam:styleable/TabItem = 0x7f11008e
com.example.fragmentsleam:styleable/StateListDrawableItem = 0x7f110089
com.example.fragmentsleam:styleable/StateListDrawable = 0x7f110088
com.example.fragmentsleam:styleable/State = 0x7f110087
com.example.fragmentsleam:styleable/Spinner = 0x7f110086
com.example.fragmentsleam:styleable/Snackbar = 0x7f110084
com.example.fragmentsleam:styleable/ShapeableImageView = 0x7f110081
com.example.fragmentsleam:styleable/SearchBar = 0x7f11007e
com.example.fragmentsleam:styleable/ScrollingViewBehavior_Layout = 0x7f11007d
com.example.fragmentsleam:styleable/RecyclerView = 0x7f11007b
com.example.fragmentsleam:styleable/RecycleListView = 0x7f11007a
com.example.fragmentsleam:styleable/PopupWindow = 0x7f110075
com.example.fragmentsleam:styleable/OnClick = 0x7f110073
com.example.fragmentsleam:styleable/NavigationView = 0x7f110072
com.example.fragmentsleam:styleable/NavigationRailView = 0x7f110071
com.example.fragmentsleam:styleable/NavigationBarActiveIndicator = 0x7f11006f
com.example.fragmentsleam:styleable/MotionTelltales = 0x7f11006e
com.example.fragmentsleam:styleable/MotionLabel = 0x7f11006b
com.example.fragmentsleam:styleable/MotionHelper = 0x7f11006a
com.example.fragmentsleam:styleable/Motion = 0x7f110068
com.example.fragmentsleam:styleable/MenuView = 0x7f110066
com.example.fragmentsleam:styleable/MaterialTimePicker = 0x7f110062
com.example.fragmentsleam:styleable/MaterialTextView = 0x7f110061
com.example.fragmentsleam:styleable/MaterialTextAppearance = 0x7f110060
com.example.fragmentsleam:styleable/MaterialSwitch = 0x7f11005f
com.example.fragmentsleam:styleable/MaterialSpring = 0x7f11005e
com.example.fragmentsleam:styleable/MaterialShape = 0x7f11005d
com.example.fragmentsleam:styleable/MaterialRadioButton = 0x7f11005c
com.example.fragmentsleam:styleable/MaterialCheckBox = 0x7f110059
com.example.fragmentsleam:styleable/MaterialCardView = 0x7f110058
com.example.fragmentsleam:styleable/MaterialCalendar = 0x7f110056
com.example.fragmentsleam:styleable/MaterialButtonToggleGroup = 0x7f110055
com.example.fragmentsleam:styleable/MaterialAutoCompleteTextView = 0x7f110052
com.example.fragmentsleam:styleable/MaterialAlertDialogTheme = 0x7f110051
com.example.fragmentsleam:styleable/MaterialAlertDialog = 0x7f110050
com.example.fragmentsleam:styleable/LoadingIndicator = 0x7f11004f
com.example.fragmentsleam:styleable/ListPopupWindow = 0x7f11004e
com.example.fragmentsleam:styleable/LinearProgressIndicator = 0x7f11004d
com.example.fragmentsleam:styleable/LinearLayoutCompat_Layout = 0x7f11004c
com.example.fragmentsleam:styleable/Layout = 0x7f11004a
com.example.fragmentsleam:styleable/KeyTrigger = 0x7f110049
com.example.fragmentsleam:styleable/KeyFramesVelocity = 0x7f110046
com.example.fragmentsleam:styleable/KeyFramesAcceleration = 0x7f110045
com.example.fragmentsleam:styleable/KeyCycle = 0x7f110043
com.example.fragmentsleam:styleable/KeyAttribute = 0x7f110042
com.example.fragmentsleam:styleable/Grid = 0x7f11003f
com.example.fragmentsleam:styleable/GradientColor = 0x7f11003d
com.example.fragmentsleam:styleable/FragmentContainerView = 0x7f11003c
com.example.fragmentsleam:styleable/FontFamily = 0x7f110038
com.example.fragmentsleam:styleable/FlowLayout = 0x7f110037
com.example.fragmentsleam:styleable/FloatingActionButton_Behavior_Layout = 0x7f110035
com.example.fragmentsleam:styleable/FloatingActionButton = 0x7f110034
com.example.fragmentsleam:attr/subtitleMaxLines = 0x7f030471
com.example.fragmentsleam:color/m3_ref_palette_orange20 = 0x7f05017f
com.example.fragmentsleam:styleable/ConstraintLayout_placeholder = 0x7f110029
com.example.fragmentsleam:styleable/ConstraintLayout_Layout = 0x7f110027
com.example.fragmentsleam:id/accessibility_custom_action_8 = 0x7f08002f
com.example.fragmentsleam:styleable/Constraint = 0x7f110026
com.example.fragmentsleam:styleable/CollapsingToolbarLayout = 0x7f110022
com.example.fragmentsleam:styleable/Chip = 0x7f11001d
com.example.fragmentsleam:style/Widget.AppCompat.SearchView.ActionBar = 0x7f100370
com.example.fragmentsleam:styleable/ButtonBarLayout = 0x7f110018
com.example.fragmentsleam:styleable/AppCompatSeekBar = 0x7f11000f
com.example.fragmentsleam:styleable/AppBarLayout = 0x7f11000a
com.example.fragmentsleam:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.example.fragmentsleam:styleable/AnimatedStateListDrawableItem = 0x7f110008
com.example.fragmentsleam:styleable/AnimatedStateListDrawableCompat = 0x7f110007
com.example.fragmentsleam:styleable/AlertDialog = 0x7f110006
com.example.fragmentsleam:attr/layout_constraintWidth_min = 0x7f0302ee
com.example.fragmentsleam:styleable/ActionMenuView = 0x7f110003
com.example.fragmentsleam:style/Widget.MaterialComponents.Tooltip = 0x7f1004cd
com.example.fragmentsleam:color/m3_timepicker_secondary_text_button_text_color = 0x7f0502af
com.example.fragmentsleam:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f1004cc
com.example.fragmentsleam:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f1004cb
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_primary = 0x7f050250
com.example.fragmentsleam:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f1004ca
com.example.fragmentsleam:style/Widget.MaterialComponents.Toolbar = 0x7f1004c9
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f1004c7
com.example.fragmentsleam:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f100218
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_pressed_label_text_color = 0x7f0c01f5
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f1004c6
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f100401
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1004c5
com.example.fragmentsleam:dimen/m3_comp_suggestion_chip_container_height = 0x7f060236
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f1004c4
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f1004c1
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.Button = 0x7f1004c0
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1004bd
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f1004bb
com.example.fragmentsleam:attr/SharedValue = 0x7f030000
com.example.fragmentsleam:color/ripple_material_dark = 0x7f0503a3
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1004b4
com.example.fragmentsleam:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f10026e
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1004b3
com.example.fragmentsleam:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b004c
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f1004b2
com.example.fragmentsleam:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f1004b0
com.example.fragmentsleam:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301f4
com.example.fragmentsleam:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f1004ad
com.example.fragmentsleam:style/Widget.MaterialComponents.ShapeableImageView = 0x7f1004aa
com.example.fragmentsleam:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f1004a7
com.example.fragmentsleam:dimen/m3_comp_split_button_small_trailing_button_leading_space = 0x7f06022a
com.example.fragmentsleam:style/Widget.MaterialComponents.NavigationView = 0x7f1004a4
com.example.fragmentsleam:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f1004a2
com.example.fragmentsleam:style/Widget.MaterialComponents.NavigationRailView = 0x7f10049f
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f10049a
com.example.fragmentsleam:color/m3_ref_palette_tertiary10 = 0x7f0501cd
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f100499
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f100496
com.example.fragmentsleam:attr/colorSurfaceBright = 0x7f03012c
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f100492
com.example.fragmentsleam:dimen/abc_text_size_title_material = 0x7f06004f
com.example.fragmentsleam:id/SYM = 0x7f08000b
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f100491
com.example.fragmentsleam:color/m3_popupmenu_overlay_color = 0x7f05009b
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f10048f
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f10048c
com.example.fragmentsleam:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f06032b
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f10048b
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f10048a
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f100487
com.example.fragmentsleam:dimen/mtrl_btn_corner_radius = 0x7f06033e
com.example.fragmentsleam:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1003c9
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f100485
com.example.fragmentsleam:attr/collapsedTitleTextAppearance = 0x7f0300f3
com.example.fragmentsleam:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f100481
com.example.fragmentsleam:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f100480
com.example.fragmentsleam:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f10047f
com.example.fragmentsleam:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f10047c
com.example.fragmentsleam:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f10047a
com.example.fragmentsleam:style/Widget.MaterialComponents.Chip.Action = 0x7f100473
com.example.fragmentsleam:attr/materialSizeOverlay = 0x7f03034f
com.example.fragmentsleam:style/Widget.MaterialComponents.CheckedTextView = 0x7f100472
com.example.fragmentsleam:attr/textAppearanceTitleSmall = 0x7f0304d1
com.example.fragmentsleam:attr/titlePositionInterpolator = 0x7f030518
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f100470
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f10046d
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f10046c
com.example.fragmentsleam:style/Widget.MaterialComponents.Button = 0x7f100465
com.example.fragmentsleam:color/m3_text_button_background_color_selector = 0x7f05029f
com.example.fragmentsleam:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f100464
com.example.fragmentsleam:dimen/design_snackbar_padding_vertical = 0x7f060088
com.example.fragmentsleam:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f100462
com.example.fragmentsleam:style/Widget.MaterialComponents.Badge = 0x7f10045c
com.example.fragmentsleam:macro/m3_comp_button_filled_unselected_icon_color = 0x7f0c001f
com.example.fragmentsleam:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f10045a
com.example.fragmentsleam:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f100456
com.example.fragmentsleam:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f100455
com.example.fragmentsleam:id/flip = 0x7f0800ca
com.example.fragmentsleam:macro/m3_comp_app_bar_subtitle_color = 0x7f0c000b
com.example.fragmentsleam:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f100451
com.example.fragmentsleam:style/Widget.Material3.Tooltip = 0x7f10044f
com.example.fragmentsleam:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f10044a
com.example.fragmentsleam:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f100447
com.example.fragmentsleam:dimen/abc_dialog_min_width_major = 0x7f060022
com.example.fragmentsleam:styleable/AppBarLayout_Layout = 0x7f11000c
com.example.fragmentsleam:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f100444
com.example.fragmentsleam:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f100443
com.example.fragmentsleam:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f100440
com.example.fragmentsleam:style/Widget.Material3.TabLayout = 0x7f10043d
com.example.fragmentsleam:style/Widget.Material3.SplitButton.IconButton.Filled.Tonal = 0x7f10043a
com.example.fragmentsleam:style/Widget.AppCompat.ListView = 0x7f100364
com.example.fragmentsleam:style/Widget.Material3.Snackbar.TextView = 0x7f100438
com.example.fragmentsleam:style/Widget.Material3.Snackbar = 0x7f100436
com.example.fragmentsleam:style/Widget.Material3.Slider.Label = 0x7f100433
com.example.fragmentsleam:style/Widget.Material3.Slider = 0x7f100432
com.example.fragmentsleam:color/abc_search_url_text_pressed = 0x7f05000f
com.example.fragmentsleam:color/mtrl_btn_ripple_color = 0x7f050363
com.example.fragmentsleam:style/Widget.Material3.SideSheet = 0x7f10042e
com.example.fragmentsleam:style/Widget.Material3.SearchView.Toolbar = 0x7f10042d
com.example.fragmentsleam:style/Widget.Material3.SearchBar.Outlined = 0x7f10042a
com.example.fragmentsleam:style/Widget.Material3.SearchBar = 0x7f100429
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_pressed_icon_color = 0x7f0c01fd
com.example.fragmentsleam:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f100428
com.example.fragmentsleam:attr/panelMenuListTheme = 0x7f0303ca
com.example.fragmentsleam:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f0603e7
com.example.fragmentsleam:style/Widget.Material3.NavigationRailView.Badge = 0x7f100421
com.example.fragmentsleam:color/material_dynamic_secondary0 = 0x7f0502f4
com.example.fragmentsleam:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f100420
com.example.fragmentsleam:attr/colorSurfaceDim = 0x7f030132
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f10041a
com.example.fragmentsleam:style/Widget.AppCompat.ListView.Menu = 0x7f100366
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionBar = 0x7f1000c0
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f100411
com.example.fragmentsleam:attr/preserveIconSpacing = 0x7f0303e5
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f10040b
com.example.fragmentsleam:style/Widget.Support.CoordinatorLayout = 0x7f1004ce
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f100409
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f100408
com.example.fragmentsleam:attr/barrierAllowsGoneWidgets = 0x7f030065
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f100407
com.example.fragmentsleam:attr/trackCornerSize = 0x7f03052f
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f100406
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f100405
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0c0119
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f100402
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1003fe
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Day = 0x7f1003fd
com.example.fragmentsleam:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1003fb
com.example.fragmentsleam:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1003f6
com.example.fragmentsleam:attr/hintMaxLines = 0x7f03025e
com.example.fragmentsleam:style/Widget.Material3.LinearProgressIndicator = 0x7f1003f5
com.example.fragmentsleam:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1003f4
com.example.fragmentsleam:color/m3_ref_palette_grey98 = 0x7f050148
com.example.fragmentsleam:id/accessibility_custom_action_16 = 0x7f080019
com.example.fragmentsleam:style/Widget.Material3.FloatingToolbar.Vibrant = 0x7f1003f3
com.example.fragmentsleam:animator/design_fab_show_motion_spec = 0x7f020004
com.example.fragmentsleam:style/Widget.Material3.FloatingToolbar.TextButton.Vibrant = 0x7f1003f2
com.example.fragmentsleam:style/Widget.Material3.FloatingToolbar.Button = 0x7f1003ed
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1003eb
com.example.fragmentsleam:string/bottomsheet_action_expand_halfway = 0x7f0f0021
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1003ea
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1003e6
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1003e2
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f0602ce
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1003e1
com.example.fragmentsleam:attr/iconSize = 0x7f03026e
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1003dd
com.example.fragmentsleam:id/NO_DEBUG = 0x7f080006
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Medium = 0x7f1003d9
com.example.fragmentsleam:id/transitionToEnd = 0x7f0801f5
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Large = 0x7f1003d8
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1003d7
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1003d6
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1003d5
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f100118
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1003d4
com.example.fragmentsleam:style/Widget.Material3.DockedToolbar.Vibrant = 0x7f1003d2
com.example.fragmentsleam:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700b6
com.example.fragmentsleam:style/Widget.Material3.DockedToolbar.TextButton.Vibrant = 0x7f1003d1
com.example.fragmentsleam:style/Widget.Material3.DockedToolbar.TextButton = 0x7f1003d0
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f100099
com.example.fragmentsleam:style/Widget.Material3.DockedToolbar.IconButton.Vibrant = 0x7f1003cf
com.example.fragmentsleam:dimen/m3_alert_dialog_elevation = 0x7f0600a2
com.example.fragmentsleam:style/Widget.Material3.DockedToolbar.Button = 0x7f1003cc
com.example.fragmentsleam:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1003c5
com.example.fragmentsleam:style/Widget.Material3.CollapsingToolbar = 0x7f1003c4
com.example.fragmentsleam:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f1003c1
com.example.fragmentsleam:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f1003be
com.example.fragmentsleam:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f1003bd
com.example.fragmentsleam:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f1003b8
com.example.fragmentsleam:attr/recyclerViewStyle = 0x7f0303f8
com.example.fragmentsleam:style/Widget.Material3.Chip.Filter.Elevated = 0x7f1003b4
com.example.fragmentsleam:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f100147
com.example.fragmentsleam:macro/m3_comp_fab_tertiary_container_color = 0x7f0c00a7
com.example.fragmentsleam:style/Widget.Material3.Chip.Filter = 0x7f1003b3
com.example.fragmentsleam:attr/tooltipFrameBackground = 0x7f030523
com.example.fragmentsleam:style/Widget.Material3.CardView.Filled = 0x7f1003ae
com.example.fragmentsleam:integer/mtrl_chip_anim_duration = 0x7f090038
com.example.fragmentsleam:style/Widget.Material3.CardView.Elevated = 0x7f1003ad
com.example.fragmentsleam:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0c01a0
com.example.fragmentsleam:style/Widget.Material3.Button.TonalButton.Icon = 0x7f1003ab
com.example.fragmentsleam:style/Widget.Material3.Button.TonalButton = 0x7f1003aa
com.example.fragmentsleam:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f1003a9
com.example.fragmentsleam:layout/design_bottom_navigation_item = 0x7f0b001e
com.example.fragmentsleam:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f1003a7
com.example.fragmentsleam:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070041
com.example.fragmentsleam:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f1003a3
com.example.fragmentsleam:drawable/design_fab_background = 0x7f070082
com.example.fragmentsleam:attr/materialCalendarHeaderSelection = 0x7f030335
com.example.fragmentsleam:styleable/BottomAppBar = 0x7f110015
com.example.fragmentsleam:color/material_dynamic_neutral_variant0 = 0x7f0502da
com.example.fragmentsleam:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f1002a6
com.example.fragmentsleam:style/Widget.Material3.Button.OutlinedButton = 0x7f1003a2
com.example.fragmentsleam:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f10045e
com.example.fragmentsleam:integer/material_motion_duration_long_1 = 0x7f090029
com.example.fragmentsleam:style/Widget.Material3.Button.IconButton.Outlined = 0x7f1003a1
com.example.fragmentsleam:style/Widget.Material3.Button.IconButton = 0x7f10039e
com.example.fragmentsleam:drawable/ic_expand_less_22px = 0x7f070090
com.example.fragmentsleam:style/Widget.Material3.BottomSheet.Modal = 0x7f100399
com.example.fragmentsleam:color/design_default_color_error = 0x7f050040
com.example.fragmentsleam:styleable/DockedToolbar = 0x7f11002f
com.example.fragmentsleam:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f100396
com.example.fragmentsleam:style/Widget.Material3.BottomNavigationView = 0x7f100395
com.example.fragmentsleam:color/material_grey_50 = 0x7f050310
com.example.fragmentsleam:style/Widget.Material3.BottomNavigation.Badge = 0x7f100394
com.example.fragmentsleam:styleable/Slider = 0x7f110083
com.example.fragmentsleam:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f100392
com.example.fragmentsleam:dimen/m3_comp_toolbar_docked_container_trailing_space = 0x7f060253
com.example.fragmentsleam:style/Widget.Material3.Badge.AdjustToBounds = 0x7f100390
com.example.fragmentsleam:drawable/ic_clock_black_24dp = 0x7f07008f
com.example.fragmentsleam:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f10038e
com.example.fragmentsleam:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f10038c
com.example.fragmentsleam:anim/design_bottom_sheet_slide_out = 0x7f010019
com.example.fragmentsleam:style/Widget.Design.TextInputEditText = 0x7f100386
com.example.fragmentsleam:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f100383
com.example.fragmentsleam:color/material_dynamic_neutral60 = 0x7f0502d4
com.example.fragmentsleam:style/Widget.Design.FloatingActionButton = 0x7f100381
com.example.fragmentsleam:style/Widget.Design.BottomSheet.Modal = 0x7f10037f
com.example.fragmentsleam:attr/thumbColor = 0x7f0304f1
com.example.fragmentsleam:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0c018c
com.example.fragmentsleam:style/Widget.Material3.Chip.Assist = 0x7f1003b1
com.example.fragmentsleam:style/Widget.Compat.NotificationActionText = 0x7f10037c
com.example.fragmentsleam:attr/behavior_peekHeight = 0x7f030071
com.example.fragmentsleam:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f100375
com.example.fragmentsleam:attr/boxBackgroundMode = 0x7f030081
com.example.fragmentsleam:style/Widget.AppCompat.Spinner.DropDown = 0x7f100374
com.example.fragmentsleam:color/m3_tabs_icon_color = 0x7f050299
com.example.fragmentsleam:style/Widget.AppCompat.Spinner = 0x7f100373
com.example.fragmentsleam:style/Widget.AppCompat.SeekBar = 0x7f100371
com.example.fragmentsleam:style/Widget.AppCompat.RatingBar.Small = 0x7f10036e
com.example.fragmentsleam:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007b
com.example.fragmentsleam:style/Widget.AppCompat.RatingBar.Indicator = 0x7f10036d
com.example.fragmentsleam:style/Widget.AppCompat.RatingBar = 0x7f10036c
com.example.fragmentsleam:style/Widget.Material3.Slider.Legacy.Label = 0x7f100435
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_focused_label_text_color = 0x7f0c01d6
com.example.fragmentsleam:style/Widget.AppCompat.PopupWindow = 0x7f100369
com.example.fragmentsleam:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f0601d0
com.example.fragmentsleam:style/Widget.AppCompat.ListMenuView = 0x7f100362
com.example.fragmentsleam:style/Widget.AppCompat.Light.SearchView = 0x7f100360
com.example.fragmentsleam:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f10035f
com.example.fragmentsleam:style/Widget.AppCompat.Light.PopupMenu = 0x7f10035e
com.example.fragmentsleam:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f10035a
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f100358
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f1002ac
com.example.fragmentsleam:macro/m3_comp_icon_button_medium_pressed_container_shape = 0x7f0c00be
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionButton = 0x7f100355
com.example.fragmentsleam:dimen/m3_comp_app_bar_small_container_height = 0x7f060105
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f100351
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f100350
com.example.fragmentsleam:dimen/compat_button_padding_horizontal_material = 0x7f060058
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f10034f
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f10034d
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.None = 0x7f10019a
com.example.fragmentsleam:styleable/Carousel = 0x7f11001b
com.example.fragmentsleam:color/m3_sys_color_tertiary_fixed_dim = 0x7f050298
com.example.fragmentsleam:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700d6
com.example.fragmentsleam:style/Widget.AppCompat.ImageButton = 0x7f10034b
com.example.fragmentsleam:style/Widget.AppCompat.EditText = 0x7f10034a
com.example.fragmentsleam:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f100349
com.example.fragmentsleam:id/scrollIndicatorDown = 0x7f080195
com.example.fragmentsleam:style/Widget.AppCompat.CompoundButton.Switch = 0x7f100347
com.example.fragmentsleam:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f100346
com.example.fragmentsleam:animator/m3_split_button_chevron_overshoot_interpolator = 0x7f020018
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500e7
com.example.fragmentsleam:attr/overlapAnchor = 0x7f0303be
com.example.fragmentsleam:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f100345
com.example.fragmentsleam:color/m3_ref_palette_green30 = 0x7f050133
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Item = 0x7f10040c
com.example.fragmentsleam:style/Widget.AppCompat.ActionBar.TabBar = 0x7f100334
com.example.fragmentsleam:style/Widget.AppCompat.ButtonBar = 0x7f100343
com.example.fragmentsleam:style/Widget.AppCompat.Button.Colored = 0x7f100341
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000f
com.example.fragmentsleam:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f060333
com.example.fragmentsleam:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f10033f
com.example.fragmentsleam:style/Widget.AppCompat.Button.Borderless = 0x7f10033e
com.example.fragmentsleam:style/Widget.AppCompat.Button = 0x7f10033d
com.example.fragmentsleam:style/Widget.AppCompat.AutoCompleteTextView = 0x7f10033c
com.example.fragmentsleam:styleable/AppCompatTextHelper = 0x7f110010
com.example.fragmentsleam:style/Widget.AppCompat.ActionButton.Overflow = 0x7f100339
com.example.fragmentsleam:style/Widget.AppCompat.ActionBar.TabView = 0x7f100336
com.example.fragmentsleam:style/Widget.AppCompat.ActionBar.Solid = 0x7f100333
com.example.fragmentsleam:dimen/m3_comp_button_group_standard_small_between_space = 0x7f060114
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f100331
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f100330
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f10032e
com.example.fragmentsleam:dimen/m3_comp_button_filled_container_elevation = 0x7f060111
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f10032c
com.example.fragmentsleam:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f10038d
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f100329
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_hovered_icon_color = 0x7f0c004a
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f100326
com.example.fragmentsleam:style/Base.V21.Theme.AppCompat.Light = 0x7f1000a2
com.example.fragmentsleam:attr/colorSurfaceContainerLow = 0x7f030130
com.example.fragmentsleam:style/TextAppearance.Design.HelperText = 0x7f1001f3
com.example.fragmentsleam:id/enterAlwaysCollapsed = 0x7f0800b9
com.example.fragmentsleam:style/Widget.Material3.Toolbar.Surface = 0x7f10044e
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f100325
com.example.fragmentsleam:integer/m3_sys_motion_duration_long2 = 0x7f090014
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f100320
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f10031e
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Light = 0x7f10031c
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f10031b
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f100319
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Dark = 0x7f100316
com.example.fragmentsleam:layout/mtrl_calendar_month_navigation = 0x7f0b0054
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f10030f
com.example.fragmentsleam:dimen/mtrl_card_corner_radius = 0x7f060381
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f10030e
com.example.fragmentsleam:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1002bf
com.example.fragmentsleam:layout/mtrl_calendar_month_labeled = 0x7f0b0053
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f10030d
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_focused_label_text_color = 0x7f0c01ef
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f10030c
com.example.fragmentsleam:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f100306
com.example.fragmentsleam:style/ThemeOverlay.Material3.SplitButton.IconButton.Filled.Tonal = 0x7f100301
com.example.fragmentsleam:dimen/mtrl_btn_padding_left = 0x7f06034b
com.example.fragmentsleam:style/ThemeOverlay.Material3.Snackbar = 0x7f1002ff
com.example.fragmentsleam:dimen/m3_comp_icon_button_xlarge_narrow_trailing_space = 0x7f06018a
com.example.fragmentsleam:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1002fc
com.example.fragmentsleam:color/m3_ref_palette_grey_variant95 = 0x7f050154
com.example.fragmentsleam:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1002fa
com.example.fragmentsleam:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1002f7
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f10017d
com.example.fragmentsleam:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1002f6
com.example.fragmentsleam:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.example.fragmentsleam:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1002f4
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f10032d
com.example.fragmentsleam:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1002f3
com.example.fragmentsleam:style/ThemeOverlay.Material3.FloatingToolbar = 0x7f1002ed
com.example.fragmentsleam:attr/crossfade = 0x7f03017a
com.example.fragmentsleam:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1002e9
com.example.fragmentsleam:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1002e7
com.example.fragmentsleam:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1002e6
com.example.fragmentsleam:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1002e4
com.example.fragmentsleam:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1002e3
com.example.fragmentsleam:style/ThemeOverlay.Material3.DockedToolbar.Vibrant = 0x7f1002e1
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050224
com.example.fragmentsleam:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1002df
com.example.fragmentsleam:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1002da
com.example.fragmentsleam:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f100340
com.example.fragmentsleam:layout/recycler_single_item = 0x7f0b006d
com.example.fragmentsleam:style/ThemeOverlay.Material3.Dark = 0x7f1002d9
com.example.fragmentsleam:style/Widget.Material3.Badge = 0x7f10038f
com.example.fragmentsleam:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1002d5
com.example.fragmentsleam:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1002d2
com.example.fragmentsleam:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1002cd
com.example.fragmentsleam:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1002cc
com.example.fragmentsleam:style/ThemeOverlay.Material3 = 0x7f1002c4
com.example.fragmentsleam:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1002c1
com.example.fragmentsleam:attr/tabPaddingEnd = 0x7f03048f
com.example.fragmentsleam:style/ThemeOverlay.AppCompat.DayNight = 0x7f1002be
com.example.fragmentsleam:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060135
com.example.fragmentsleam:dimen/m3_sys_elevation_level5 = 0x7f0602c4
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f10041d
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Toolbar.Vibrant.Vibrant.Container.Shape = 0x7f100187
com.example.fragmentsleam:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1002bd
com.example.fragmentsleam:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f1002b9
com.example.fragmentsleam:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f06036b
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f1002b6
com.example.fragmentsleam:attr/motionDurationExtraLong3 = 0x7f030378
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f1002b4
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f1002b0
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f1002af
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f1002ae
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f1002ab
com.example.fragmentsleam:color/bright_foreground_disabled_material_dark = 0x7f050022
com.example.fragmentsleam:style/Theme.MaterialComponents.Light = 0x7f1002a8
com.example.fragmentsleam:attr/boxCornerRadiusBottomEnd = 0x7f030083
com.example.fragmentsleam:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f1002a3
com.example.fragmentsleam:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f1002a2
com.example.fragmentsleam:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f1002a1
com.example.fragmentsleam:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f060167
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f10029e
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f10029a
com.example.fragmentsleam:attr/extendedFloatingActionButtonLargeStyle = 0x7f0301f2
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f100296
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f100291
com.example.fragmentsleam:string/material_slider_value = 0x7f0f0056
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f100290
com.example.fragmentsleam:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f10028c
com.example.fragmentsleam:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600e3
com.example.fragmentsleam:style/Theme.Material3.Light.DialogWhenLarge = 0x7f100288
com.example.fragmentsleam:style/Theme.Material3.Light.Dialog.Alert = 0x7f100286
com.example.fragmentsleam:attr/badgeHeight = 0x7f030054
com.example.fragmentsleam:attr/mock_diagonalsColor = 0x7f03036f
com.example.fragmentsleam:styleable/AppCompatTheme = 0x7f110012
com.example.fragmentsleam:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f100086
com.example.fragmentsleam:style/Theme.Material3.Light.Dialog = 0x7f100285
com.example.fragmentsleam:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f100282
com.example.fragmentsleam:styleable/View = 0x7f11009a
com.example.fragmentsleam:style/Theme.Material3.DynamicColors.Light = 0x7f100281
com.example.fragmentsleam:style/Theme.Material3.DynamicColors.DayNight = 0x7f10027f
com.example.fragmentsleam:dimen/design_fab_translation_z_pressed = 0x7f060076
com.example.fragmentsleam:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f10027e
com.example.fragmentsleam:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f100278
com.example.fragmentsleam:attr/quantizeMotionPhase = 0x7f0303ea
com.example.fragmentsleam:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f100276
com.example.fragmentsleam:style/Theme.Material3.Dark.Dialog.Alert = 0x7f100270
com.example.fragmentsleam:style/Theme.Material3.Dark = 0x7f10026d
com.example.fragmentsleam:style/Theme.FragmentSleam = 0x7f10026c
com.example.fragmentsleam:attr/actionBarStyle = 0x7f030007
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f100134
com.example.fragmentsleam:style/Theme.Design.Light.NoActionBar = 0x7f10026a
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f10002c
com.example.fragmentsleam:style/Theme.Design = 0x7f100266
com.example.fragmentsleam:style/Theme.AppCompat.NoActionBar = 0x7f100265
com.example.fragmentsleam:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f100261
com.example.fragmentsleam:style/Theme.AppCompat.Light.Dialog = 0x7f100260
com.example.fragmentsleam:style/Theme.AppCompat.Light.DarkActionBar = 0x7f10025f
com.example.fragmentsleam:style/Theme.AppCompat.DialogWhenLarge = 0x7f10025c
com.example.fragmentsleam:style/Theme.AppCompat.Dialog.Alert = 0x7f10025a
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_active_focused_state_layer_color = 0x7f0c00e4
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Body2 = 0x7f10023e
com.example.fragmentsleam:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f100258
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1004b9
com.example.fragmentsleam:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f100279
com.example.fragmentsleam:dimen/design_bottom_navigation_item_min_width = 0x7f060068
com.example.fragmentsleam:style/Theme.AppCompat.DayNight = 0x7f100252
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f10024b
com.example.fragmentsleam:macro/m3_comp_fab_secondary_container_icon_color = 0x7f0c00a2
com.example.fragmentsleam:style/Widget.AppCompat.ListView.DropDown = 0x7f100365
com.example.fragmentsleam:dimen/mtrl_calendar_day_corner = 0x7f06035b
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f100249
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Headline1 = 0x7f100242
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Chip = 0x7f100241
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Caption = 0x7f100240
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Button = 0x7f10023f
com.example.fragmentsleam:style/TextAppearance.Material3.TitleSmall.Emphasized = 0x7f10023b
com.example.fragmentsleam:style/TextAppearance.Material3.TitleMedium.Emphasized = 0x7f100239
com.example.fragmentsleam:style/TextAppearance.Material3.TitleMedium = 0x7f100238
com.example.fragmentsleam:style/TextAppearance.Material3.TitleLarge = 0x7f100236
com.example.fragmentsleam:style/TextAppearance.Material3.SearchBar = 0x7f100233
com.example.fragmentsleam:style/TextAppearance.Material3.LabelMedium.Emphasized = 0x7f10022f
com.example.fragmentsleam:style/TextAppearance.Material3.HeadlineMedium.Emphasized = 0x7f100229
com.example.fragmentsleam:style/TextAppearance.Material3.HeadlineLarge = 0x7f100226
com.example.fragmentsleam:style/TextAppearance.Material3.DisplaySmall.Emphasized = 0x7f100225
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.ButtonGroup.Connected.Small.Container.Shape = 0x7f10016c
com.example.fragmentsleam:style/TextAppearance.Material3.DisplaySmall = 0x7f100224
com.example.fragmentsleam:style/TextAppearance.Material3.DisplayMedium.Emphasized = 0x7f100223
com.example.fragmentsleam:color/m3_ref_palette_cyan95 = 0x7f0500c5
com.example.fragmentsleam:style/TextAppearance.Material3.DisplayMedium = 0x7f100222
com.example.fragmentsleam:style/TextAppearance.Material3.BodyMedium.Emphasized = 0x7f10021d
com.example.fragmentsleam:style/TextAppearance.Material3.BodyMedium = 0x7f10021c
com.example.fragmentsleam:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0c010d
com.example.fragmentsleam:style/TextAppearance.Material3.BodyLarge.Emphasized = 0x7f10021b
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.TitleSmall.Emphasized = 0x7f100217
com.example.fragmentsleam:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1002d0
com.example.fragmentsleam:dimen/material_helper_text_font_1_3_padding_top = 0x7f060323
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.TitleLarge.Emphasized = 0x7f100213
com.example.fragmentsleam:dimen/m3_comp_icon_button_xsmall_default_trailing_space = 0x7f06018f
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.LabelSmall.Emphasized = 0x7f100211
com.example.fragmentsleam:attr/liftOnScrollColor = 0x7f030303
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.LabelLarge.Emphasized = 0x7f10020d
com.example.fragmentsleam:style/Widget.AppCompat.DrawerArrowToggle = 0x7f100348
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0c00f6
com.example.fragmentsleam:dimen/m3_comp_icon_button_xlarge_icon_size = 0x7f060188
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f10020c
com.example.fragmentsleam:color/material_personalized_color_tertiary_container = 0x7f05034d
com.example.fragmentsleam:style/Widget.Material3.Button.IconButton.Filled = 0x7f10039f
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f10020a
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f10018c
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge.Emphasized = 0x7f100207
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f100204
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f100202
com.example.fragmentsleam:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.BodyLarge.Emphasized = 0x7f1001fb
com.example.fragmentsleam:style/TextAppearance.Design.Placeholder = 0x7f1001f5
com.example.fragmentsleam:dimen/abc_button_inset_vertical_material = 0x7f060013
com.example.fragmentsleam:style/TextAppearance.Design.Hint = 0x7f1001f4
com.example.fragmentsleam:attr/hideOnContentScroll = 0x7f03025a
com.example.fragmentsleam:style/TextAppearance.Compat.Notification.Time = 0x7f1001ed
com.example.fragmentsleam:color/m3_ref_palette_green40 = 0x7f050134
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1001e2
com.example.fragmentsleam:attr/textAppearanceBody1 = 0x7f03049f
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.Button = 0x7f1001e0
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1001dc
com.example.fragmentsleam:dimen/m3_comp_split_button_medium_trailing_button_trailing_space = 0x7f060226
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1001d8
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1001d7
com.example.fragmentsleam:dimen/hint_alpha_material_light = 0x7f060099
com.example.fragmentsleam:style/TextAppearance.AppCompat.Subhead = 0x7f1001d2
com.example.fragmentsleam:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1001ce
com.example.fragmentsleam:styleable/MaterialToolbar = 0x7f110063
com.example.fragmentsleam:attr/scrimBackground = 0x7f030406
com.example.fragmentsleam:attr/checkedIconMargin = 0x7f0300bc
com.example.fragmentsleam:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f100049
com.example.fragmentsleam:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1001ca
com.example.fragmentsleam:style/TextAppearance.Design.Prefix = 0x7f1001f6
com.example.fragmentsleam:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f10024f
com.example.fragmentsleam:style/TextAppearance.AppCompat.Large = 0x7f1001c5
com.example.fragmentsleam:style/TextAppearance.AppCompat.Headline = 0x7f1001c3
com.example.fragmentsleam:style/TextAppearance.AppCompat.Display4 = 0x7f1001c2
com.example.fragmentsleam:drawable/abc_switch_track_mtrl_alpha = 0x7f07006b
com.example.fragmentsleam:color/m3_hint_foreground = 0x7f05008f
com.example.fragmentsleam:style/ThemeOverlay.Material3.Search = 0x7f1002fd
com.example.fragmentsleam:animator/m3_btn_state_list_anim = 0x7f02000d
com.example.fragmentsleam:layout/abc_alert_dialog_material = 0x7f0b0009
com.example.fragmentsleam:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f1003ba
com.example.fragmentsleam:style/TextAppearance.AppCompat.Display3 = 0x7f1001c1
com.example.fragmentsleam:style/TextAppearance.AppCompat.Display2 = 0x7f1001c0
com.example.fragmentsleam:style/TextAppearance.AppCompat.Display1 = 0x7f1001bf
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1001b7
com.example.fragmentsleam:style/TextAppearance.AppCompat.Button = 0x7f1001bd
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1001b5
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f1001b2
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f1001b1
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f1001b0
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f1001aa
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f1001a8
com.example.fragmentsleam:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f1001a4
com.example.fragmentsleam:attr/textAppearanceHeadlineMedium = 0x7f0304b7
com.example.fragmentsleam:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f1001a3
com.example.fragmentsleam:style/ShapeAppearance.Material3.Tooltip = 0x7f1001a0
com.example.fragmentsleam:style/ShapeAppearance.Material3.SmallComponent = 0x7f10019f
com.example.fragmentsleam:dimen/tooltip_precise_anchor_extra_offset = 0x7f060402
com.example.fragmentsleam:id/bestChoice = 0x7f080062
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.Small = 0x7f10019b
com.example.fragmentsleam:attr/shrinkMotionSpec = 0x7f030434
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.LargeIncreased = 0x7f100198
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f100193
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f100190
com.example.fragmentsleam:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f0602ff
com.example.fragmentsleam:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070026
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.LargeIncreased = 0x7f10018e
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f10018d
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLargeIncreased = 0x7f10018a
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraExtraLarge = 0x7f100188
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f100184
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f100183
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f100182
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f100200
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f100181
com.example.fragmentsleam:dimen/m3_side_sheet_width = 0x7f0602b6
com.example.fragmentsleam:dimen/m3_chip_elevated_elevation = 0x7f0600fc
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Tooltip = 0x7f10024c
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.NavRail.Item.ActiveIndicator.Shape = 0x7f10017c
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Xsmall.Container.Shape.Round = 0x7f100176
com.example.fragmentsleam:style/ShapeAppearance.Material3.LargeComponent = 0x7f10019c
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Xlarge.Selected.Container.Shape.Square = 0x7f100175
com.example.fragmentsleam:dimen/material_helper_text_default_padding_top = 0x7f060321
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Small.Selected.Container.Shape.Square = 0x7f100173
com.example.fragmentsleam:drawable/mtrl_dropdown_arrow = 0x7f0700c2
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Medium.Container.Shape.Round = 0x7f100170
com.example.fragmentsleam:attr/titleTextStyle = 0x7f03051c
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Large.Selected.Container.Shape.Square = 0x7f10016f
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Large.Container.Shape.Round = 0x7f10016e
com.example.fragmentsleam:color/mtrl_tabs_icon_color_selector_colored = 0x7f050390
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Xsmall.Selected.Container.Shape.Square = 0x7f10016b
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Xsmall.Container.Shape.Round = 0x7f10016a
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Xlarge.Selected.Container.Shape.Square = 0x7f100169
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Medium.Selected.Container.Shape.Square = 0x7f100165
com.example.fragmentsleam:attr/goIcon = 0x7f030241
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Medium.Container.Shape.Round = 0x7f100164
com.example.fragmentsleam:styleable/AnimatedStateListDrawableTransition = 0x7f110009
com.example.fragmentsleam:attr/containerIconSize = 0x7f030146
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f10040a
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar = 0x7f1003fc
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Large.Selected.Container.Shape.Square = 0x7f100163
com.example.fragmentsleam:style/Widget.Design.NavigationView = 0x7f100382
com.example.fragmentsleam:attr/homeAsUpIndicator = 0x7f030261
com.example.fragmentsleam:attr/textureWidth = 0x7f0304ee
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f100160
com.example.fragmentsleam:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f10015e
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f10015a
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f100158
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f100157
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0c0078
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f100156
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f100155
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f100152
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f10014f
com.example.fragmentsleam:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f10014e
com.example.fragmentsleam:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f1003c0
com.example.fragmentsleam:style/Platform.Widget.AppCompat.Spinner = 0x7f10014d
com.example.fragmentsleam:macro/m3_comp_icon_button_xsmall_container_shape_square = 0x7f0c00cb
com.example.fragmentsleam:style/Platform.V25.AppCompat.Light = 0x7f10014c
com.example.fragmentsleam:styleable/Transform = 0x7f110097
com.example.fragmentsleam:color/material_dynamic_secondary95 = 0x7f0502ff
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1001e4
com.example.fragmentsleam:style/Platform.ThemeOverlay.AppCompat = 0x7f100146
com.example.fragmentsleam:style/Platform.MaterialComponents.Light.Dialog = 0x7f100145
com.example.fragmentsleam:style/Platform.MaterialComponents.Light = 0x7f100144
com.example.fragmentsleam:style/Platform.MaterialComponents.Dialog = 0x7f100143
com.example.fragmentsleam:id/material_textinput_timepicker = 0x7f08011b
com.example.fragmentsleam:style/Motion.Material3.Spring.Standard.Slow.Effects = 0x7f10013e
com.example.fragmentsleam:style/Motion.Material3.Spring.Standard.Fast.Spatial = 0x7f10013d
com.example.fragmentsleam:style/Motion.Material3.Spring.Standard.Default.Spatial = 0x7f10013b
com.example.fragmentsleam:style/Motion.Material3.Spring.Standard.Default.Effects = 0x7f10013a
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f100135
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f100131
com.example.fragmentsleam:style/Widget.Material3.Slider.Legacy = 0x7f100434
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500f0
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents = 0x7f100130
com.example.fragmentsleam:attr/behavior_halfExpandedRatio = 0x7f03006e
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f10012f
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Body.Text = 0x7f100128
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Animation = 0x7f100127
com.example.fragmentsleam:style/CardView.Light = 0x7f100125
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant99 = 0x7f05017b
com.example.fragmentsleam:color/m3_navigation_bar_ripple_color_selector = 0x7f050093
com.example.fragmentsleam:color/m3_timepicker_display_background_color = 0x7f0502ab
com.example.fragmentsleam:style/CardView = 0x7f100123
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f100120
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.Slider = 0x7f10011e
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f10011b
com.example.fragmentsleam:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f100113
com.example.fragmentsleam:style/Base.V21.Theme.MaterialComponents = 0x7f1000a4
com.example.fragmentsleam:style/Base.Widget.Material3.FloatingToolbar = 0x7f10010e
com.example.fragmentsleam:drawable/btn_radio_on_mtrl = 0x7f070080
com.example.fragmentsleam:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f10010d
com.example.fragmentsleam:style/Base.Widget.Material3.ExtendedFloatingActionButton.Small = 0x7f100109
com.example.fragmentsleam:string/mtrl_picker_invalid_format_example = 0x7f0f007c
com.example.fragmentsleam:style/Base.Widget.Material3.ExtendedFloatingActionButton.Large = 0x7f100107
com.example.fragmentsleam:attr/placeholderTextAppearance = 0x7f0303da
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_circular_active_indicator_wave_wavelength = 0x7f0601d8
com.example.fragmentsleam:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f100105
com.example.fragmentsleam:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f100102
com.example.fragmentsleam:attr/animateRelativeTo = 0x7f030034
com.example.fragmentsleam:style/Base.Widget.Material3.Chip = 0x7f1000ff
com.example.fragmentsleam:style/Base.Widget.Material3.BottomNavigationView = 0x7f1000fd
com.example.fragmentsleam:style/Base.Widget.AppCompat.Toolbar = 0x7f1000f8
com.example.fragmentsleam:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1000f7
com.example.fragmentsleam:string/searchview_navigation_content_description = 0x7f0f00aa
com.example.fragmentsleam:style/Base.Widget.AppCompat.TextView = 0x7f1000f6
com.example.fragmentsleam:attr/snackbarButtonStyle = 0x7f030440
com.example.fragmentsleam:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1000f5
com.example.fragmentsleam:style/Base.Widget.AppCompat.Spinner = 0x7f1000f4
com.example.fragmentsleam:style/Base.Widget.AppCompat.SeekBar = 0x7f1000f2
com.example.fragmentsleam:style/Widget.Design.BottomNavigationView = 0x7f10037e
com.example.fragmentsleam:attr/textAllCaps = 0x7f03049e
com.example.fragmentsleam:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0c0184
com.example.fragmentsleam:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1000f1
com.example.fragmentsleam:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1000ef
com.example.fragmentsleam:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1000ee
com.example.fragmentsleam:style/Base.Widget.AppCompat.PopupWindow = 0x7f1000ea
com.example.fragmentsleam:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1000e7
com.example.fragmentsleam:style/Base.Widget.AppCompat.ListMenuView = 0x7f1000e3
com.example.fragmentsleam:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1000e2
com.example.fragmentsleam:dimen/m3_comp_fab_primary_container_pressed_state_layer_opacity = 0x7f06015e
com.example.fragmentsleam:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1000e1
com.example.fragmentsleam:macro/m3_comp_button_small_pressed_container_shape = 0x7f0c0055
com.example.fragmentsleam:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1000de
com.example.fragmentsleam:style/Base.Widget.AppCompat.ImageButton = 0x7f1000da
com.example.fragmentsleam:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.example.fragmentsleam:style/Base.Widget.AppCompat.EditText = 0x7f1000d9
com.example.fragmentsleam:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1000d7
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1003e5
com.example.fragmentsleam:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1000d5
com.example.fragmentsleam:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1000d2
com.example.fragmentsleam:styleable/Fragment = 0x7f11003b
com.example.fragmentsleam:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f10035c
com.example.fragmentsleam:style/Base.Widget.AppCompat.Button.Small = 0x7f1000d0
com.example.fragmentsleam:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1000ce
com.example.fragmentsleam:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1000cd
com.example.fragmentsleam:style/Base.Widget.AppCompat.Button = 0x7f1000cb
com.example.fragmentsleam:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f100305
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionMode = 0x7f1000c8
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1000c7
com.example.fragmentsleam:color/primary_text_disabled_material_light = 0x7f0503a2
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionButton = 0x7f1000c5
com.example.fragmentsleam:dimen/m3_comp_slider_medium_active_track_height = 0x7f06020d
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1000c4
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar = 0x7f10034c
com.example.fragmentsleam:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1000bd
com.example.fragmentsleam:style/MaterialAlertDialog.Material3 = 0x7f100126
com.example.fragmentsleam:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1000b9
com.example.fragmentsleam:style/Base.V28.Theme.AppCompat.Light = 0x7f1000b7
com.example.fragmentsleam:attr/badgeGravity = 0x7f030053
com.example.fragmentsleam:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1000b2
com.example.fragmentsleam:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.example.fragmentsleam:style/Base.V24.Theme.Material3.Light = 0x7f1000b1
com.example.fragmentsleam:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1000b0
com.example.fragmentsleam:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1000aa
com.example.fragmentsleam:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1000a6
com.example.fragmentsleam:style/Widget.Material3.Button.TextButton.Icon = 0x7f1003a8
com.example.fragmentsleam:attr/motionSpringFastSpatial = 0x7f0303a1
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1001b9
com.example.fragmentsleam:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f02001f
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral0 = 0x7f0500c7
com.example.fragmentsleam:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1000a1
com.example.fragmentsleam:attr/topInsetScrimEnabled = 0x7f030526
com.example.fragmentsleam:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f10009f
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f10006b
com.example.fragmentsleam:id/always = 0x7f08004d
com.example.fragmentsleam:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f10009e
com.example.fragmentsleam:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f10009a
com.example.fragmentsleam:layout/abc_select_dialog_material = 0x7f0b001a
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f100098
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f100093
com.example.fragmentsleam:id/tag_on_receive_content_listener = 0x7f0801d3
com.example.fragmentsleam:style/Base.Widget.Material3.CardView = 0x7f1000fe
com.example.fragmentsleam:style/Base.V14.Theme.Material3.Light = 0x7f10008d
com.example.fragmentsleam:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f10008b
com.example.fragmentsleam:dimen/m3_chip_icon_size = 0x7f0600fe
com.example.fragmentsleam:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f10008a
com.example.fragmentsleam:style/Base.V14.Theme.Material3.Dark = 0x7f100089
com.example.fragmentsleam:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f100088
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1001d9
com.example.fragmentsleam:dimen/m3_appbar_size_large = 0x7f0600ac
com.example.fragmentsleam:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f100085
com.example.fragmentsleam:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f0601e7
com.example.fragmentsleam:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f100084
com.example.fragmentsleam:id/mtrl_child_content_container = 0x7f080136
com.example.fragmentsleam:style/Widget.Material3.SideSheet.Detached = 0x7f10042f
com.example.fragmentsleam:attr/hideNavigationIcon = 0x7f030259
com.example.fragmentsleam:style/Base.ThemeOverlay.Material3.Dialog = 0x7f100081
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.LabelMedium.Emphasized = 0x7f10020f
com.example.fragmentsleam:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f10007c
com.example.fragmentsleam:color/m3_ref_palette_error99 = 0x7f05012e
com.example.fragmentsleam:style/Base.Widget.Material3.FloatingActionButton = 0x7f10010a
com.example.fragmentsleam:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f10007a
com.example.fragmentsleam:attr/mock_showDiagonals = 0x7f030373
com.example.fragmentsleam:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f100079
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f100077
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f100074
com.example.fragmentsleam:attr/suffixText = 0x7f030475
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f100072
com.example.fragmentsleam:dimen/mtrl_fab_elevation = 0x7f06039a
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f100070
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light = 0x7f10006e
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f10006a
com.example.fragmentsleam:attr/submenuDividersEnabled = 0x7f03046d
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f100069
com.example.fragmentsleam:dimen/notification_top_pad = 0x7f0603fd
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Dialog = 0x7f100068
com.example.fragmentsleam:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f10005d
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f100067
com.example.fragmentsleam:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f0304e2
com.example.fragmentsleam:string/m3_ref_typeface_plain_medium = 0x7f0f003b
com.example.fragmentsleam:style/Widget.AppCompat.Spinner.Underlined = 0x7f100376
com.example.fragmentsleam:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f100063
com.example.fragmentsleam:id/search_button = 0x7f08019b
com.example.fragmentsleam:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f100060
com.example.fragmentsleam:attr/triggerReceiver = 0x7f030549
com.example.fragmentsleam:style/Widget.Material3.Button.Icon = 0x7f10039d
com.example.fragmentsleam:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f10005e
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0c0138
com.example.fragmentsleam:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f10005a
com.example.fragmentsleam:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0603c4
com.example.fragmentsleam:style/Base.Theme.FragmentSleam = 0x7f100058
com.example.fragmentsleam:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f100056
com.example.fragmentsleam:style/Base.Theme.AppCompat.Light.Dialog = 0x7f100053
com.example.fragmentsleam:style/Base.Theme.AppCompat.Light = 0x7f100051
com.example.fragmentsleam:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f10004f
com.example.fragmentsleam:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f10004e
com.example.fragmentsleam:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f100046
com.example.fragmentsleam:color/m3_dynamic_hint_foreground = 0x7f050084
com.example.fragmentsleam:style/Base.TextAppearance.MaterialComponents.Button = 0x7f100044
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f100041
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f100314
com.example.fragmentsleam:macro/m3_comp_button_xlarge_selected_container_shape_round = 0x7f0c0061
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f100040
com.example.fragmentsleam:id/mtrl_internal_children_alpha_tag = 0x7f080137
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f100039
com.example.fragmentsleam:attr/closeIconStartPadding = 0x7f0300e7
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f100036
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f100033
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f100032
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Title = 0x7f10002f
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f10002e
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Small = 0x7f10002b
com.example.fragmentsleam:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1000a5
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f100029
com.example.fragmentsleam:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1001c7
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Menu = 0x7f100027
com.example.fragmentsleam:attr/compatShadowEnabled = 0x7f03013b
com.example.fragmentsleam:dimen/material_clock_hand_stroke_width = 0x7f06030d
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f100026
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Medium = 0x7f100025
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f100022
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Large = 0x7f100021
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Inverse = 0x7f100020
com.example.fragmentsleam:attr/emojiCompatEnabled = 0x7f0301c0
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Display3 = 0x7f10001d
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Display2 = 0x7f10001c
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Caption = 0x7f10001a
com.example.fragmentsleam:dimen/m3_appbar_size_large_with_subtitle = 0x7f0600ad
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Body2 = 0x7f100018
com.example.fragmentsleam:style/Widget.Material3.BottomSheet = 0x7f100397
com.example.fragmentsleam:layout/material_clock_display_divider = 0x7f0b003b
com.example.fragmentsleam:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f100013
com.example.fragmentsleam:id/fixed = 0x7f0800c9
com.example.fragmentsleam:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1002c6
com.example.fragmentsleam:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1002f0
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f100129
com.example.fragmentsleam:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f100012
com.example.fragmentsleam:style/Base.Animation.AppCompat.Tooltip = 0x7f10000f
com.example.fragmentsleam:style/Theme.Design.Light = 0x7f100268
com.example.fragmentsleam:style/Base.Animation.AppCompat.DropDownUp = 0x7f10000e
com.example.fragmentsleam:style/Base.Animation.AppCompat.Dialog = 0x7f10000d
com.example.fragmentsleam:dimen/m3_comp_slider_stop_indicator_size = 0x7f060213
com.example.fragmentsleam:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f1002a7
com.example.fragmentsleam:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0c0154
com.example.fragmentsleam:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f10024d
com.example.fragmentsleam:style/Base.AlertDialog.AppCompat = 0x7f10000b
com.example.fragmentsleam:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f10000a
com.example.fragmentsleam:color/abc_search_url_text_normal = 0x7f05000e
com.example.fragmentsleam:style/Widget.Design.CollapsingToolbar = 0x7f100380
com.example.fragmentsleam:attr/fontStyle = 0x7f030238
com.example.fragmentsleam:style/Animation.AppCompat.DropDownUp = 0x7f100003
com.example.fragmentsleam:id/leftToRight = 0x7f0800ff
com.example.fragmentsleam:id/open_search_view_root = 0x7f080166
com.example.fragmentsleam:style/Animation.AppCompat.Dialog = 0x7f100002
com.example.fragmentsleam:layout/mtrl_picker_text_input_date_range = 0x7f0b0064
com.example.fragmentsleam:style/AlertDialog.AppCompat.Light = 0x7f100001
com.example.fragmentsleam:id/chronometer = 0x7f080078
com.example.fragmentsleam:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f100484
com.example.fragmentsleam:style/AlertDialog.AppCompat = 0x7f100000
com.example.fragmentsleam:string/side_sheet_behavior = 0x7f0f00ac
com.example.fragmentsleam:styleable/PropertySet = 0x7f110077
com.example.fragmentsleam:style/Widget.Material3.Chip.Input = 0x7f1003b5
com.example.fragmentsleam:string/searchview_clear_text_content_description = 0x7f0f00a9
com.example.fragmentsleam:string/searchbar_scrolling_view_behavior = 0x7f0f00a8
com.example.fragmentsleam:string/search_menu_title = 0x7f0f00a7
com.example.fragmentsleam:style/ThemeOverlay.AppCompat.Dark = 0x7f1002bc
com.example.fragmentsleam:string/path_password_eye_mask_strike_through = 0x7f0f00a4
com.example.fragmentsleam:string/path_password_eye = 0x7f0f00a3
com.example.fragmentsleam:layout/design_menu_item_action_area = 0x7f0b0024
com.example.fragmentsleam:string/password_toggle_content_description = 0x7f0f00a2
com.example.fragmentsleam:string/nav_rail_expanded_a11y_label = 0x7f0f00a1
com.example.fragmentsleam:string/mtrl_switch_track_decoration_path = 0x7f0f009c
com.example.fragmentsleam:id/fill = 0x7f0800c0
com.example.fragmentsleam:string/mtrl_switch_thumb_path_pressed = 0x7f0f009a
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.TitleMedium.Emphasized = 0x7f100215
com.example.fragmentsleam:string/mtrl_switch_thumb_path_name = 0x7f0f0099
com.example.fragmentsleam:attr/trackStopIndicatorPadding = 0x7f03053d
com.example.fragmentsleam:string/mtrl_switch_thumb_path_morphing = 0x7f0f0098
com.example.fragmentsleam:string/mtrl_switch_thumb_group_name = 0x7f0f0096
com.example.fragmentsleam:string/mtrl_picker_today_description = 0x7f0f008f
com.example.fragmentsleam:string/mtrl_picker_text_input_day_abbr = 0x7f0f008c
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500d5
com.example.fragmentsleam:attr/autoCompleteTextViewStyle = 0x7f03003e
com.example.fragmentsleam:string/mtrl_picker_text_input_date_range_start_hint = 0x7f0f008b
com.example.fragmentsleam:string/mtrl_picker_range_header_title = 0x7f0f0085
com.example.fragmentsleam:string/abc_menu_enter_shortcut_label = 0x7f0f000b
com.example.fragmentsleam:string/mtrl_picker_range_header_selected = 0x7f0f0084
com.example.fragmentsleam:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f1004a1
com.example.fragmentsleam:color/material_dynamic_color_light_error_container = 0x7f0502ca
com.example.fragmentsleam:string/mtrl_picker_range_header_only_end_selected = 0x7f0f0082
com.example.fragmentsleam:string/mtrl_picker_out_of_range = 0x7f0f0081
com.example.fragmentsleam:string/mtrl_picker_navigate_to_year_description = 0x7f0f0080
com.example.fragmentsleam:string/mtrl_picker_navigate_to_current_year_description = 0x7f0f007f
com.example.fragmentsleam:color/m3_ref_palette_orange10 = 0x7f05017d
com.example.fragmentsleam:color/material_timepicker_clockface = 0x7f050360
com.example.fragmentsleam:string/mtrl_picker_invalid_format_use = 0x7f0f007d
com.example.fragmentsleam:string/mtrl_picker_date_header_unselected = 0x7f0f0078
com.example.fragmentsleam:attr/floatingActionButtonLargeTertiaryStyle = 0x7f03020e
com.example.fragmentsleam:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f100080
com.example.fragmentsleam:string/mtrl_picker_date_header_title = 0x7f0f0077
com.example.fragmentsleam:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0c018b
com.example.fragmentsleam:string/mtrl_picker_date_header_selected = 0x7f0f0076
com.example.fragmentsleam:string/mtrl_picker_confirm = 0x7f0f0075
com.example.fragmentsleam:string/mtrl_picker_announce_current_selection = 0x7f0f0072
com.example.fragmentsleam:style/Base.V21.Theme.AppCompat = 0x7f1000a0
com.example.fragmentsleam:color/design_dark_default_color_on_secondary = 0x7f050037
com.example.fragmentsleam:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f100087
com.example.fragmentsleam:string/mtrl_picker_a11y_next_month = 0x7f0f006f
com.example.fragmentsleam:attr/materialCalendarHeaderCancelButton = 0x7f030331
com.example.fragmentsleam:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f060240
com.example.fragmentsleam:id/chains = 0x7f080075
com.example.fragmentsleam:string/mtrl_exceed_max_badge_number_suffix = 0x7f0f006e
com.example.fragmentsleam:string/mtrl_checkbox_state_description_checked = 0x7f0f0069
com.example.fragmentsleam:string/mtrl_checkbox_button_path_unchecked = 0x7f0f0068
com.example.fragmentsleam:attr/warmth = 0x7f03055a
com.example.fragmentsleam:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f10047d
com.example.fragmentsleam:color/m3_ref_palette_yellow100 = 0x7f0501dd
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070017
com.example.fragmentsleam:macro/m3_comp_badge_color = 0x7f0c0010
com.example.fragmentsleam:string/mtrl_checkbox_button_path_group_name = 0x7f0f0066
com.example.fragmentsleam:string/mtrl_checkbox_button_path_checked = 0x7f0f0065
com.example.fragmentsleam:string/mtrl_switch_thumb_path_checked = 0x7f0f0097
com.example.fragmentsleam:attr/fontProviderFetchTimeout = 0x7f030234
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f05022b
com.example.fragmentsleam:style/Widget.Material3.ActionBar.Solid = 0x7f100388
com.example.fragmentsleam:string/mtrl_checkbox_button_icon_path_name = 0x7f0f0064
com.example.fragmentsleam:styleable/MotionScene = 0x7f11006d
com.example.fragmentsleam:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1001c9
com.example.fragmentsleam:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f0f0063
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0c0164
com.example.fragmentsleam:integer/material_motion_duration_medium_1 = 0x7f09002b
com.example.fragmentsleam:string/mtrl_checkbox_button_icon_path_checked = 0x7f0f0061
com.example.fragmentsleam:string/mtrl_button_expanded_content_description = 0x7f0f0060
com.example.fragmentsleam:string/material_timepicker_am = 0x7f0f0057
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0c00f4
com.example.fragmentsleam:string/material_slider_range_end = 0x7f0f0054
com.example.fragmentsleam:attr/state_dragged = 0x7f03045c
com.example.fragmentsleam:color/design_fab_shadow_start_color = 0x7f05004f
com.example.fragmentsleam:string/material_motion_easing_standard = 0x7f0f0053
com.example.fragmentsleam:attr/motionEffect_alpha = 0x7f030392
com.example.fragmentsleam:string/material_motion_easing_linear = 0x7f0f0052
com.example.fragmentsleam:layout/mtrl_calendar_day_of_week = 0x7f0b004f
com.example.fragmentsleam:string/material_motion_easing_emphasized = 0x7f0f0051
com.example.fragmentsleam:string/material_motion_easing_decelerated = 0x7f0f0050
com.example.fragmentsleam:layout/mtrl_auto_complete_simple_item = 0x7f0b004d
com.example.fragmentsleam:string/material_motion_easing_accelerated = 0x7f0f004f
com.example.fragmentsleam:string/material_hour_suffix = 0x7f0f004c
com.example.fragmentsleam:string/material_hour_selection = 0x7f0f004b
com.example.fragmentsleam:id/accessibility_custom_action_2 = 0x7f08001d
com.example.fragmentsleam:string/material_hour_24h_suffix = 0x7f0f004a
com.example.fragmentsleam:string/material_clock_toggle_content_description = 0x7f0f0049
com.example.fragmentsleam:string/material_clock_display_divider = 0x7f0f0048
com.example.fragmentsleam:string/m3_sys_motion_easing_linear = 0x7f0f0044
com.example.fragmentsleam:string/m3_sys_motion_easing_legacy_decelerate = 0x7f0f0043
com.example.fragmentsleam:string/m3_sys_motion_easing_legacy_accelerate = 0x7f0f0042
com.example.fragmentsleam:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f100448
com.example.fragmentsleam:attr/startInsetScrimEnabled = 0x7f030457
com.example.fragmentsleam:string/m3_sys_motion_easing_legacy = 0x7f0f0041
com.example.fragmentsleam:string/m3_sys_motion_easing_emphasized_path_data = 0x7f0f0040
com.example.fragmentsleam:string/path_password_strike_through = 0x7f0f00a6
com.example.fragmentsleam:drawable/s3 = 0x7f0700ec
com.example.fragmentsleam:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f0f003f
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f10003c
com.example.fragmentsleam:string/m3_ref_typeface_plain_regular = 0x7f0f003c
com.example.fragmentsleam:id/accessibility_custom_action_0 = 0x7f080011
com.example.fragmentsleam:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.example.fragmentsleam:string/m3_ref_typeface_brand_regular = 0x7f0f003a
com.example.fragmentsleam:string/m3_loading_indicator_content_description = 0x7f0f0038
com.example.fragmentsleam:color/material_dynamic_neutral_variant60 = 0x7f0502e1
com.example.fragmentsleam:string/m3_exceed_max_badge_text_suffix = 0x7f0f0037
com.example.fragmentsleam:string/item_view_role_description = 0x7f0f0036
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f1001ab
com.example.fragmentsleam:id/right_icon = 0x7f08018b
com.example.fragmentsleam:string/fab_transformation_sheet_behavior = 0x7f0f0032
com.example.fragmentsleam:string/fab_transformation_scrim_behavior = 0x7f0f0031
com.example.fragmentsleam:attr/tintNavigationIcon = 0x7f03050c
com.example.fragmentsleam:string/character_counter_content_description = 0x7f0f002a
com.example.fragmentsleam:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f100272
com.example.fragmentsleam:id/item_role = 0x7f0800f8
com.example.fragmentsleam:string/call_notification_screening_text = 0x7f0f0029
com.example.fragmentsleam:string/call_notification_ongoing_text = 0x7f0f0028
com.example.fragmentsleam:color/material_personalized_color_surface_dim = 0x7f050349
com.example.fragmentsleam:string/call_notification_decline_action = 0x7f0f0025
com.example.fragmentsleam:string/bottomsheet_drag_handle_content_description = 0x7f0f0022
com.example.fragmentsleam:dimen/abc_control_corner_material = 0x7f060018
com.example.fragmentsleam:string/bottomsheet_action_expand = 0x7f0f0020
com.example.fragmentsleam:string/bottomsheet_action_collapse = 0x7f0f001f
com.example.fragmentsleam:string/androidx_startup = 0x7f0f001b
com.example.fragmentsleam:color/mtrl_choice_chip_background_color = 0x7f050372
com.example.fragmentsleam:string/abc_toolbar_collapse_description = 0x7f0f001a
com.example.fragmentsleam:string/abc_searchview_description_submit = 0x7f0f0016
com.example.fragmentsleam:string/abc_search_hint = 0x7f0f0012
com.example.fragmentsleam:string/abc_menu_sym_shortcut_label = 0x7f0f0010
com.example.fragmentsleam:string/abc_menu_function_shortcut_label = 0x7f0f000c
com.example.fragmentsleam:string/abc_action_menu_overflow_description = 0x7f0f0002
com.example.fragmentsleam:string/abc_action_bar_home_description = 0x7f0f0000
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_vibrant_selected_icon_color = 0x7f0c0205
com.example.fragmentsleam:dimen/design_bottom_sheet_modal_elevation = 0x7f06006e
com.example.fragmentsleam:attr/shapeAppearanceCornerExtraExtraLarge = 0x7f030414
com.example.fragmentsleam:attr/tabStyle = 0x7f030496
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_vibrant_label_text_color = 0x7f0c0203
com.example.fragmentsleam:color/m3_radiobutton_button_tint = 0x7f05009d
com.example.fragmentsleam:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f1003bf
com.example.fragmentsleam:dimen/m3_comp_fab_large_icon_size = 0x7f060155
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_vibrant_button_container_color = 0x7f0c0200
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_pressed_state_layer_color = 0x7f0c01ff
com.example.fragmentsleam:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f0601e4
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_pressed_label_text_color = 0x7f0c01fe
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_focused_state_layer_color = 0x7f0c01f9
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_focused_label_text_color = 0x7f0c01f8
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_focused_icon_color = 0x7f0c01f7
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_pressed_icon_color = 0x7f0c01f4
com.example.fragmentsleam:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1002f8
com.example.fragmentsleam:id/autoComplete = 0x7f080056
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_hovered_icon_color = 0x7f0c01f1
com.example.fragmentsleam:attr/reactiveGuide_applyToAllConstraintSets = 0x7f0303f5
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_focused_state_layer_color = 0x7f0c01f0
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f100154
com.example.fragmentsleam:attr/horizontalItemTextAppearanceInactive = 0x7f030265
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_focused_icon_color = 0x7f0c01ee
com.example.fragmentsleam:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1002f2
com.example.fragmentsleam:id/disjoint = 0x7f0800a3
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_disabled_label_text_color = 0x7f0c01ed
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_disabled_icon_color = 0x7f0c01ec
com.example.fragmentsleam:attr/materialIconButtonOutlinedStyle = 0x7f030348
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_pressed_label_text_color = 0x7f0c01ea
com.example.fragmentsleam:style/CardView.Dark = 0x7f100124
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_pressed_icon_color = 0x7f0c01e9
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_label_text_color = 0x7f0c01e8
com.example.fragmentsleam:macro/m3_comp_button_group_connected_small_pressed_inner_corner_corner_size = 0x7f0c0021
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_icon_color = 0x7f0c01e7
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_hovered_state_layer_color = 0x7f0c01e6
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_focused_state_layer_color = 0x7f0c01e3
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_pressed_icon_color = 0x7f0c01dd
com.example.fragmentsleam:macro/m3_comp_button_outlined_icon_color = 0x7f0c0033
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_icon_color = 0x7f0c01db
com.example.fragmentsleam:id/text2 = 0x7f0801dc
com.example.fragmentsleam:dimen/disabled_alpha_material_dark = 0x7f060090
com.example.fragmentsleam:id/packed = 0x7f080170
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_disabled_icon_color = 0x7f0c01d3
com.example.fragmentsleam:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f10035d
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_button_container_color = 0x7f0c01d1
com.example.fragmentsleam:style/Widget.Material3.Toolbar = 0x7f10044c
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f1001ac
com.example.fragmentsleam:color/m3_ref_palette_blue_variant20 = 0x7f0500b0
com.example.fragmentsleam:attr/motionEasingEmphasizedInterpolator = 0x7f03038b
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0c01d0
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0c01ce
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0c01cd
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0c01cc
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0c01c7
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0c01c6
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0c01c4
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0c01c3
com.example.fragmentsleam:attr/motionDurationMedium4 = 0x7f030381
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0c01c2
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0c01c0
com.example.fragmentsleam:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070074
com.example.fragmentsleam:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1002d1
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0c01bf
com.example.fragmentsleam:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070060
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0c01bd
com.example.fragmentsleam:macro/m3_comp_time_picker_headline_type = 0x7f0c01b7
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f100489
com.example.fragmentsleam:dimen/m3_comp_extended_fab_primary_container_focused_container_elevation = 0x7f060147
com.example.fragmentsleam:style/Widget.Material3.BottomAppBar = 0x7f100391
com.example.fragmentsleam:attr/materialCalendarFullscreenTheme = 0x7f030330
com.example.fragmentsleam:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0c01b1
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_pressed_icon_color = 0x7f0c004f
com.example.fragmentsleam:macro/m3_comp_switch_unselected_track_color = 0x7f0c01ab
com.example.fragmentsleam:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0c01aa
com.example.fragmentsleam:dimen/abc_action_bar_elevation_material = 0x7f060005
com.example.fragmentsleam:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0c01a7
com.example.fragmentsleam:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0c01a6
com.example.fragmentsleam:macro/m3_comp_switch_unselected_icon_color = 0x7f0c01a5
com.example.fragmentsleam:style/Motion.Material3.Spring.Standard.Fast.Effects = 0x7f10013c
com.example.fragmentsleam:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0c01a1
com.example.fragmentsleam:macro/m3_comp_switch_unselected_handle_color = 0x7f0c019f
com.example.fragmentsleam:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0c019e
com.example.fragmentsleam:color/m3_dynamic_primary_text_disable_only = 0x7f050085
com.example.fragmentsleam:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0c019d
com.example.fragmentsleam:attr/suffixTextAppearance = 0x7f030476
com.example.fragmentsleam:attr/tint = 0x7f03050a
com.example.fragmentsleam:macro/m3_comp_switch_selected_track_color = 0x7f0c0199
com.example.fragmentsleam:attr/floatingActionButtonTertiaryStyle = 0x7f030219
com.example.fragmentsleam:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0c0198
com.example.fragmentsleam:macro/m3_comp_switch_selected_hover_track_color = 0x7f0c0193
com.example.fragmentsleam:integer/mtrl_switch_thumb_motion_duration = 0x7f090039
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.DisplaySmall.Emphasized = 0x7f100205
com.example.fragmentsleam:attr/titleCentered = 0x7f03050e
com.example.fragmentsleam:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0c0190
com.example.fragmentsleam:style/Base.Widget.AppCompat.SearchView = 0x7f1000f0
com.example.fragmentsleam:macro/m3_comp_switch_selected_focus_track_color = 0x7f0c018e
com.example.fragmentsleam:style/Widget.MaterialComponents.PopupMenu = 0x7f1004a5
com.example.fragmentsleam:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0c018a
com.example.fragmentsleam:drawable/abc_action_bar_item_background_material = 0x7f07002a
com.example.fragmentsleam:attr/region_widthLessThan = 0x7f0303fb
com.example.fragmentsleam:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0c0187
com.example.fragmentsleam:attr/content = 0x7f030154
com.example.fragmentsleam:integer/m3_sys_motion_duration_extra_long2 = 0x7f090010
com.example.fragmentsleam:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0c0186
com.example.fragmentsleam:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0c0183
com.example.fragmentsleam:attr/boxStrokeColor = 0x7f030087
com.example.fragmentsleam:dimen/mtrl_textinput_box_stroke_width_default = 0x7f0603e3
com.example.fragmentsleam:macro/m3_comp_snackbar_container_color = 0x7f0c017e
com.example.fragmentsleam:macro/m3_comp_slider_value_indicator_label_label_text_color = 0x7f0c017d
com.example.fragmentsleam:string/material_timepicker_clock_mode_description = 0x7f0f0058
com.example.fragmentsleam:macro/m3_comp_slider_value_indicator_container_color = 0x7f0c017c
com.example.fragmentsleam:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f10038b
com.example.fragmentsleam:dimen/mtrl_chip_pressed_translation_z = 0x7f060385
com.example.fragmentsleam:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0c0178
com.example.fragmentsleam:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f10037a
com.example.fragmentsleam:dimen/abc_dialog_min_width_minor = 0x7f060023
com.example.fragmentsleam:macro/m3_comp_slider_disabled_inactive_stop_indicator_container_color = 0x7f0c0177
com.example.fragmentsleam:macro/m3_comp_slider_disabled_active_track_color = 0x7f0c0175
com.example.fragmentsleam:macro/m3_comp_slider_active_track_color = 0x7f0c0173
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.BodySmall.Emphasized = 0x7f1001ff
com.example.fragmentsleam:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0c0171
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500cf
com.example.fragmentsleam:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0c0170
com.example.fragmentsleam:drawable/design_ic_visibility = 0x7f070083
com.example.fragmentsleam:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0c013e
com.example.fragmentsleam:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0c016e
com.example.fragmentsleam:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1001c8
com.example.fragmentsleam:id/top_center = 0x7f0801f3
com.example.fragmentsleam:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0c016b
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c016a
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0c0166
com.example.fragmentsleam:string/character_counter_pattern = 0x7f0f002c
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0c0165
com.example.fragmentsleam:attr/grid_rows = 0x7f030247
com.example.fragmentsleam:string/clear_text_end_icon_content_description = 0x7f0f002d
com.example.fragmentsleam:style/Base.V23.Theme.AppCompat = 0x7f1000ad
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0c0163
com.example.fragmentsleam:attr/motionEffect_translationX = 0x7f030397
com.example.fragmentsleam:id/continuousVelocity = 0x7f080087
com.example.fragmentsleam:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0c0160
com.example.fragmentsleam:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f1004a8
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Body1 = 0x7f100017
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f100294
com.example.fragmentsleam:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0c015e
com.example.fragmentsleam:macro/m3_comp_search_view_header_input_text_type = 0x7f0c015c
com.example.fragmentsleam:macro/m3_comp_search_view_header_input_text_color = 0x7f0c015b
com.example.fragmentsleam:macro/m3_comp_search_view_docked_container_shape = 0x7f0c015a
com.example.fragmentsleam:macro/m3_comp_search_view_divider_color = 0x7f0c0159
com.example.fragmentsleam:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0c0157
com.example.fragmentsleam:id/tag_accessibility_actions = 0x7f0801ce
com.example.fragmentsleam:styleable/BottomNavigationView = 0x7f110016
com.example.fragmentsleam:color/m3_tabs_icon_color_secondary = 0x7f05029a
com.example.fragmentsleam:attr/hintTextAppearance = 0x7f03025f
com.example.fragmentsleam:macro/m3_comp_search_bar_input_text_type = 0x7f0c0151
com.example.fragmentsleam:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0c014f
com.example.fragmentsleam:layout/abc_action_menu_item_layout = 0x7f0b0002
com.example.fragmentsleam:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0c014e
com.example.fragmentsleam:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0c014b
com.example.fragmentsleam:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0c014a
com.example.fragmentsleam:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0c0147
com.example.fragmentsleam:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0c0142
com.example.fragmentsleam:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0c0141
com.example.fragmentsleam:attr/endIconMode = 0x7f0301c6
com.example.fragmentsleam:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0c0140
com.example.fragmentsleam:attr/hintTextColor = 0x7f030260
com.example.fragmentsleam:dimen/m3_comp_loading_indicator_container_height = 0x7f06019b
com.example.fragmentsleam:macro/m3_comp_progress_indicator_track_color = 0x7f0c013c
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0c0134
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0c0132
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f10049b
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0c0130
com.example.fragmentsleam:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070047
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0c012f
com.example.fragmentsleam:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060161
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_outline_color = 0x7f0c012a
com.example.fragmentsleam:id/scale = 0x7f080192
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0c0128
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0c0126
com.example.fragmentsleam:color/m3_tabs_ripple_color_secondary = 0x7f05029c
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0c0125
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0c0123
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0c0122
com.example.fragmentsleam:style/TextAppearance.Material3.LabelSmall.Emphasized = 0x7f100231
com.example.fragmentsleam:color/m3_sys_color_light_primary = 0x7f05027e
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0c0121
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0c011f
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0c011d
com.example.fragmentsleam:attr/switchStyle = 0x7f03047b
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0c011c
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_container_shape = 0x7f0c0118
com.example.fragmentsleam:attr/fastScrollHorizontalThumbDrawable = 0x7f030205
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_caret_color = 0x7f0c0117
com.example.fragmentsleam:style/Base.V23.Theme.AppCompat.Light = 0x7f1000ae
com.example.fragmentsleam:attr/color = 0x7f0300fa
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500dd
com.example.fragmentsleam:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0c0116
com.example.fragmentsleam:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f100062
com.example.fragmentsleam:dimen/mtrl_calendar_year_corner = 0x7f06037a
com.example.fragmentsleam:macro/m3_comp_outlined_card_outline_color = 0x7f0c0115
com.example.fragmentsleam:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0c0114
com.example.fragmentsleam:macro/m3_comp_outlined_card_container_shape = 0x7f0c0110
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f100400
com.example.fragmentsleam:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0c010e
com.example.fragmentsleam:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0c010c
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0c010a
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_default_effects_stiffness = 0x7f0602ea
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0c0109
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0c0107
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0c0105
com.example.fragmentsleam:dimen/m3_comp_nav_rail_collapsed_top_space = 0x7f0601aa
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0c0103
com.example.fragmentsleam:color/m3_ref_palette_grey50 = 0x7f050142
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f100312
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0c0102
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0c0101
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0c00ff
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_headline_type = 0x7f0c00fe
com.example.fragmentsleam:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0c013f
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0c00fc
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0c00fb
com.example.fragmentsleam:attr/floatingActionButtonLargeSurfaceStyle = 0x7f03020d
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0c00fa
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0c00f8
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0c00f3
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0c00f2
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0c00f1
com.example.fragmentsleam:style/Base.V28.Theme.AppCompat = 0x7f1000b6
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_inactive_pressed_state_layer_color = 0x7f0c00ef
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight = 0x7f10028f
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_active_pressed_state_layer_color = 0x7f0c00e9
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_active_icon_color = 0x7f0c00e6
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_active_hovered_state_layer_color = 0x7f0c00e5
com.example.fragmentsleam:style/TextAppearance.Design.Error = 0x7f1001f2
com.example.fragmentsleam:color/m3_sys_color_dark_on_error_container = 0x7f050201
com.example.fragmentsleam:dimen/mtrl_navigation_item_icon_size = 0x7f0603ac
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_vertical_label_text_font = 0x7f0c00e2
com.example.fragmentsleam:style/TextAppearance.Design.Suffix = 0x7f1001f8
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_inactive_pressed_state_layer_color = 0x7f0c00e1
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_inactive_label_text_color = 0x7f0c00e0
com.example.fragmentsleam:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f060373
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_inactive_icon_color = 0x7f0c00df
com.example.fragmentsleam:color/m3_ref_palette_yellow40 = 0x7f0501e0
com.example.fragmentsleam:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1000a9
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_inactive_focused_state_layer_color = 0x7f0c00dd
com.example.fragmentsleam:animator/fragment_open_exit = 0x7f02000a
com.example.fragmentsleam:layout/mtrl_picker_header_toggle = 0x7f0b0062
com.example.fragmentsleam:string/nav_rail_collapsed_a11y_label = 0x7f0f00a0
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_horizontal_label_text_font = 0x7f0c00dc
com.example.fragmentsleam:string/icon_content_description = 0x7f0f0035
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_active_label_text_color = 0x7f0c00da
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_active_icon_color = 0x7f0c00d8
com.example.fragmentsleam:color/m3_ref_palette_green90 = 0x7f050139
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_active_hovered_state_layer_color = 0x7f0c00d7
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_active_focused_state_layer_color = 0x7f0c00d6
com.example.fragmentsleam:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.example.fragmentsleam:macro/m3_comp_menu_container_color = 0x7f0c00d4
com.example.fragmentsleam:macro/m3_comp_loading_indicator_contained_container_color = 0x7f0c00d3
com.example.fragmentsleam:macro/m3_comp_loading_indicator_contained_active_indicator_color = 0x7f0c00d2
com.example.fragmentsleam:styleable/ViewTransition = 0x7f11009e
com.example.fragmentsleam:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f10045f
com.example.fragmentsleam:macro/m3_comp_input_chip_label_text_type = 0x7f0c00cf
com.example.fragmentsleam:dimen/m3_small_fab_size = 0x7f0602bc
com.example.fragmentsleam:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1002db
com.example.fragmentsleam:macro/m3_comp_icon_button_xlarge_pressed_container_shape = 0x7f0c00c9
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f100150
com.example.fragmentsleam:macro/m3_comp_icon_button_xlarge_container_shape_square = 0x7f0c00c8
com.example.fragmentsleam:id/alertTitle = 0x7f080049
com.example.fragmentsleam:macro/m3_comp_icon_button_standard_selected_icon_color = 0x7f0c00c4
com.example.fragmentsleam:macro/m3_comp_icon_button_small_container_shape_square = 0x7f0c00c0
com.example.fragmentsleam:macro/m3_comp_icon_button_large_selected_container_shape_round = 0x7f0c00bc
com.example.fragmentsleam:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f10005c
com.example.fragmentsleam:macro/m3_comp_icon_button_large_pressed_container_shape = 0x7f0c00bb
com.example.fragmentsleam:macro/m3_comp_icon_button_filled_container_color = 0x7f0c00b8
com.example.fragmentsleam:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0c00b5
com.example.fragmentsleam:anim/m3_side_sheet_exit_to_left = 0x7f010027
com.example.fragmentsleam:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0c00b3
com.example.fragmentsleam:color/material_dynamic_secondary60 = 0x7f0502fb
com.example.fragmentsleam:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0c00b1
com.example.fragmentsleam:macro/m3_comp_filled_text_field_container_shape = 0x7f0c00b0
com.example.fragmentsleam:macro/m3_comp_filled_text_field_container_color = 0x7f0c00af
com.example.fragmentsleam:macro/m3_comp_extended_fab_small_label_text = 0x7f0c0094
com.example.fragmentsleam:macro/m3_comp_filled_card_container_shape = 0x7f0c00ae
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Small.Selected.Container.Shape.Square = 0x7f100167
com.example.fragmentsleam:macro/m3_comp_filled_card_container_color = 0x7f0c00ad
com.example.fragmentsleam:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0c00ac
com.example.fragmentsleam:drawable/abc_list_divider_mtrl_alpha = 0x7f07004e
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_fast_spatial_stiffness = 0x7f0602f0
com.example.fragmentsleam:attr/expandedActiveIndicatorPaddingBottom = 0x7f0301da
com.example.fragmentsleam:macro/m3_comp_fab_tertiary_icon_color = 0x7f0c00aa
com.example.fragmentsleam:attr/divider = 0x7f03019a
com.example.fragmentsleam:attr/colorPrimaryInverse = 0x7f030123
com.example.fragmentsleam:macro/m3_comp_fab_tertiary_container_icon_color = 0x7f0c00a9
com.example.fragmentsleam:macro/m3_comp_fab_tertiary_container_container_color = 0x7f0c00a8
com.example.fragmentsleam:macro/m3_comp_fab_small_container_shape = 0x7f0c00a4
com.example.fragmentsleam:attr/tabMaxWidth = 0x7f03048a
com.example.fragmentsleam:attr/waveOffset = 0x7f03055d
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f100327
com.example.fragmentsleam:macro/m3_comp_fab_secondary_container_color = 0x7f0c00a0
com.example.fragmentsleam:attr/colorPrimaryContainer = 0x7f03011f
com.example.fragmentsleam:color/m3_ref_palette_neutral90 = 0x7f050167
com.example.fragmentsleam:style/TextAppearance.Material3.HeadlineSmall.Emphasized = 0x7f10022b
com.example.fragmentsleam:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020013
com.example.fragmentsleam:string/app_name = 0x7f0f001c
com.example.fragmentsleam:styleable/OnSwipe = 0x7f110074
com.example.fragmentsleam:macro/m3_comp_fab_primary_icon_color = 0x7f0c009f
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary99 = 0x7f050104
com.example.fragmentsleam:macro/m3_comp_fab_primary_container_container_color = 0x7f0c009d
com.example.fragmentsleam:integer/mtrl_switch_thumb_viewport_size = 0x7f09003e
com.example.fragmentsleam:macro/m3_comp_fab_container_shape = 0x7f0c0099
com.example.fragmentsleam:drawable/abc_ic_menu_overflow_material = 0x7f070045
com.example.fragmentsleam:macro/m3_comp_extended_fab_tertiary_container_icon_color = 0x7f0c0098
com.example.fragmentsleam:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0c0096
com.example.fragmentsleam:macro/m3_comp_extended_fab_secondary_container_icon_color = 0x7f0c0092
com.example.fragmentsleam:macro/m3_comp_extended_fab_primary_container_container_color = 0x7f0c008f
com.example.fragmentsleam:macro/m3_comp_extended_fab_medium_label_text = 0x7f0c008e
com.example.fragmentsleam:attr/tickColorInactive = 0x7f030502
com.example.fragmentsleam:macro/m3_comp_extended_fab_medium_container_shape = 0x7f0c008d
com.example.fragmentsleam:macro/m3_comp_elevated_card_container_color = 0x7f0c0089
com.example.fragmentsleam:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f0f003e
com.example.fragmentsleam:id/right_side = 0x7f08018c
com.example.fragmentsleam:style/Base.V7.Theme.AppCompat = 0x7f1000b8
com.example.fragmentsleam:macro/m3_comp_divider_color = 0x7f0c0088
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Overline = 0x7f100248
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f1002b1
com.example.fragmentsleam:macro/m3_comp_dialog_supporting_text_type = 0x7f0c0087
com.example.fragmentsleam:attr/headerLayout = 0x7f030250
com.example.fragmentsleam:macro/m3_comp_dialog_headline_type = 0x7f0c0085
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0c0081
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1003da
com.example.fragmentsleam:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c7
com.example.fragmentsleam:string/mtrl_picker_range_header_only_start_selected = 0x7f0f0083
com.example.fragmentsleam:styleable/FontFamilyFont = 0x7f110039
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0c007c
com.example.fragmentsleam:attr/viewTransitionOnCross = 0x7f030555
com.example.fragmentsleam:string/abc_searchview_description_voice = 0x7f0f0017
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0c0079
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary60 = 0x7f05011a
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0c0076
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0c0075
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0c0072
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0c0071
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0c006f
com.example.fragmentsleam:layout/mtrl_picker_header_title_text = 0x7f0b0061
com.example.fragmentsleam:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1000b5
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_container_color = 0x7f0c006d
com.example.fragmentsleam:macro/m3_comp_checkbox_selected_icon_color = 0x7f0c006b
com.example.fragmentsleam:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0c0068
com.example.fragmentsleam:macro/m3_comp_button_xsmall_pressed_container_shape = 0x7f0c0064
com.example.fragmentsleam:dimen/material_clock_hand_center_dot_radius = 0x7f06030b
com.example.fragmentsleam:macro/m3_comp_button_tonal_unselected_icon_color = 0x7f0c005d
com.example.fragmentsleam:macro/m3_comp_button_tonal_unselected_container_color = 0x7f0c005c
com.example.fragmentsleam:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f100450
com.example.fragmentsleam:macro/m3_comp_button_tonal_selected_icon_color = 0x7f0c005b
com.example.fragmentsleam:macro/m3_comp_button_tonal_container_color = 0x7f0c0058
com.example.fragmentsleam:macro/m3_comp_button_text_icon_color = 0x7f0c0057
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Headline4 = 0x7f100245
com.example.fragmentsleam:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070046
com.example.fragmentsleam:macro/m3_comp_button_small_selected_container_shape_round = 0x7f0c0056
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f100195
com.example.fragmentsleam:string/m3_sys_motion_easing_standard_accelerate = 0x7f0f0046
com.example.fragmentsleam:macro/m3_comp_button_small_container_shape_square = 0x7f0c0053
com.example.fragmentsleam:anim/design_bottom_sheet_slide_in = 0x7f010018
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_icon_color = 0x7f0c004e
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary20 = 0x7f0500fa
com.example.fragmentsleam:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f050264
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_hovered_state_layer_color = 0x7f0c004d
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_hovered_label_text_color = 0x7f0c004b
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1001b3
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_focused_outline_color = 0x7f0c0048
com.example.fragmentsleam:id/transition_transform = 0x7f0801fe
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_focused_label_text_color = 0x7f0c0047
com.example.fragmentsleam:styleable/CollapsingToolbarLayout_Layout = 0x7f110023
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_pressed_state_layer_color = 0x7f0c0044
com.example.fragmentsleam:dimen/design_bottom_navigation_elevation = 0x7f060064
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_hovered_state_layer_color = 0x7f0c0040
com.example.fragmentsleam:string/mtrl_picker_invalid_format = 0x7f0f007b
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_focused_label_text_color = 0x7f0c003c
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_disabled_container_color = 0x7f0c003a
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_container_color = 0x7f0c0039
com.example.fragmentsleam:macro/m3_comp_button_outlined_pressed_outline_color = 0x7f0c0037
com.example.fragmentsleam:macro/m3_comp_button_outlined_pressed_icon_color = 0x7f0c0035
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Xlarge.Container.Shape.Round = 0x7f100174
com.example.fragmentsleam:macro/m3_comp_button_outlined_outline_color = 0x7f0c0034
com.example.fragmentsleam:macro/m3_comp_outlined_card_container_color = 0x7f0c010f
com.example.fragmentsleam:macro/m3_comp_button_outlined_hovered_outline_color = 0x7f0c0031
com.example.fragmentsleam:macro/m3_comp_button_outlined_focused_label_text_color = 0x7f0c002c
com.example.fragmentsleam:attr/maxImageSize = 0x7f03035f
com.example.fragmentsleam:color/material_dynamic_tertiary30 = 0x7f050305
com.example.fragmentsleam:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0c013d
com.example.fragmentsleam:color/dim_foreground_material_dark = 0x7f050058
com.example.fragmentsleam:dimen/m3_side_sheet_margin_detached = 0x7f0602b3
com.example.fragmentsleam:macro/m3_comp_button_medium_pressed_container_shape = 0x7f0c0028
com.example.fragmentsleam:macro/m3_comp_button_medium_label_text = 0x7f0c0027
com.example.fragmentsleam:attr/tabPaddingStart = 0x7f030490
com.example.fragmentsleam:macro/m3_comp_button_medium_container_shape_square = 0x7f0c0026
com.example.fragmentsleam:attr/colorTertiaryFixedDim = 0x7f030139
com.example.fragmentsleam:macro/m3_comp_button_large_label_text = 0x7f0c0023
com.example.fragmentsleam:dimen/notification_action_icon_size = 0x7f0603f0
com.example.fragmentsleam:macro/m3_comp_button_large_container_shape_square = 0x7f0c0022
com.example.fragmentsleam:macro/m3_comp_button_filled_icon_color = 0x7f0c001b
com.example.fragmentsleam:macro/m3_comp_button_elevated_unselected_icon_color = 0x7f0c0019
com.example.fragmentsleam:color/material_dynamic_neutral80 = 0x7f0502d6
com.example.fragmentsleam:string/m3_sys_motion_easing_emphasized = 0x7f0f003d
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f10041b
com.example.fragmentsleam:macro/m3_comp_button_elevated_unselected_container_color = 0x7f0c0018
com.example.fragmentsleam:macro/m3_comp_button_elevated_selected_container_color = 0x7f0c0016
com.example.fragmentsleam:drawable/notification_tile_bg = 0x7f0700e8
com.example.fragmentsleam:id/autoCompleteToStart = 0x7f080058
com.example.fragmentsleam:macro/m3_comp_fab_surface_container_color = 0x7f0c00a5
com.example.fragmentsleam:attr/fabAnimationMode = 0x7f0301fe
com.example.fragmentsleam:macro/m3_comp_badge_large_label_text_type = 0x7f0c0012
com.example.fragmentsleam:macro/m3_comp_assist_chip_label_text_type = 0x7f0c000f
com.example.fragmentsleam:macro/m3_comp_app_bar_trailing_icon_color = 0x7f0c000d
com.example.fragmentsleam:macro/m3_comp_app_bar_title_color = 0x7f0c000c
com.example.fragmentsleam:style/TextAppearance.Material3.HeadlineMedium = 0x7f100228
com.example.fragmentsleam:color/bright_foreground_inverse_material_dark = 0x7f050024
com.example.fragmentsleam:macro/m3_comp_app_bar_small_title_font = 0x7f0c000a
com.example.fragmentsleam:attr/autoSizeMinTextSize = 0x7f030041
com.example.fragmentsleam:macro/m3_comp_app_bar_medium_flexible_title_font = 0x7f0c0006
com.example.fragmentsleam:styleable/ActionMode = 0x7f110004
com.example.fragmentsleam:macro/m3_comp_app_bar_large_flexible_title_font = 0x7f0c0002
com.example.fragmentsleam:macro/m3_comp_button_xsmall_selected_container_shape_round = 0x7f0c0065
com.example.fragmentsleam:macro/m3_comp_app_bar_large_flexible_subtitle_font = 0x7f0c0001
com.example.fragmentsleam:color/mtrl_navigation_item_icon_tint = 0x7f050382
com.example.fragmentsleam:attr/grid_skips = 0x7f030248
com.example.fragmentsleam:dimen/mtrl_btn_focused_z = 0x7f060343
com.example.fragmentsleam:macro/m3_comp_app_bar_container_color = 0x7f0c0000
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_hovered_label_text_color = 0x7f0c01e5
com.example.fragmentsleam:id/time = 0x7f0801ec
com.example.fragmentsleam:layout/support_simple_spinner_dropdown_item = 0x7f0b0071
com.example.fragmentsleam:dimen/m3_btn_text_btn_padding_left = 0x7f0600e5
com.example.fragmentsleam:layout/select_dialog_singlechoice_material = 0x7f0b0070
com.example.fragmentsleam:macro/m3_comp_slider_inactive_stop_indicator_container_color = 0x7f0c017a
com.example.fragmentsleam:dimen/mtrl_extended_fab_start_padding = 0x7f060394
com.example.fragmentsleam:layout/notification_template_icon_group = 0x7f0b006a
com.example.fragmentsleam:layout/notification_template_custom_big = 0x7f0b0069
com.example.fragmentsleam:layout/notification_action = 0x7f0b0067
com.example.fragmentsleam:layout/mtrl_search_bar = 0x7f0b0065
com.example.fragmentsleam:layout/mtrl_picker_text_input_date = 0x7f0b0063
com.example.fragmentsleam:attr/contrast = 0x7f030165
com.example.fragmentsleam:id/staticPostLayout = 0x7f0801c7
com.example.fragmentsleam:layout/mtrl_picker_header_selection_text = 0x7f0b0060
com.example.fragmentsleam:dimen/m3_comp_slider_inactive_track_height = 0x7f060207
com.example.fragmentsleam:layout/mtrl_picker_header_dialog = 0x7f0b005e
com.example.fragmentsleam:id/bounceStart = 0x7f080068
com.example.fragmentsleam:layout/mtrl_picker_actions = 0x7f0b005b
com.example.fragmentsleam:dimen/material_textinput_min_width = 0x7f060327
com.example.fragmentsleam:layout/mtrl_layout_snackbar_include = 0x7f0b0059
com.example.fragmentsleam:layout/mtrl_calendar_year = 0x7f0b0057
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f1004c8
com.example.fragmentsleam:layout/mtrl_calendar_days_of_week = 0x7f0b0050
com.example.fragmentsleam:layout/mtrl_calendar_day = 0x7f0b004e
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_active_pressed_state_layer_color = 0x7f0c00db
com.example.fragmentsleam:dimen/m3_btn_icon_only_min_width = 0x7f0600db
com.example.fragmentsleam:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b004b
com.example.fragmentsleam:layout/mtrl_alert_dialog = 0x7f0b0047
com.example.fragmentsleam:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f100459
com.example.fragmentsleam:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f1001a5
com.example.fragmentsleam:dimen/abc_text_size_large_material = 0x7f060048
com.example.fragmentsleam:attr/prefixText = 0x7f0303e2
com.example.fragmentsleam:layout/material_time_chip = 0x7f0b0042
com.example.fragmentsleam:layout/material_radial_view_group = 0x7f0b0040
com.example.fragmentsleam:layout/material_clockface_view = 0x7f0b003f
com.example.fragmentsleam:layout/material_clockface_textview = 0x7f0b003e
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_pressed_state_layer_color = 0x7f0c01eb
com.example.fragmentsleam:layout/material_clock_display = 0x7f0b003a
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000d
com.example.fragmentsleam:layout/m3_navigation_menu_subheader = 0x7f0b0037
com.example.fragmentsleam:layout/m3_navigation_menu_divider = 0x7f0b0036
com.example.fragmentsleam:layout/m3_auto_complete_simple_item = 0x7f0b0035
com.example.fragmentsleam:integer/app_bar_elevation_anim_duration = 0x7f090002
com.example.fragmentsleam:layout/m3_alert_dialog = 0x7f0b0032
com.example.fragmentsleam:attr/visibilityMode = 0x7f030558
com.example.fragmentsleam:style/Theme.AppCompat.Empty = 0x7f10025d
com.example.fragmentsleam:layout/ime_secondary_split_test_activity = 0x7f0b0031
com.example.fragmentsleam:layout/ime_base_split_test_activity = 0x7f0b0030
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0c0100
com.example.fragmentsleam:layout/fragment_red = 0x7f0b002f
com.example.fragmentsleam:layout/fragment_blue = 0x7f0b002d
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_vibrant_selected_button_container_color = 0x7f0c0204
com.example.fragmentsleam:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f060379
com.example.fragmentsleam:layout/design_navigation_item = 0x7f0b0025
com.example.fragmentsleam:color/m3_dynamic_default_color_primary_text = 0x7f050081
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_focused_state_layer_color = 0x7f0c01d7
com.example.fragmentsleam:color/m3_sys_color_light_surface_dim = 0x7f050289
com.example.fragmentsleam:layout/design_layout_snackbar_include = 0x7f0b0021
com.example.fragmentsleam:layout/custom_dialog = 0x7f0b001d
com.example.fragmentsleam:style/TextAppearance.AppCompat.Body2 = 0x7f1001bc
com.example.fragmentsleam:layout/activity_main = 0x7f0b001c
com.example.fragmentsleam:layout/abc_screen_toolbar = 0x7f0b0017
com.example.fragmentsleam:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.example.fragmentsleam:attr/dialogCornerRadius = 0x7f030196
com.example.fragmentsleam:drawable/mtrl_popupmenu_background = 0x7f0700cc
com.example.fragmentsleam:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.example.fragmentsleam:layout/abc_screen_simple = 0x7f0b0015
com.example.fragmentsleam:style/Base.Widget.Material3.ExtendedFloatingActionButton.Medium = 0x7f100108
com.example.fragmentsleam:attr/counterMaxLength = 0x7f030175
com.example.fragmentsleam:style/Platform.V25.AppCompat = 0x7f10014b
com.example.fragmentsleam:color/material_personalized_color_outline_variant = 0x7f050338
com.example.fragmentsleam:string/mtrl_checkbox_state_description_indeterminate = 0x7f0f006a
com.example.fragmentsleam:layout/abc_screen_content_include = 0x7f0b0014
com.example.fragmentsleam:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1000f9
com.example.fragmentsleam:layout/abc_list_menu_item_radio = 0x7f0b0011
com.example.fragmentsleam:macro/m3_comp_filled_text_field_input_text_type = 0x7f0c00b4
com.example.fragmentsleam:layout/abc_list_menu_item_layout = 0x7f0b0010
com.example.fragmentsleam:color/switch_thumb_material_dark = 0x7f0503ab
com.example.fragmentsleam:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.example.fragmentsleam:attr/trackDecorationTint = 0x7f030531
com.example.fragmentsleam:layout/abc_dialog_title_material = 0x7f0b000c
com.example.fragmentsleam:id/open_search_view_background = 0x7f08015f
com.example.fragmentsleam:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.example.fragmentsleam:layout/abc_action_mode_bar = 0x7f0b0004
com.example.fragmentsleam:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0c019c
com.example.fragmentsleam:layout/abc_action_menu_layout = 0x7f0b0003
com.example.fragmentsleam:drawable/design_password_eye = 0x7f070085
com.example.fragmentsleam:string/abc_action_bar_up_description = 0x7f0f0001
com.example.fragmentsleam:layout/abc_action_bar_up_container = 0x7f0b0001
com.example.fragmentsleam:color/m3_ref_palette_purple95 = 0x7f0501af
com.example.fragmentsleam:interpolator/mtrl_linear = 0x7f0a0010
com.example.fragmentsleam:interpolator/mtrl_fast_out_linear_in = 0x7f0a000e
com.example.fragmentsleam:attr/containerColor = 0x7f030143
com.example.fragmentsleam:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0a000d
com.example.fragmentsleam:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0a000c
com.example.fragmentsleam:interpolator/m3_sys_motion_easing_standard = 0x7f0a000b
com.example.fragmentsleam:dimen/m3_comp_divider_thickness = 0x7f060139
com.example.fragmentsleam:macro/m3_comp_button_tonal_selected_container_color = 0x7f0c005a
com.example.fragmentsleam:drawable/ic_launcher_background = 0x7f070093
com.example.fragmentsleam:interpolator/m3_sys_motion_easing_linear = 0x7f0a000a
com.example.fragmentsleam:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f100090
com.example.fragmentsleam:attr/colorOnErrorContainer = 0x7f03010b
com.example.fragmentsleam:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.example.fragmentsleam:style/Theme.MaterialComponents.Bridge = 0x7f10028d
com.example.fragmentsleam:style/TextAppearance.AppCompat.Small = 0x7f1001d0
com.example.fragmentsleam:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008
com.example.fragmentsleam:anim/m3_side_sheet_enter_from_left = 0x7f010025
com.example.fragmentsleam:interpolator/m3_sys_motion_easing_emphasized = 0x7f0a0007
com.example.fragmentsleam:drawable/$m3_avd_show_password__1 = 0x7f07000b
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0c011e
com.example.fragmentsleam:attr/insetForeground = 0x7f030287
com.example.fragmentsleam:attr/endIconTintMode = 0x7f0301c9
com.example.fragmentsleam:attr/tabMode = 0x7f03048c
com.example.fragmentsleam:interpolator/fast_out_slow_in = 0x7f0a0006
com.example.fragmentsleam:dimen/cardview_default_radius = 0x7f060054
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Headline6 = 0x7f100247
com.example.fragmentsleam:id/selected = 0x7f0801a4
com.example.fragmentsleam:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.example.fragmentsleam:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.example.fragmentsleam:color/m3_sys_color_dark_background = 0x7f0501f9
com.example.fragmentsleam:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.example.fragmentsleam:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.example.fragmentsleam:integer/status_bar_notification_info_maxnum = 0x7f090046
com.example.fragmentsleam:integer/show_password_duration = 0x7f090045
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1003df
com.example.fragmentsleam:drawable/mtrl_switch_track_decoration = 0x7f0700d9
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.NavRail.Collapsed.Container.Shape = 0x7f10017a
com.example.fragmentsleam:color/m3_ref_palette_pink40 = 0x7f05018e
com.example.fragmentsleam:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f10045b
com.example.fragmentsleam:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f09003b
com.example.fragmentsleam:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f09003a
com.example.fragmentsleam:integer/mtrl_card_anim_duration_ms = 0x7f090037
com.example.fragmentsleam:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f0601e6
com.example.fragmentsleam:style/Widget.Design.TextInputLayout = 0x7f100387
com.example.fragmentsleam:integer/mtrl_card_anim_delay_ms = 0x7f090036
com.example.fragmentsleam:integer/mtrl_calendar_year_selector_span = 0x7f090035
com.example.fragmentsleam:macro/m3_comp_icon_button_small_pressed_container_shape = 0x7f0c00c1
com.example.fragmentsleam:integer/mtrl_calendar_selection_text_lines = 0x7f090034
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f100206
com.example.fragmentsleam:integer/mtrl_btn_anim_duration_ms = 0x7f090032
com.example.fragmentsleam:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f08020b
com.example.fragmentsleam:integer/mtrl_btn_anim_delay_ms = 0x7f090031
com.example.fragmentsleam:integer/mtrl_badge_max_character_count = 0x7f090030
com.example.fragmentsleam:integer/material_motion_duration_short_1 = 0x7f09002d
com.example.fragmentsleam:integer/material_motion_duration_medium_2 = 0x7f09002c
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_large = 0x7f0602f9
com.example.fragmentsleam:integer/material_motion_duration_long_2 = 0x7f09002a
com.example.fragmentsleam:integer/m3_sys_shape_corner_small_corner_family = 0x7f090028
com.example.fragmentsleam:integer/m3_sys_shape_corner_medium_corner_family = 0x7f090027
com.example.fragmentsleam:integer/m3_sys_shape_corner_large_corner_family = 0x7f090025
com.example.fragmentsleam:style/Widget.AppCompat.SeekBar.Discrete = 0x7f100372
com.example.fragmentsleam:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f090023
com.example.fragmentsleam:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1002d8
com.example.fragmentsleam:integer/m3_sys_shape_corner_extra_large_increased_corner_family = 0x7f090022
com.example.fragmentsleam:integer/m3_sys_shape_corner_extra_extra_large_corner_family = 0x7f090020
com.example.fragmentsleam:integer/m3_sys_motion_path = 0x7f09001f
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f10002a
com.example.fragmentsleam:integer/m3_sys_motion_duration_short4 = 0x7f09001e
com.example.fragmentsleam:integer/m3_sys_motion_duration_short3 = 0x7f09001d
com.example.fragmentsleam:style/Base.AlertDialog.AppCompat.Light = 0x7f10000c
com.example.fragmentsleam:attr/indeterminateTrackVisible = 0x7f03027e
com.example.fragmentsleam:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f100110
com.example.fragmentsleam:integer/m3_sys_motion_duration_short2 = 0x7f09001c
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1000c2
com.example.fragmentsleam:integer/m3_sys_motion_duration_medium4 = 0x7f09001a
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_slow_effects_stiffness = 0x7f0602f2
com.example.fragmentsleam:integer/m3_sys_motion_duration_medium1 = 0x7f090017
com.example.fragmentsleam:integer/m3_sys_motion_duration_long4 = 0x7f090016
com.example.fragmentsleam:integer/m3_sys_motion_duration_long3 = 0x7f090015
com.example.fragmentsleam:attr/firstBaselineToTopHeight = 0x7f030209
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_hovered_icon_color = 0x7f0c01e4
com.example.fragmentsleam:integer/m3_sys_motion_duration_long1 = 0x7f090013
com.example.fragmentsleam:integer/m3_sys_motion_duration_extra_long3 = 0x7f090011
com.example.fragmentsleam:integer/m3_chip_anim_duration = 0x7f09000e
com.example.fragmentsleam:attr/collapsedTitleGravityMode = 0x7f0300f2
com.example.fragmentsleam:integer/m3_btn_anim_duration_ms = 0x7f09000b
com.example.fragmentsleam:integer/m3_btn_anim_delay_ms = 0x7f09000a
com.example.fragmentsleam:integer/m3_badge_max_number = 0x7f090009
com.example.fragmentsleam:attr/rippleColor = 0x7f0303ff
com.example.fragmentsleam:integer/hide_password_duration = 0x7f090008
com.example.fragmentsleam:id/search_plate = 0x7f0801a0
com.example.fragmentsleam:id/navigation_bar_item_small_label_view = 0x7f08014b
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1003e0
com.example.fragmentsleam:dimen/m3_navigation_rail_icon_label_horizontal_padding = 0x7f060292
com.example.fragmentsleam:integer/design_tab_indicator_anim_duration_ms = 0x7f090007
com.example.fragmentsleam:drawable/abc_list_pressed_holo_light = 0x7f070052
com.example.fragmentsleam:style/Theme.MaterialComponents.Dialog.Alert = 0x7f1002a0
com.example.fragmentsleam:integer/design_snackbar_text_max_lines = 0x7f090006
com.example.fragmentsleam:style/Animation.AppCompat.Tooltip = 0x7f100004
com.example.fragmentsleam:integer/bottom_sheet_slide_duration = 0x7f090003
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0c0135
com.example.fragmentsleam:integer/abc_config_activityShortDur = 0x7f090001
com.example.fragmentsleam:integer/abc_config_activityDefaultDur = 0x7f090000
com.example.fragmentsleam:id/wrap_content = 0x7f080215
com.example.fragmentsleam:id/withinBounds = 0x7f080213
com.example.fragmentsleam:attr/windowActionBarOverlay = 0x7f030568
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f100116
com.example.fragmentsleam:dimen/m3_alert_dialog_icon_size = 0x7f0600a4
com.example.fragmentsleam:layout/design_navigation_item_subheader = 0x7f0b0028
com.example.fragmentsleam:id/withText = 0x7f080211
com.example.fragmentsleam:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f100054
com.example.fragmentsleam:id/west = 0x7f080210
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f100212
com.example.fragmentsleam:id/view_offset_helper = 0x7f080207
com.example.fragmentsleam:macro/m3_comp_icon_button_small_selected_container_shape_round = 0x7f0c00c2
com.example.fragmentsleam:id/vertical_only = 0x7f080206
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Button = 0x7f100019
com.example.fragmentsleam:attr/trackTint = 0x7f030540
com.example.fragmentsleam:id/useLogo = 0x7f080204
com.example.fragmentsleam:style/Theme.Material3.DynamicColors.Dark = 0x7f10027d
com.example.fragmentsleam:id/unchecked = 0x7f080200
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500ed
com.example.fragmentsleam:id/triangle = 0x7f0801ff
com.example.fragmentsleam:style/Base.Widget.AppCompat.PopupMenu = 0x7f1000e8
com.example.fragmentsleam:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0c0148
com.example.fragmentsleam:id/transition_pause_alpha = 0x7f0801fb
com.example.fragmentsleam:id/transition_layout_save = 0x7f0801fa
com.example.fragmentsleam:id/transition_current_scene = 0x7f0801f8
com.example.fragmentsleam:id/titleDividerNoCustom = 0x7f0801ee
com.example.fragmentsleam:attr/fabAnchorMode = 0x7f0301fd
com.example.fragmentsleam:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.example.fragmentsleam:id/textinput_suffix_text = 0x7f0801eb
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f10017e
com.example.fragmentsleam:id/textinput_prefix_text = 0x7f0801ea
com.example.fragmentsleam:attr/contentPaddingBottom = 0x7f03015e
com.example.fragmentsleam:id/textinput_helper_text = 0x7f0801e8
com.example.fragmentsleam:dimen/m3_comp_button_medium_leading_space = 0x7f06011c
com.example.fragmentsleam:id/textSpacerNoButtons = 0x7f0801de
com.example.fragmentsleam:id/textEnd = 0x7f0801dd
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0c0133
com.example.fragmentsleam:id/frrg2 = 0x7f0800d2
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f10012d
com.example.fragmentsleam:id/tag_state_description = 0x7f0801d6
com.example.fragmentsleam:id/tag_screen_reader_focusable = 0x7f0801d5
com.example.fragmentsleam:macro/m3_comp_search_bar_supporting_text_type = 0x7f0c0156
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_vibrant_container_color = 0x7f0c0201
com.example.fragmentsleam:id/tag_on_apply_window_listener = 0x7f0801d2
com.example.fragmentsleam:color/m3_ref_palette_blue_variant95 = 0x7f0500b8
com.example.fragmentsleam:color/material_dynamic_primary40 = 0x7f0502ec
com.example.fragmentsleam:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f1004b1
com.example.fragmentsleam:id/tag_accessibility_heading = 0x7f0801d0
com.example.fragmentsleam:id/tabMode = 0x7f0801cd
com.example.fragmentsleam:attr/defaultDuration = 0x7f03018e
com.example.fragmentsleam:attr/startIconMinSize = 0x7f030453
com.example.fragmentsleam:id/stretch = 0x7f0801c9
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_headline_color = 0x7f0c00fd
com.example.fragmentsleam:style/Theme.Material3.Light = 0x7f100283
com.example.fragmentsleam:id/supportScrollUp = 0x7f0801cc
com.example.fragmentsleam:id/center_horizontal = 0x7f080071
com.example.fragmentsleam:id/submenuarrow = 0x7f0801ca
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary40 = 0x7f050118
com.example.fragmentsleam:id/stop = 0x7f0801c8
com.example.fragmentsleam:id/staticLayout = 0x7f0801c6
com.example.fragmentsleam:id/startVertical = 0x7f0801c4
com.example.fragmentsleam:id/start = 0x7f0801c1
com.example.fragmentsleam:id/src_over = 0x7f0801bf
com.example.fragmentsleam:id/square = 0x7f0801bc
com.example.fragmentsleam:macro/m3_comp_button_group_connected_small_inner_corner_corner_size = 0x7f0c0020
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary60 = 0x7f0500fe
com.example.fragmentsleam:id/spring = 0x7f0801bb
com.example.fragmentsleam:color/m3_dynamic_dark_highlighted_text = 0x7f05007e
com.example.fragmentsleam:styleable/CoordinatorLayout = 0x7f11002c
com.example.fragmentsleam:color/m3_sys_color_light_secondary = 0x7f050280
com.example.fragmentsleam:id/spread = 0x7f0801b9
com.example.fragmentsleam:style/ThemeOverlay.Material3.Chip = 0x7f1002d7
com.example.fragmentsleam:id/spline = 0x7f0801b7
com.example.fragmentsleam:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f110033
com.example.fragmentsleam:style/Theme.Material3.DayNight.NoActionBar = 0x7f10027b
com.example.fragmentsleam:id/special_effects_controller_view_tag = 0x7f0801b6
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f10030b
com.example.fragmentsleam:attr/cardCornerRadius = 0x7f03009e
com.example.fragmentsleam:id/spacer = 0x7f0801b5
com.example.fragmentsleam:dimen/design_bottom_sheet_elevation = 0x7f06006d
com.example.fragmentsleam:id/south = 0x7f0801b4
com.example.fragmentsleam:id/snackbar_text = 0x7f0801b1
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f100322
com.example.fragmentsleam:id/snackbar_action = 0x7f0801b0
com.example.fragmentsleam:drawable/abc_ic_clear_material = 0x7f070040
com.example.fragmentsleam:id/slide = 0x7f0801af
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker = 0x7f100416
com.example.fragmentsleam:id/skipped = 0x7f0801ae
com.example.fragmentsleam:id/skipCollapsed = 0x7f0801ad
com.example.fragmentsleam:dimen/abc_star_small = 0x7f06003d
com.example.fragmentsleam:id/sin = 0x7f0801ac
com.example.fragmentsleam:id/showTitle = 0x7f0801ab
com.example.fragmentsleam:id/action_mode_bar = 0x7f080041
com.example.fragmentsleam:id/showCustom = 0x7f0801a9
com.example.fragmentsleam:dimen/m3_fab_disabled_elevation = 0x7f06026d
com.example.fragmentsleam:style/Widget.Design.Snackbar = 0x7f100384
com.example.fragmentsleam:attr/tabUnboundedRipple = 0x7f030499
com.example.fragmentsleam:id/sharedValueSet = 0x7f0801a6
com.example.fragmentsleam:id/search_src_text = 0x7f0801a1
com.example.fragmentsleam:color/m3_sys_color_dark_surface_container = 0x7f050212
com.example.fragmentsleam:id/search_mag_icon = 0x7f08019f
com.example.fragmentsleam:dimen/m3_carousel_small_item_default_corner_size = 0x7f0600f5
com.example.fragmentsleam:id/search_go_btn = 0x7f08019e
com.example.fragmentsleam:color/m3_ref_palette_blue10 = 0x7f0500a1
com.example.fragmentsleam:id/search_badge = 0x7f080199
com.example.fragmentsleam:color/m3_sys_color_dark_inverse_surface = 0x7f0501fe
com.example.fragmentsleam:id/scrollIndicatorUp = 0x7f080196
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary10 = 0x7f0500f8
com.example.fragmentsleam:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f060165
com.example.fragmentsleam:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f100064
com.example.fragmentsleam:id/scroll = 0x7f080194
com.example.fragmentsleam:color/m3_ref_palette_tertiary20 = 0x7f0501cf
com.example.fragmentsleam:id/screen = 0x7f080193
com.example.fragmentsleam:attr/layout_constraintCircle = 0x7f0302cb
com.example.fragmentsleam:integer/m3_sys_motion_duration_extra_long4 = 0x7f090012
com.example.fragmentsleam:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009c
com.example.fragmentsleam:id/save_overlay_view = 0x7f080190
com.example.fragmentsleam:id/row_index_key = 0x7f08018e
com.example.fragmentsleam:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0c006a
com.example.fragmentsleam:id/rounded = 0x7f08018d
com.example.fragmentsleam:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f06023e
com.example.fragmentsleam:style/Widget.Material3.MaterialSplitButton = 0x7f100415
com.example.fragmentsleam:macro/m3_comp_elevated_card_container_shape = 0x7f0c008a
com.example.fragmentsleam:attr/upDuration = 0x7f03054c
com.example.fragmentsleam:id/rightToLeft = 0x7f08018a
com.example.fragmentsleam:id/right = 0x7f080189
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f100468
com.example.fragmentsleam:id/retreat = 0x7f080187
com.example.fragmentsleam:color/m3_navigation_item_ripple_color = 0x7f050096
com.example.fragmentsleam:id/recyclerView = 0x7f080185
com.example.fragmentsleam:id/rectangles = 0x7f080184
com.example.fragmentsleam:id/radio = 0x7f080182
com.example.fragmentsleam:dimen/m3_fab_border_width = 0x7f06026b
com.example.fragmentsleam:id/progress_horizontal = 0x7f080181
com.example.fragmentsleam:id/postLayout = 0x7f08017e
com.example.fragmentsleam:id/percent = 0x7f08017a
com.example.fragmentsleam:style/Widget.Material3.BottomAppBar.Legacy = 0x7f100393
com.example.fragmentsleam:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1000fb
com.example.fragmentsleam:id/peekHeight = 0x7f080179
com.example.fragmentsleam:id/pathRelative = 0x7f080178
com.example.fragmentsleam:id/password_toggle = 0x7f080176
com.example.fragmentsleam:string/material_minute_selection = 0x7f0f004d
com.example.fragmentsleam:id/parent_matrix = 0x7f080175
com.example.fragmentsleam:id/parentRelative = 0x7f080174
com.example.fragmentsleam:id/parallax = 0x7f080171
com.example.fragmentsleam:style/Base.ThemeOverlay.AppCompat.Light = 0x7f10007e
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0c0127
com.example.fragmentsleam:id/outward = 0x7f08016e
com.example.fragmentsleam:id/open_search_view_toolbar = 0x7f08016b
com.example.fragmentsleam:id/open_search_view_text_container = 0x7f08016a
com.example.fragmentsleam:id/open_search_view_scrim = 0x7f080167
com.example.fragmentsleam:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f100338
com.example.fragmentsleam:layout/mtrl_alert_dialog_title = 0x7f0b0049
com.example.fragmentsleam:id/open_search_view_edit_text = 0x7f080164
com.example.fragmentsleam:attr/chipBackgroundColor = 0x7f0300c2
com.example.fragmentsleam:attr/extendedFloatingActionButtonMediumStyle = 0x7f0301f3
com.example.fragmentsleam:id/open_search_view_dummy_toolbar = 0x7f080163
com.example.fragmentsleam:color/m3_ref_palette_green10 = 0x7f050130
com.example.fragmentsleam:style/TextAppearance.Material3.BodySmall.Emphasized = 0x7f10021f
com.example.fragmentsleam:color/m3_ref_palette_grey_variant60 = 0x7f050150
com.example.fragmentsleam:id/open_search_view_content_container = 0x7f080161
com.example.fragmentsleam:id/open_search_view_clear_button = 0x7f080160
com.example.fragmentsleam:id/open_search_bar_text_view_container = 0x7f08015e
com.example.fragmentsleam:id/open_search_bar_placeholder_text_view = 0x7f08015c
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_label_text_color = 0x7f0c01dc
com.example.fragmentsleam:anim/abc_fade_in = 0x7f010000
com.example.fragmentsleam:id/onInterceptTouchReturnSwipe = 0x7f08015b
com.example.fragmentsleam:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0c01b3
com.example.fragmentsleam:id/on = 0x7f08015a
com.example.fragmentsleam:color/abc_tint_btn_checkable = 0x7f050013
com.example.fragmentsleam:string/character_counter_overflowed_content_description = 0x7f0f002b
com.example.fragmentsleam:styleable/Insets = 0x7f110041
com.example.fragmentsleam:id/off = 0x7f080159
com.example.fragmentsleam:style/Widget.Material3.CircularProgressIndicator = 0x7f1003bc
com.example.fragmentsleam:id/notification_main_column_container = 0x7f080158
com.example.fragmentsleam:attr/carousel_touchUpMode = 0x7f0300ad
com.example.fragmentsleam:id/notification_main_column = 0x7f080157
com.example.fragmentsleam:attr/applyMotionScene = 0x7f030037
com.example.fragmentsleam:dimen/mtrl_extended_fab_elevation = 0x7f06038d
com.example.fragmentsleam:id/notification_background = 0x7f080156
com.example.fragmentsleam:id/normal = 0x7f080154
com.example.fragmentsleam:id/none = 0x7f080153
com.example.fragmentsleam:dimen/m3_card_disabled_z = 0x7f0600e9
com.example.fragmentsleam:style/Theme.Design.NoActionBar = 0x7f10026b
com.example.fragmentsleam:dimen/mtrl_navigation_rail_default_width = 0x7f0603b1
com.example.fragmentsleam:id/noState = 0x7f080152
com.example.fragmentsleam:id/noScroll = 0x7f080151
com.example.fragmentsleam:style/TextAppearance.Design.Tab = 0x7f1001f9
com.example.fragmentsleam:attr/roundPercent = 0x7f030402
com.example.fragmentsleam:id/navigation_menu_subheader_label = 0x7f08014d
com.example.fragmentsleam:id/navigation_header_container = 0x7f08014c
com.example.fragmentsleam:drawable/m3_selection_control_ripple = 0x7f0700a5
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Bridge = 0x7f100066
com.example.fragmentsleam:id/navigation_bar_item_labels_group = 0x7f080149
com.example.fragmentsleam:styleable/StateListSizeChange = 0x7f11008a
com.example.fragmentsleam:attr/cursorColor = 0x7f03017c
com.example.fragmentsleam:id/navigation_bar_item_icon_view = 0x7f080147
com.example.fragmentsleam:color/m3_sys_color_dark_surface_dim = 0x7f050217
com.example.fragmentsleam:id/outline = 0x7f08016d
com.example.fragmentsleam:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002e
com.example.fragmentsleam:id/navigation_bar_item_icon_container = 0x7f080146
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f10046b
com.example.fragmentsleam:id/multiply = 0x7f080143
com.example.fragmentsleam:id/mtrl_picker_text_input_range_start = 0x7f080140
com.example.fragmentsleam:color/cardview_dark_background = 0x7f05002c
com.example.fragmentsleam:id/mtrl_picker_text_input_range_end = 0x7f08013f
com.example.fragmentsleam:style/Widget.AppCompat.ListPopupWindow = 0x7f100363
com.example.fragmentsleam:id/mtrl_picker_text_input_date = 0x7f08013e
com.example.fragmentsleam:id/mtrl_picker_header_toggle = 0x7f08013d
com.example.fragmentsleam:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700b1
com.example.fragmentsleam:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1001c6
com.example.fragmentsleam:id/mtrl_picker_header = 0x7f08013a
com.example.fragmentsleam:id/mtrl_picker_fullscreen = 0x7f080139
com.example.fragmentsleam:id/mtrl_motion_snapshot_view = 0x7f080138
com.example.fragmentsleam:attr/titleMargins = 0x7f030516
com.example.fragmentsleam:id/mtrl_card_checked_layer_id = 0x7f080135
com.example.fragmentsleam:style/Base.Theme.Material3.Dark = 0x7f100059
com.example.fragmentsleam:id/mtrl_calendar_year_selector_frame = 0x7f080134
com.example.fragmentsleam:id/mtrl_calendar_text_input_frame = 0x7f080133
com.example.fragmentsleam:macro/m3_comp_button_elevated_selected_icon_color = 0x7f0c0017
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f050223
com.example.fragmentsleam:id/mtrl_calendar_frame = 0x7f08012f
com.example.fragmentsleam:id/mtrl_calendar_day_selector_frame = 0x7f08012d
com.example.fragmentsleam:styleable/SwitchMaterial = 0x7f11008d
com.example.fragmentsleam:id/month_grid = 0x7f080126
com.example.fragmentsleam:id/mini = 0x7f080125
com.example.fragmentsleam:dimen/m3_comp_nav_bar_item_icon_size = 0x7f0601a2
com.example.fragmentsleam:id/message = 0x7f080123
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium.Emphasized = 0x7f100209
com.example.fragmentsleam:id/matrix = 0x7f080122
com.example.fragmentsleam:id/material_timepicker_view = 0x7f080120
com.example.fragmentsleam:style/TextAppearance.Material3.SearchView.Prefix = 0x7f100235
com.example.fragmentsleam:id/material_timepicker_ok_button = 0x7f08011f
com.example.fragmentsleam:id/material_timepicker_mode_button = 0x7f08011e
com.example.fragmentsleam:id/material_minute_tv = 0x7f08011a
com.example.fragmentsleam:id/material_hour_text_input = 0x7f080116
com.example.fragmentsleam:id/material_clock_period_pm_button = 0x7f080114
com.example.fragmentsleam:id/material_clock_period_am_button = 0x7f080113
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f100310
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0c0108
com.example.fragmentsleam:id/material_clock_display = 0x7f08010e
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_background = 0x7f050243
com.example.fragmentsleam:string/mtrl_picker_toggle_to_year_selection = 0x7f0f0093
com.example.fragmentsleam:id/match_parent = 0x7f08010d
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f100490
com.example.fragmentsleam:drawable/abc_seekbar_tick_mark_material = 0x7f070064
com.example.fragmentsleam:style/Widget.MaterialComponents.ActionMode = 0x7f100454
com.example.fragmentsleam:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b5
com.example.fragmentsleam:id/match_constraint = 0x7f08010c
com.example.fragmentsleam:id/masked = 0x7f08010b
com.example.fragmentsleam:id/marquee = 0x7f08010a
com.example.fragmentsleam:attr/textAppearanceDisplayLargeEmphasized = 0x7f0304aa
com.example.fragmentsleam:id/main = 0x7f080109
com.example.fragmentsleam:color/material_personalized_color_control_normal = 0x7f050328
com.example.fragmentsleam:id/m3_side_sheet = 0x7f080108
com.example.fragmentsleam:id/linearLayout = 0x7f080104
com.example.fragmentsleam:attr/hideMotionSpec = 0x7f030258
com.example.fragmentsleam:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0c015f
com.example.fragmentsleam:id/linear = 0x7f080103
com.example.fragmentsleam:id/legacy = 0x7f080100
com.example.fragmentsleam:color/black = 0x7f050021
com.example.fragmentsleam:color/m3_ref_palette_green50 = 0x7f050135
com.example.fragmentsleam:color/material_personalized_color_primary_text = 0x7f05033c
com.example.fragmentsleam:string/m3_sys_motion_easing_standard = 0x7f0f0045
com.example.fragmentsleam:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1000d4
com.example.fragmentsleam:id/layout = 0x7f0800fd
com.example.fragmentsleam:id/labeled = 0x7f0800fc
com.example.fragmentsleam:id/jumpToStart = 0x7f0800fb
com.example.fragmentsleam:id/item_image = 0x7f0800f6
com.example.fragmentsleam:id/italic = 0x7f0800f5
com.example.fragmentsleam:id/is_pooling_container_tag = 0x7f0800f4
com.example.fragmentsleam:id/inward = 0x7f0800f3
com.example.fragmentsleam:styleable/KeyPosition = 0x7f110047
com.example.fragmentsleam:macro/m3_comp_fab_primary_container_color = 0x7f0c009c
com.example.fragmentsleam:attr/carousel_previousState = 0x7f0300ac
com.example.fragmentsleam:id/included = 0x7f0800ef
com.example.fragmentsleam:id/immediateStop = 0x7f0800ee
com.example.fragmentsleam:id/imageButton = 0x7f0800ec
com.example.fragmentsleam:id/icon_group = 0x7f0800e7
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary50 = 0x7f050119
com.example.fragmentsleam:id/honorRequest = 0x7f0800e3
com.example.fragmentsleam:id/home = 0x7f0800e1
com.example.fragmentsleam:color/primary_text_disabled_material_dark = 0x7f0503a1
com.example.fragmentsleam:id/hide_ime_id = 0x7f0800df
com.example.fragmentsleam:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600cf
com.example.fragmentsleam:macro/m3_comp_dialog_headline_color = 0x7f0c0084
com.example.fragmentsleam:id/hidden = 0x7f0800de
com.example.fragmentsleam:id/header_title = 0x7f0800dd
com.example.fragmentsleam:id/groups = 0x7f0800dc
com.example.fragmentsleam:color/m3_sys_color_secondary_fixed = 0x7f050295
com.example.fragmentsleam:id/graph = 0x7f0800d8
com.example.fragmentsleam:dimen/m3_comp_button_medium_trailing_space = 0x7f06011e
com.example.fragmentsleam:id/gotoFrg3 = 0x7f0800d7
com.example.fragmentsleam:style/ThemeOverlay.Material3.TabLayout = 0x7f100302
com.example.fragmentsleam:id/ghost_view_holder = 0x7f0800d5
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f100493
com.example.fragmentsleam:mipmap/ic_launcher = 0x7f0d0000
com.example.fragmentsleam:id/ghost_view = 0x7f0800d4
com.example.fragmentsleam:attr/snackbarTextViewStyle = 0x7f030442
com.example.fragmentsleam:id/frost = 0x7f0800d0
com.example.fragmentsleam:id/frame = 0x7f0800ce
com.example.fragmentsleam:dimen/appcompat_dialog_background_inset = 0x7f060051
com.example.fragmentsleam:id/fragment_container_view_tag = 0x7f0800cd
com.example.fragmentsleam:drawable/abc_ic_voice_search_api_material = 0x7f07004a
com.example.fragmentsleam:id/forever = 0x7f0800cc
com.example.fragmentsleam:attr/carousel_emptyViewsBehavior = 0x7f0300a7
com.example.fragmentsleam:drawable/abc_cab_background_internal_bg = 0x7f070038
com.example.fragmentsleam:id/floating = 0x7f0800cb
com.example.fragmentsleam:attr/actionBarSplitStyle = 0x7f030006
com.example.fragmentsleam:id/fitXY = 0x7f0800c8
com.example.fragmentsleam:macro/m3_comp_search_bar_container_color = 0x7f0c014d
com.example.fragmentsleam:id/fitToContents = 0x7f0800c7
com.example.fragmentsleam:id/fitStart = 0x7f0800c6
com.example.fragmentsleam:color/m3_ref_palette_grey_variant100 = 0x7f05014b
com.example.fragmentsleam:id/fitEnd = 0x7f0800c5
com.example.fragmentsleam:id/fitCenter = 0x7f0800c4
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f0602d7
com.example.fragmentsleam:color/mtrl_text_btn_text_color_selector = 0x7f050393
com.example.fragmentsleam:id/month_title = 0x7f08012b
com.example.fragmentsleam:id/fill_vertical = 0x7f0800c2
com.example.fragmentsleam:string/mtrl_exceed_max_badge_number_content_description = 0x7f0f006d
com.example.fragmentsleam:id/fill_horizontal = 0x7f0800c1
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f100357
com.example.fragmentsleam:dimen/m3_ripple_default_alpha = 0x7f0602a3
com.example.fragmentsleam:id/fade = 0x7f0800bf
com.example.fragmentsleam:id/expanded_menu = 0x7f0800be
com.example.fragmentsleam:style/Base.Theme.Material3.Light = 0x7f10005f
com.example.fragmentsleam:id/enterAlways = 0x7f0800b8
com.example.fragmentsleam:id/transition_scene_layoutid_cache = 0x7f0801fd
com.example.fragmentsleam:id/endToStart = 0x7f0800b7
com.example.fragmentsleam:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009
com.example.fragmentsleam:style/Widget.Material3.Chip.Input.Elevated = 0x7f1003b6
com.example.fragmentsleam:id/elastic = 0x7f0800b4
com.example.fragmentsleam:id/edge = 0x7f0800b1
com.example.fragmentsleam:id/east = 0x7f0800b0
com.example.fragmentsleam:color/abc_hint_foreground_material_light = 0x7f050008
com.example.fragmentsleam:id/easeInOut = 0x7f0800ae
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialDivider = 0x7f10049e
com.example.fragmentsleam:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1003c6
com.example.fragmentsleam:id/dropdown_menu = 0x7f0800ac
com.example.fragmentsleam:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1002eb
com.example.fragmentsleam:layout/mtrl_picker_fullscreen = 0x7f0b005d
com.example.fragmentsleam:id/dragUp = 0x7f0800ab
com.example.fragmentsleam:id/dragEnd = 0x7f0800a7
com.example.fragmentsleam:style/ThemeOverlay.AppCompat.Dialog = 0x7f1002c0
com.example.fragmentsleam:id/dragDown = 0x7f0800a6
com.example.fragmentsleam:id/dragClockwise = 0x7f0800a5
com.example.fragmentsleam:id/dragAnticlockwise = 0x7f0800a4
com.example.fragmentsleam:id/disableScroll = 0x7f0800a2
com.example.fragmentsleam:id/disableIntraAutoTransition = 0x7f0800a0
com.example.fragmentsleam:id/dialog_button = 0x7f08009c
com.example.fragmentsleam:id/month_navigation_next = 0x7f080129
com.example.fragmentsleam:id/design_menu_item_text = 0x7f08009a
com.example.fragmentsleam:style/TextAppearance.Material3.DisplayLarge = 0x7f100220
com.example.fragmentsleam:attr/percentHeight = 0x7f0303d3
com.example.fragmentsleam:id/design_menu_item_action_area = 0x7f080098
com.example.fragmentsleam:id/design_bottom_sheet = 0x7f080097
com.example.fragmentsleam:color/m3_text_button_ripple_color_selector = 0x7f0502a1
com.example.fragmentsleam:id/deltaRelative = 0x7f080095
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1003e7
com.example.fragmentsleam:id/default_activity_button = 0x7f080094
com.example.fragmentsleam:id/decelerate = 0x7f080091
com.example.fragmentsleam:id/cut = 0x7f08008f
com.example.fragmentsleam:id/customPanel = 0x7f08008e
com.example.fragmentsleam:attr/statusBarBackground = 0x7f030462
com.example.fragmentsleam:id/custom = 0x7f08008d
com.example.fragmentsleam:attr/layout_optimizationLevel = 0x7f0302fd
com.example.fragmentsleam:drawable/$m3_avd_show_password__0 = 0x7f07000a
com.example.fragmentsleam:style/TextAppearance.Material3.BodyLarge = 0x7f10021a
com.example.fragmentsleam:id/counterclockwise = 0x7f08008a
com.example.fragmentsleam:id/cos = 0x7f080089
com.example.fragmentsleam:dimen/m3_multiline_hint_filled_text_extra_space = 0x7f06027b
com.example.fragmentsleam:color/m3_chip_text_color = 0x7f050074
com.example.fragmentsleam:macro/m3_comp_button_outlined_hovered_label_text_color = 0x7f0c0030
com.example.fragmentsleam:attr/grid_useRtl = 0x7f03024a
com.example.fragmentsleam:id/coordinator = 0x7f080088
com.example.fragmentsleam:id/content = 0x7f080084
com.example.fragmentsleam:dimen/material_clock_hand_padding = 0x7f06030c
com.example.fragmentsleam:id/confirm_button = 0x7f080081
com.example.fragmentsleam:id/icon = 0x7f0800e6
com.example.fragmentsleam:id/collapseActionView = 0x7f08007f
com.example.fragmentsleam:style/Theme.Material3.Light.BottomSheetDialog = 0x7f100284
com.example.fragmentsleam:color/m3_ref_palette_grey40 = 0x7f050141
com.example.fragmentsleam:id/circle_center = 0x7f080079
com.example.fragmentsleam:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f06023b
com.example.fragmentsleam:id/checkbox = 0x7f080076
com.example.fragmentsleam:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f10049d
com.example.fragmentsleam:id/chain2 = 0x7f080074
com.example.fragmentsleam:integer/material_motion_path = 0x7f09002f
com.example.fragmentsleam:id/chain = 0x7f080073
com.example.fragmentsleam:id/center_vertical = 0x7f080072
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f100208
com.example.fragmentsleam:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f100103
com.example.fragmentsleam:attr/hoveredFocusedTranslationZ = 0x7f030268
com.example.fragmentsleam:layout/design_text_input_start_icon = 0x7f0b002c
com.example.fragmentsleam:id/centerCrop = 0x7f08006f
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.Medium = 0x7f100199
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f100297
com.example.fragmentsleam:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f06029d
com.example.fragmentsleam:layout/design_navigation_menu = 0x7f0b0029
com.example.fragmentsleam:string/mtrl_picker_announce_current_range_selection = 0x7f0f0071
com.example.fragmentsleam:id/center = 0x7f08006e
com.example.fragmentsleam:id/carryVelocity = 0x7f08006d
com.example.fragmentsleam:color/material_dynamic_primary30 = 0x7f0502eb
com.example.fragmentsleam:id/tag_accessibility_pane_title = 0x7f0801d1
com.example.fragmentsleam:id/cache_measures = 0x7f08006a
com.example.fragmentsleam:drawable/abc_list_selector_disabled_holo_light = 0x7f070056
com.example.fragmentsleam:id/bounceEnd = 0x7f080067
com.example.fragmentsleam:styleable/TextAppearance = 0x7f110090
com.example.fragmentsleam:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f1004a0
com.example.fragmentsleam:id/bounce = 0x7f080065
com.example.fragmentsleam:style/Theme.AppCompat.Light = 0x7f10025e
com.example.fragmentsleam:color/m3_dark_default_color_primary_text = 0x7f050075
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_circular_active_indicator_wave_amplitude = 0x7f0601d7
com.example.fragmentsleam:id/beginning = 0x7f080060
com.example.fragmentsleam:id/barrier = 0x7f08005d
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f10029d
com.example.fragmentsleam:id/availableSpace = 0x7f08005b
com.example.fragmentsleam:id/auto = 0x7f080055
com.example.fragmentsleam:id/material_label = 0x7f080118
com.example.fragmentsleam:dimen/m3_comp_extended_fab_small_container_height = 0x7f06014d
com.example.fragmentsleam:id/async = 0x7f080054
com.example.fragmentsleam:id/topPanel = 0x7f0801f2
com.example.fragmentsleam:id/asConfigured = 0x7f080053
com.example.fragmentsleam:attr/cardMaxElevation = 0x7f0300a1
com.example.fragmentsleam:style/Base.Widget.AppCompat.ProgressBar = 0x7f1000eb
com.example.fragmentsleam:attr/textLocale = 0x7f0304e5
com.example.fragmentsleam:id/antiClockwise = 0x7f080050
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.Icon = 0x7f100466
com.example.fragmentsleam:id/animateToStart = 0x7f08004f
com.example.fragmentsleam:id/search_voice_btn = 0x7f0801a2
com.example.fragmentsleam:id/animateToEnd = 0x7f08004e
com.example.fragmentsleam:layout/abc_tooltip = 0x7f0b001b
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f100071
com.example.fragmentsleam:id/actions = 0x7f080045
com.example.fragmentsleam:id/action_text = 0x7f080044
com.example.fragmentsleam:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f0601cc
com.example.fragmentsleam:drawable/m3_popupmenu_background_overlay = 0x7f0700a3
com.example.fragmentsleam:id/action_mode_close_button = 0x7f080043
com.example.fragmentsleam:id/list_item = 0x7f080107
com.example.fragmentsleam:id/action_menu_presenter = 0x7f080040
com.example.fragmentsleam:id/action_menu_divider = 0x7f08003f
com.example.fragmentsleam:string/mtrl_switch_thumb_path_unchecked = 0x7f0f009b
com.example.fragmentsleam:color/m3_sys_color_dark_primary = 0x7f05020c
com.example.fragmentsleam:id/action_image = 0x7f08003e
com.example.fragmentsleam:id/action_context_bar = 0x7f08003c
com.example.fragmentsleam:id/action_bar_title = 0x7f08003a
com.example.fragmentsleam:dimen/m3_navigation_rail_default_width = 0x7f06028d
com.example.fragmentsleam:id/action_bar_subtitle = 0x7f080039
com.example.fragmentsleam:id/action_bar_spinner = 0x7f080038
com.example.fragmentsleam:id/action_bar_activity_content = 0x7f080035
com.example.fragmentsleam:id/actionDown = 0x7f080031
com.example.fragmentsleam:id/accessibility_custom_action_9 = 0x7f080030
com.example.fragmentsleam:id/accessibility_custom_action_7 = 0x7f08002e
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500d0
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface_bright = 0x7f050255
com.example.fragmentsleam:id/accessibility_custom_action_4 = 0x7f08002b
com.example.fragmentsleam:attr/state_error = 0x7f03045d
com.example.fragmentsleam:id/accessibility_custom_action_31 = 0x7f08002a
com.example.fragmentsleam:style/Base.Widget.Material3.CollapsingToolbar = 0x7f100100
com.example.fragmentsleam:id/accessibility_custom_action_3 = 0x7f080028
com.example.fragmentsleam:id/accessibility_custom_action_29 = 0x7f080027
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f050236
com.example.fragmentsleam:color/abc_search_url_text = 0x7f05000d
com.example.fragmentsleam:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1002f5
com.example.fragmentsleam:macro/m3_comp_time_picker_clock_dial_color = 0x7f0c01b2
com.example.fragmentsleam:attr/itemHorizontalPadding = 0x7f030290
com.example.fragmentsleam:id/action_bar_container = 0x7f080036
com.example.fragmentsleam:id/accessibility_custom_action_28 = 0x7f080026
com.example.fragmentsleam:id/accessibility_custom_action_27 = 0x7f080025
com.example.fragmentsleam:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.example.fragmentsleam:id/accessibility_custom_action_24 = 0x7f080022
com.example.fragmentsleam:id/accessibility_custom_action_23 = 0x7f080021
com.example.fragmentsleam:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600cc
com.example.fragmentsleam:style/Widget.Material3.TabLayout.Secondary = 0x7f10043f
com.example.fragmentsleam:color/error_color_material_dark = 0x7f05005a
com.example.fragmentsleam:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f06031b
com.example.fragmentsleam:id/accessibility_custom_action_22 = 0x7f080020
com.example.fragmentsleam:id/accessibility_custom_action_21 = 0x7f08001f
com.example.fragmentsleam:styleable/RadialViewGroup = 0x7f110078
com.example.fragmentsleam:id/accessibility_custom_action_19 = 0x7f08001c
com.example.fragmentsleam:id/accessibility_custom_action_15 = 0x7f080018
com.example.fragmentsleam:style/Platform.MaterialComponents = 0x7f100142
com.example.fragmentsleam:id/accessibility_custom_action_13 = 0x7f080016
com.example.fragmentsleam:id/accessibility_custom_action_12 = 0x7f080015
com.example.fragmentsleam:id/accessibility_custom_action_11 = 0x7f080014
com.example.fragmentsleam:id/text = 0x7f0801db
com.example.fragmentsleam:layout/material_timepicker_dialog = 0x7f0b0045
com.example.fragmentsleam:id/accessibility_custom_action_1 = 0x7f080012
com.example.fragmentsleam:color/m3_ref_palette_grey70 = 0x7f050144
com.example.fragmentsleam:id/accessibility_action_clickable_span = 0x7f080010
com.example.fragmentsleam:color/m3_ref_palette_neutral4 = 0x7f05015f
com.example.fragmentsleam:dimen/abc_text_size_display_1_material = 0x7f060043
com.example.fragmentsleam:attr/itemShapeAppearance = 0x7f03029c
com.example.fragmentsleam:id/accelerate = 0x7f08000f
com.example.fragmentsleam:id/TOP_START = 0x7f08000d
com.example.fragmentsleam:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06016a
com.example.fragmentsleam:id/TOP_END = 0x7f08000c
com.example.fragmentsleam:style/Widget.AppCompat.TextView = 0x7f100377
com.example.fragmentsleam:id/SHOW_ALL = 0x7f080008
com.example.fragmentsleam:id/CTRL = 0x7f080003
com.example.fragmentsleam:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f100082
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f0602e8
com.example.fragmentsleam:layout/select_dialog_item_material = 0x7f0b006e
com.example.fragmentsleam:id/BOTTOM_START = 0x7f080002
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1001e8
com.example.fragmentsleam:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.example.fragmentsleam:dimen/m3_comp_icon_button_small_default_trailing_space = 0x7f06017f
com.example.fragmentsleam:id/BOTTOM_END = 0x7f080001
com.example.fragmentsleam:drawable/tooltip_frame_light = 0x7f0700f3
com.example.fragmentsleam:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f10015d
com.example.fragmentsleam:color/m3_sys_color_dark_surface_container_highest = 0x7f050214
com.example.fragmentsleam:drawable/tooltip_frame_dark = 0x7f0700f2
com.example.fragmentsleam:attr/extendStrategy = 0x7f0301f1
com.example.fragmentsleam:drawable/thresh = 0x7f0700f1
com.example.fragmentsleam:style/Widget.Material3.FloatingToolbar = 0x7f1003ec
com.example.fragmentsleam:style/TextAppearance.Compat.Notification.Info = 0x7f1001eb
com.example.fragmentsleam:styleable/MenuItem = 0x7f110065
com.example.fragmentsleam:color/primary_material_dark = 0x7f05039d
com.example.fragmentsleam:drawable/s5 = 0x7f0700ee
com.example.fragmentsleam:drawable/s2 = 0x7f0700eb
com.example.fragmentsleam:drawable/s1 = 0x7f0700ea
com.example.fragmentsleam:id/neverCompleteToStart = 0x7f080150
com.example.fragmentsleam:drawable/notification_template_icon_low_bg = 0x7f0700e7
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f1001af
com.example.fragmentsleam:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f0601cd
com.example.fragmentsleam:id/contiguous = 0x7f080086
com.example.fragmentsleam:drawable/notification_template_icon_bg = 0x7f0700e6
com.example.fragmentsleam:style/TextAppearance.AppCompat.Body1 = 0x7f1001bb
com.example.fragmentsleam:style/ThemeOverlay.Design.TextInputEditText = 0x7f1002c3
com.example.fragmentsleam:drawable/notification_icon_background = 0x7f0700e4
com.example.fragmentsleam:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0c0192
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Large.Container.Shape.Round = 0x7f100162
com.example.fragmentsleam:id/title = 0x7f0801ed
com.example.fragmentsleam:drawable/notification_bg_normal = 0x7f0700e2
com.example.fragmentsleam:drawable/notification_bg_low_pressed = 0x7f0700e1
com.example.fragmentsleam:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f100255
com.example.fragmentsleam:drawable/notification_bg_low_normal = 0x7f0700e0
com.example.fragmentsleam:drawable/notification_bg_low = 0x7f0700df
com.example.fragmentsleam:drawable/notification_bg = 0x7f0700de
com.example.fragmentsleam:drawable/mtrl_tabs_default_indicator = 0x7f0700da
com.example.fragmentsleam:drawable/mtrl_switch_track = 0x7f0700d8
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_disabled_outline_color = 0x7f0c0045
com.example.fragmentsleam:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700d7
com.example.fragmentsleam:drawable/mtrl_switch_thumb_pressed = 0x7f0700d2
com.example.fragmentsleam:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700d1
com.example.fragmentsleam:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700d0
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f10012c
com.example.fragmentsleam:attr/simpleItemSelectedColor = 0x7f030438
com.example.fragmentsleam:drawable/mtrl_switch_thumb = 0x7f0700ce
com.example.fragmentsleam:drawable/mtrl_popupmenu_background_overlay = 0x7f0700cd
com.example.fragmentsleam:drawable/navigation_empty_icon = 0x7f0700dc
com.example.fragmentsleam:dimen/m3_comp_split_button_medium_trailing_button_leading_space = 0x7f060225
com.example.fragmentsleam:drawable/mtrl_ic_error = 0x7f0700c9
com.example.fragmentsleam:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f0f0090
com.example.fragmentsleam:string/m3_sys_motion_easing_standard_decelerate = 0x7f0f0047
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0c0077
com.example.fragmentsleam:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700c8
com.example.fragmentsleam:dimen/clock_face_margin_start = 0x7f060055
com.example.fragmentsleam:drawable/mtrl_ic_checkbox_checked = 0x7f0700c7
com.example.fragmentsleam:drawable/s4 = 0x7f0700ed
com.example.fragmentsleam:drawable/mtrl_ic_cancel = 0x7f0700c5
com.example.fragmentsleam:drawable/mtrl_ic_arrow_drop_up = 0x7f0700c4
com.example.fragmentsleam:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f100055
com.example.fragmentsleam:drawable/mtrl_dialog_background = 0x7f0700c1
com.example.fragmentsleam:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700bf
com.example.fragmentsleam:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700bd
com.example.fragmentsleam:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700bc
com.example.fragmentsleam:dimen/mtrl_extended_fab_translation_z_base = 0x7f060397
com.example.fragmentsleam:dimen/m3_comp_fab_primary_container_focused_state_layer_opacity = 0x7f06015a
com.example.fragmentsleam:style/Widget.MaterialComponents.CardView = 0x7f100471
com.example.fragmentsleam:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700bb
com.example.fragmentsleam:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700b8
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0c0161
com.example.fragmentsleam:color/material_dynamic_neutral70 = 0x7f0502d5
com.example.fragmentsleam:drawable/mtrl_checkbox_button = 0x7f0700b7
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f100119
com.example.fragmentsleam:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700b4
com.example.fragmentsleam:id/mtrl_picker_header_selection_text = 0x7f08013b
com.example.fragmentsleam:dimen/mtrl_btn_text_btn_padding_left = 0x7f060352
com.example.fragmentsleam:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700b0
com.example.fragmentsleam:layout/mtrl_calendar_months = 0x7f0b0055
com.example.fragmentsleam:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700af
com.example.fragmentsleam:drawable/material_ic_edit_black_24dp = 0x7f0700ae
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1003de
com.example.fragmentsleam:drawable/m3_tabs_line_indicator = 0x7f0700a8
com.example.fragmentsleam:anim/m3_side_sheet_enter_from_right = 0x7f010026
com.example.fragmentsleam:id/mtrl_calendar_months = 0x7f080131
com.example.fragmentsleam:dimen/m3_searchview_height = 0x7f0602b2
com.example.fragmentsleam:drawable/m3_tabs_background = 0x7f0700a7
com.example.fragmentsleam:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.example.fragmentsleam:drawable/material_cursor_drawable = 0x7f0700ab
com.example.fragmentsleam:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f100015
com.example.fragmentsleam:drawable/m3_split_button_chevron_avd = 0x7f0700a6
com.example.fragmentsleam:color/m3_textfield_label_color = 0x7f0502a5
com.example.fragmentsleam:drawable/m3_radiobutton_ripple = 0x7f0700a4
com.example.fragmentsleam:drawable/m3_bottom_sheet_drag_handle = 0x7f0700a1
com.example.fragmentsleam:drawable/m3_avd_hide_password = 0x7f07009f
com.example.fragmentsleam:color/m3_sys_color_light_inverse_primary = 0x7f05026f
com.example.fragmentsleam:integer/m3_sys_motion_duration_medium2 = 0x7f090018
com.example.fragmentsleam:drawable/ic_mtrl_chip_close_circle = 0x7f07009c
com.example.fragmentsleam:drawable/nasuse = 0x7f0700db
com.example.fragmentsleam:drawable/ic_mtrl_chip_checked_black = 0x7f07009a
com.example.fragmentsleam:drawable/ic_mtrl_checked_circle = 0x7f070099
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f1004b6
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f100410
com.example.fragmentsleam:attr/scrimAnimationDuration = 0x7f030405
com.example.fragmentsleam:drawable/ic_mtrl_arrow_circle = 0x7f070098
com.example.fragmentsleam:drawable/ic_m3_chip_close = 0x7f070097
com.example.fragmentsleam:drawable/ic_m3_chip_checked_circle = 0x7f070096
com.example.fragmentsleam:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.example.fragmentsleam:dimen/design_bottom_sheet_peek_height_min = 0x7f06006f
com.example.fragmentsleam:drawable/ic_launcher_foreground = 0x7f070094
com.example.fragmentsleam:styleable/AppCompatTextView = 0x7f110011
com.example.fragmentsleam:attr/onStateTransition = 0x7f0303bb
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f070020
com.example.fragmentsleam:drawable/ic_keyboard_black_24dp = 0x7f070092
com.example.fragmentsleam:drawable/ic_expand_more_22px = 0x7f070091
com.example.fragmentsleam:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0c01a9
com.example.fragmentsleam:attr/layout_insetEdge = 0x7f0302fa
com.example.fragmentsleam:attr/indeterminateAnimationType = 0x7f03027a
com.example.fragmentsleam:drawable/ic_clear_black_24 = 0x7f07008e
com.example.fragmentsleam:drawable/ic_call_decline_low = 0x7f07008d
com.example.fragmentsleam:drawable/ic_call_decline = 0x7f07008c
com.example.fragmentsleam:drawable/ic_call_answer_video_low = 0x7f07008b
com.example.fragmentsleam:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f1003a0
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_focused_icon_color = 0x7f0c01e1
com.example.fragmentsleam:macro/m3_comp_time_picker_headline_color = 0x7f0c01b6
com.example.fragmentsleam:attr/materialCalendarHeaderConfirmButton = 0x7f030332
com.example.fragmentsleam:integer/cancel_button_image_alpha = 0x7f090004
com.example.fragmentsleam:drawable/ic_call_answer = 0x7f070088
com.example.fragmentsleam:id/standard = 0x7f0801c0
com.example.fragmentsleam:drawable/ic_arrow_back_black_24 = 0x7f070087
com.example.fragmentsleam:id/container = 0x7f080083
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f1002b7
com.example.fragmentsleam:drawable/design_snackbar_background = 0x7f070086
com.example.fragmentsleam:drawable/design_ic_visibility_off = 0x7f070084
com.example.fragmentsleam:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07007f
com.example.fragmentsleam:style/Widget.Material3.MaterialDivider.Heavy = 0x7f100414
com.example.fragmentsleam:drawable/avd_show_password = 0x7f070079
com.example.fragmentsleam:drawable/ic_call_answer_video = 0x7f07008a
com.example.fragmentsleam:color/m3_ref_palette_blue_variant70 = 0x7f0500b5
com.example.fragmentsleam:drawable/abc_textfield_search_material = 0x7f070076
com.example.fragmentsleam:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070075
com.example.fragmentsleam:attr/title = 0x7f03050d
com.example.fragmentsleam:drawable/abc_textfield_default_mtrl_alpha = 0x7f070073
com.example.fragmentsleam:drawable/abc_text_select_handle_right_mtrl = 0x7f070071
com.example.fragmentsleam:drawable/abc_text_select_handle_middle_mtrl = 0x7f070070
com.example.fragmentsleam:id/dragLeft = 0x7f0800a8
com.example.fragmentsleam:drawable/abc_text_select_handle_left_mtrl = 0x7f07006f
com.example.fragmentsleam:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0603c2
com.example.fragmentsleam:drawable/abc_text_cursor_material = 0x7f07006e
com.example.fragmentsleam:color/material_dynamic_neutral40 = 0x7f0502d2
com.example.fragmentsleam:drawable/abc_switch_thumb_material = 0x7f07006a
com.example.fragmentsleam:drawable/abc_star_half_black_48dp = 0x7f070069
com.example.fragmentsleam:drawable/abc_star_black_48dp = 0x7f070068
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f100324
com.example.fragmentsleam:drawable/abc_seekbar_thumb_material = 0x7f070063
com.example.fragmentsleam:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f050260
com.example.fragmentsleam:animator/fragment_fade_enter = 0x7f020007
com.example.fragmentsleam:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070062
com.example.fragmentsleam:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070061
com.example.fragmentsleam:macro/m3_comp_bottom_app_bar_container_color = 0x7f0c0013
com.example.fragmentsleam:macro/m3_comp_input_chip_container_shape = 0x7f0c00ce
com.example.fragmentsleam:attr/allowStacking = 0x7f03002d
com.example.fragmentsleam:dimen/abc_progress_bar_height_material = 0x7f060035
com.example.fragmentsleam:id/fromWhere = 0x7f0800cf
com.example.fragmentsleam:styleable/BaseProgressIndicator = 0x7f110014
com.example.fragmentsleam:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005f
com.example.fragmentsleam:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005e
com.example.fragmentsleam:macro/m3_comp_nav_rail_collapsed_container_color = 0x7f0c00e3
com.example.fragmentsleam:drawable/abc_ratingbar_indicator_material = 0x7f07005b
com.example.fragmentsleam:attr/motionDurationLong2 = 0x7f03037b
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f10003a
com.example.fragmentsleam:drawable/abc_popup_background_mtrl_mult = 0x7f07005a
com.example.fragmentsleam:drawable/abc_list_selector_holo_light = 0x7f070058
com.example.fragmentsleam:drawable/abc_list_selector_disabled_holo_dark = 0x7f070055
com.example.fragmentsleam:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070053
com.example.fragmentsleam:drawable/abc_list_divider_material = 0x7f07004d
com.example.fragmentsleam:drawable/abc_item_background_holo_light = 0x7f07004c
com.example.fragmentsleam:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0c016c
com.example.fragmentsleam:color/design_default_color_primary_variant = 0x7f050048
com.example.fragmentsleam:drawable/abc_ic_search_api_material = 0x7f070049
com.example.fragmentsleam:color/m3_calendar_item_disabled_text = 0x7f050069
com.example.fragmentsleam:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070048
com.example.fragmentsleam:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070043
com.example.fragmentsleam:color/m3_ref_palette_green60 = 0x7f050136
com.example.fragmentsleam:style/Widget.Material3.DockedToolbar = 0x7f1003cb
com.example.fragmentsleam:layout/mtrl_navigation_rail_item = 0x7f0b005a
com.example.fragmentsleam:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003f
com.example.fragmentsleam:drawable/abc_edit_text_material = 0x7f07003d
com.example.fragmentsleam:attr/shapeCornerSizeLarge = 0x7f030425
com.example.fragmentsleam:style/Theme.Material3.DayNight.Dialog = 0x7f100277
com.example.fragmentsleam:drawable/abc_dialog_material_background = 0x7f07003c
com.example.fragmentsleam:macro/m3_comp_checkbox_selected_container_color = 0x7f0c0066
com.example.fragmentsleam:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070037
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500df
com.example.fragmentsleam:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070036
com.example.fragmentsleam:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070035
com.example.fragmentsleam:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070034
com.example.fragmentsleam:drawable/abc_btn_radio_material_anim = 0x7f070033
com.example.fragmentsleam:color/abc_search_url_text_selected = 0x7f050010
com.example.fragmentsleam:drawable/abc_btn_radio_material = 0x7f070032
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f100189
com.example.fragmentsleam:color/primary_text_default_material_dark = 0x7f05039f
com.example.fragmentsleam:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0c012d
com.example.fragmentsleam:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f100057
com.example.fragmentsleam:drawable/abc_btn_default_mtrl_shape = 0x7f070031
com.example.fragmentsleam:style/ThemeOverlay.Material3.SplitButton.IconButton.Filled = 0x7f100300
com.example.fragmentsleam:id/image = 0x7f0800eb
com.example.fragmentsleam:drawable/abc_btn_colored_material = 0x7f070030
com.example.fragmentsleam:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002f
com.example.fragmentsleam:color/mtrl_navigation_item_text_color = 0x7f050383
com.example.fragmentsleam:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070029
com.example.fragmentsleam:attr/expandedHeight = 0x7f0301de
com.example.fragmentsleam:id/closest = 0x7f08007e
com.example.fragmentsleam:drawable/abc_btn_check_material = 0x7f07002c
com.example.fragmentsleam:attr/expandedTitleMarginBottom = 0x7f0301e8
com.example.fragmentsleam:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070027
com.example.fragmentsleam:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070024
com.example.fragmentsleam:attr/layout_constraintHeight_percent = 0x7f0302d8
com.example.fragmentsleam:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070023
com.example.fragmentsleam:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f1002a5
com.example.fragmentsleam:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070021
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001e
com.example.fragmentsleam:style/TextAppearance.AppCompat = 0x7f1001ba
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001c
com.example.fragmentsleam:string/error_a11y_label = 0x7f0f002e
com.example.fragmentsleam:drawable/abc_ic_ab_back_material = 0x7f07003e
com.example.fragmentsleam:attr/pressedTranslationZ = 0x7f0303e6
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070019
com.example.fragmentsleam:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f10024e
com.example.fragmentsleam:integer/mtrl_calendar_header_orientation = 0x7f090033
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070018
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070016
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070014
com.example.fragmentsleam:drawable/abc_tab_indicator_material = 0x7f07006c
com.example.fragmentsleam:styleable/CheckedTextView = 0x7f11001c
com.example.fragmentsleam:styleable/StateSet = 0x7f11008b
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070013
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f070010
com.example.fragmentsleam:style/TextAppearance.AppCompat.Caption = 0x7f1001be
com.example.fragmentsleam:string/abc_activity_chooser_view_see_all = 0x7f0f0004
com.example.fragmentsleam:drawable/$m3_avd_hide_password__2 = 0x7f070009
com.example.fragmentsleam:drawable/$m3_avd_hide_password__0 = 0x7f070007
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.NavBar.Container.Shape = 0x7f100178
com.example.fragmentsleam:macro/m3_comp_list_list_item_selected_container_color = 0x7f0c00d0
com.example.fragmentsleam:attr/tabTextColor = 0x7f030498
com.example.fragmentsleam:drawable/$ic_launcher_foreground__0 = 0x7f070006
com.example.fragmentsleam:drawable/$avd_show_password__1 = 0x7f070004
com.example.fragmentsleam:drawable/$avd_hide_password__2 = 0x7f070002
com.example.fragmentsleam:macro/m3_comp_button_outlined_pressed_label_text_color = 0x7f0c0036
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f0602e2
com.example.fragmentsleam:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f090021
com.example.fragmentsleam:drawable/$avd_hide_password__1 = 0x7f070001
com.example.fragmentsleam:drawable/$avd_hide_password__0 = 0x7f070000
com.example.fragmentsleam:dimen/tooltip_y_offset_touch = 0x7f060406
com.example.fragmentsleam:color/m3_sys_color_primary_fixed = 0x7f050293
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f0602e1
com.example.fragmentsleam:dimen/tooltip_y_offset_non_touch = 0x7f060405
com.example.fragmentsleam:attr/trackInnerCornerRadius = 0x7f03053b
com.example.fragmentsleam:dimen/tooltip_vertical_padding = 0x7f060404
com.example.fragmentsleam:attr/tabInlineLabel = 0x7f030489
com.example.fragmentsleam:dimen/notification_top_pad_large_text = 0x7f0603fe
com.example.fragmentsleam:style/Platform.AppCompat = 0x7f100140
com.example.fragmentsleam:string/mtrl_timepicker_cancel = 0x7f0f009e
com.example.fragmentsleam:dimen/notification_subtext_size = 0x7f0603fc
com.example.fragmentsleam:color/m3_navigation_rail_ripple_color_selector = 0x7f05009a
com.example.fragmentsleam:style/Theme.AppCompat.Dialog.MinWidth = 0x7f10025b
com.example.fragmentsleam:dimen/notification_right_side_padding_top = 0x7f0603f9
com.example.fragmentsleam:dimen/notification_right_icon_size = 0x7f0603f8
com.example.fragmentsleam:dimen/notification_media_narrow_margin = 0x7f0603f7
com.example.fragmentsleam:attr/checkboxStyle = 0x7f0300b6
com.example.fragmentsleam:dimen/notification_main_column_padding_top = 0x7f0603f6
com.example.fragmentsleam:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0c01a2
com.example.fragmentsleam:dimen/notification_action_text_size = 0x7f0603f1
com.example.fragmentsleam:style/ShapeAppearance.Material3.MediumComponent = 0x7f10019d
com.example.fragmentsleam:dimen/mtrl_tooltip_padding = 0x7f0603ee
com.example.fragmentsleam:id/start_center = 0x7f0801c5
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface_variant = 0x7f05025c
com.example.fragmentsleam:dimen/mtrl_tooltip_minWidth = 0x7f0603ed
com.example.fragmentsleam:dimen/mtrl_tooltip_arrowSize = 0x7f0603ea
com.example.fragmentsleam:dimen/mtrl_toolbar_default_height = 0x7f0603e9
com.example.fragmentsleam:dimen/mtrl_textinput_end_icon_margin_start = 0x7f0603e6
com.example.fragmentsleam:id/title_template = 0x7f0801ef
com.example.fragmentsleam:dimen/mtrl_textinput_counter_margin_start = 0x7f0603e5
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f10040d
com.example.fragmentsleam:id/fullscreen_header = 0x7f0800d3
com.example.fragmentsleam:attr/colorSurfaceContainerLowest = 0x7f030131
com.example.fragmentsleam:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f0603e2
com.example.fragmentsleam:attr/lStar = 0x7f0302af
com.example.fragmentsleam:dimen/mtrl_textinput_box_corner_radius_small = 0x7f0603e1
com.example.fragmentsleam:dimen/mtrl_switch_thumb_size = 0x7f0603dd
com.example.fragmentsleam:dimen/mtrl_switch_thumb_icon_size = 0x7f0603dc
com.example.fragmentsleam:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f060109
com.example.fragmentsleam:dimen/mtrl_switch_thumb_elevation = 0x7f0603db
com.example.fragmentsleam:drawable/abc_btn_borderless_material = 0x7f07002b
com.example.fragmentsleam:dimen/mtrl_switch_text_padding = 0x7f0603da
com.example.fragmentsleam:dimen/mtrl_snackbar_padding_horizontal = 0x7f0603d9
com.example.fragmentsleam:attr/layout_constraintGuide_percent = 0x7f0302d3
com.example.fragmentsleam:style/TextAppearance.Material3.LabelMedium = 0x7f10022e
com.example.fragmentsleam:attr/badgeFixedEdge = 0x7f030052
com.example.fragmentsleam:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0603d8
com.example.fragmentsleam:style/Base.Theme.Material3.Light.Dialog = 0x7f100061
com.example.fragmentsleam:dimen/m3_comp_icon_button_small_narrow_trailing_space = 0x7f060182
com.example.fragmentsleam:color/m3_dark_highlighted_text = 0x7f050077
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f100151
com.example.fragmentsleam:dimen/mtrl_snackbar_margin = 0x7f0603d7
com.example.fragmentsleam:dimen/mtrl_snackbar_background_corner_radius = 0x7f0603d5
com.example.fragmentsleam:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0603d4
com.example.fragmentsleam:integer/mtrl_switch_track_viewport_width = 0x7f090040
com.example.fragmentsleam:dimen/mtrl_slider_widget_height = 0x7f0603d3
com.example.fragmentsleam:dimen/mtrl_slider_track_side_padding = 0x7f0603d2
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1001e5
com.example.fragmentsleam:dimen/mtrl_slider_track_height = 0x7f0603d1
com.example.fragmentsleam:style/TextAppearance.AppCompat.Medium = 0x7f1001cb
com.example.fragmentsleam:dimen/mtrl_slider_tick_radius = 0x7f0603d0
com.example.fragmentsleam:dimen/mtrl_slider_thumb_radius = 0x7f0603ce
com.example.fragmentsleam:style/Widget.Material3.FloatingToolbar.TextButton = 0x7f1003f1
com.example.fragmentsleam:dimen/mtrl_slider_thumb_elevation = 0x7f0603cd
com.example.fragmentsleam:id/spread_inside = 0x7f0801ba
com.example.fragmentsleam:id/search_close_btn = 0x7f08019c
com.example.fragmentsleam:dimen/mtrl_slider_label_square_side = 0x7f0603cc
com.example.fragmentsleam:dimen/mtrl_slider_label_radius = 0x7f0603cb
com.example.fragmentsleam:dimen/mtrl_slider_halo_radius = 0x7f0603c9
com.example.fragmentsleam:dimen/mtrl_shape_corner_size_small_component = 0x7f0603c8
com.example.fragmentsleam:string/mtrl_picker_a11y_prev_month = 0x7f0f0070
com.example.fragmentsleam:dimen/mtrl_shape_corner_size_medium_component = 0x7f0603c7
com.example.fragmentsleam:styleable/MenuGroup = 0x7f110064
com.example.fragmentsleam:style/Base.Widget.AppCompat.Button.Colored = 0x7f1000cf
com.example.fragmentsleam:id/linearLayout2 = 0x7f080105
com.example.fragmentsleam:dimen/mtrl_shape_corner_size_large_component = 0x7f0603c6
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary98 = 0x7f050103
com.example.fragmentsleam:id/group_divider = 0x7f0800da
com.example.fragmentsleam:dimen/mtrl_progress_circular_size_small = 0x7f0603c0
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_error = 0x7f05021c
com.example.fragmentsleam:dimen/mtrl_progress_circular_size_medium = 0x7f0603bf
com.example.fragmentsleam:dimen/mtrl_progress_circular_size_extra_small = 0x7f0603be
com.example.fragmentsleam:style/TextAppearance.AppCompat.Tooltip = 0x7f1001d6
com.example.fragmentsleam:macro/m3_comp_button_elevated_icon_color = 0x7f0c0015
com.example.fragmentsleam:dimen/mtrl_progress_circular_size = 0x7f0603bd
com.example.fragmentsleam:dimen/mtrl_progress_circular_inset_medium = 0x7f0603ba
com.example.fragmentsleam:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0603b9
com.example.fragmentsleam:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0603b6
com.example.fragmentsleam:dimen/hint_alpha_material_dark = 0x7f060098
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.NavBar.Item.ActiveIndicator.Shape = 0x7f100179
com.example.fragmentsleam:dimen/mtrl_navigation_rail_margin = 0x7f0603b5
com.example.fragmentsleam:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1002c9
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Subhead = 0x7f10002d
com.example.fragmentsleam:dimen/mtrl_navigation_rail_elevation = 0x7f0603b2
com.example.fragmentsleam:dimen/mtrl_navigation_rail_compact_width = 0x7f0603b0
com.example.fragmentsleam:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0603ad
com.example.fragmentsleam:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600b1
com.example.fragmentsleam:id/action_mode_bar_stub = 0x7f080042
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f10032a
com.example.fragmentsleam:dimen/mtrl_navigation_item_icon_padding = 0x7f0603ab
com.example.fragmentsleam:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0603aa
com.example.fragmentsleam:style/Platform.AppCompat.Light = 0x7f100141
com.example.fragmentsleam:attr/collapsingToolbarLayoutStyle = 0x7f0300f9
com.example.fragmentsleam:dimen/mtrl_navigation_elevation = 0x7f0603a9
com.example.fragmentsleam:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0603a7
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f0602d5
com.example.fragmentsleam:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f05026a
com.example.fragmentsleam:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f10009d
com.example.fragmentsleam:color/m3_ref_palette_blue100 = 0x7f0500a2
com.example.fragmentsleam:dimen/cardview_compat_inset_shadow = 0x7f060052
com.example.fragmentsleam:dimen/mtrl_min_touch_target_size = 0x7f0603a6
com.example.fragmentsleam:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0603a4
com.example.fragmentsleam:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0c018d
com.example.fragmentsleam:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007d
com.example.fragmentsleam:dimen/mtrl_low_ripple_focused_alpha = 0x7f0603a3
com.example.fragmentsleam:drawable/mtrl_navigation_bar_item_background = 0x7f0700cb
com.example.fragmentsleam:dimen/compat_notification_large_icon_max_height = 0x7f06005b
com.example.fragmentsleam:dimen/mtrl_low_ripple_default_alpha = 0x7f0603a2
com.example.fragmentsleam:id/buttonPanel = 0x7f080069
com.example.fragmentsleam:drawable/btn_checkbox_checked_mtrl = 0x7f07007a
com.example.fragmentsleam:dimen/m3_comp_search_bar_container_elevation = 0x7f0601ed
com.example.fragmentsleam:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0603a0
com.example.fragmentsleam:dimen/mtrl_high_ripple_focused_alpha = 0x7f06039f
com.example.fragmentsleam:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f06039c
com.example.fragmentsleam:dimen/mtrl_fab_min_touch_target = 0x7f06039b
com.example.fragmentsleam:attr/daySelectedStyle = 0x7f03018b
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1001e1
com.example.fragmentsleam:id/navigation_bar_item_content_container = 0x7f080145
com.example.fragmentsleam:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f060399
com.example.fragmentsleam:dimen/mtrl_extended_fab_top_padding = 0x7f060396
com.example.fragmentsleam:color/m3_slider_halo_color_legacy = 0x7f0501ed
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1001fa
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f100132
com.example.fragmentsleam:style/Widget.AppCompat.ActionButton = 0x7f100337
com.example.fragmentsleam:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f060391
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_active_indicator_color = 0x7f0c00d9
com.example.fragmentsleam:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700ba
com.example.fragmentsleam:id/transition_position = 0x7f0801fc
com.example.fragmentsleam:attr/elevationOverlayEnabled = 0x7f0301bf
com.example.fragmentsleam:dimen/mtrl_extended_fab_icon_size = 0x7f060390
com.example.fragmentsleam:attr/colorOutlineVariant = 0x7f03011d
com.example.fragmentsleam:attr/floatingActionButtonSurfaceStyle = 0x7f030218
com.example.fragmentsleam:style/Base.V26.Theme.AppCompat.Light = 0x7f1000b4
com.example.fragmentsleam:dimen/mtrl_extended_fab_end_padding_icon = 0x7f06038f
com.example.fragmentsleam:dimen/mtrl_extended_fab_end_padding = 0x7f06038e
com.example.fragmentsleam:style/ThemeOverlay.AppCompat = 0x7f1002ba
com.example.fragmentsleam:integer/m3_sys_motion_duration_short1 = 0x7f09001b
com.example.fragmentsleam:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f06038c
com.example.fragmentsleam:dimen/mtrl_extended_fab_bottom_padding = 0x7f06038a
com.example.fragmentsleam:attr/listPreferredItemPaddingStart = 0x7f030317
com.example.fragmentsleam:color/m3_ref_palette_grey_variant20 = 0x7f05014c
com.example.fragmentsleam:id/wrap = 0x7f080214
com.example.fragmentsleam:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f060389
com.example.fragmentsleam:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f060388
com.example.fragmentsleam:attr/itemPaddingTop = 0x7f03029a
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f100028
com.example.fragmentsleam:string/mtrl_checkbox_button_path_name = 0x7f0f0067
com.example.fragmentsleam:drawable/material_ic_clear_black_24dp = 0x7f0700ad
com.example.fragmentsleam:color/mtrl_indicator_text_color = 0x7f05037c
com.example.fragmentsleam:dimen/mtrl_card_checked_icon_margin = 0x7f06037f
com.example.fragmentsleam:dimen/mtrl_calendar_year_width = 0x7f06037e
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f10020e
com.example.fragmentsleam:color/m3_sys_color_on_secondary_fixed_variant = 0x7f050290
com.example.fragmentsleam:dimen/mtrl_calendar_year_vertical_padding = 0x7f06037d
com.example.fragmentsleam:style/Theme.Material3.DayNight = 0x7f100275
com.example.fragmentsleam:style/Widget.Material3.CardView.Outlined = 0x7f1003af
com.example.fragmentsleam:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700b3
com.example.fragmentsleam:anim/abc_slide_in_bottom = 0x7f010006
com.example.fragmentsleam:dimen/m3_comp_button_large_trailing_space = 0x7f060119
com.example.fragmentsleam:dimen/mtrl_calendar_year_height = 0x7f06037b
com.example.fragmentsleam:id/accessibility_custom_action_14 = 0x7f080017
com.example.fragmentsleam:layout/design_layout_snackbar = 0x7f0b0020
com.example.fragmentsleam:dimen/mtrl_calendar_text_input_padding_top = 0x7f060377
com.example.fragmentsleam:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f06010a
com.example.fragmentsleam:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f060375
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f100412
com.example.fragmentsleam:dimen/mtrl_calendar_navigation_height = 0x7f060371
com.example.fragmentsleam:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f06036d
com.example.fragmentsleam:dimen/mtrl_calendar_landscape_header_width = 0x7f06036c
com.example.fragmentsleam:dimen/mtrl_calendar_header_text_padding = 0x7f060369
com.example.fragmentsleam:attr/materialIconButtonStyle = 0x7f030349
com.example.fragmentsleam:dimen/mtrl_calendar_header_selection_line_height = 0x7f060368
com.example.fragmentsleam:dimen/mtrl_calendar_header_height = 0x7f060366
com.example.fragmentsleam:dimen/mtrl_calendar_header_divider_thickness = 0x7f060365
com.example.fragmentsleam:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f060364
com.example.fragmentsleam:dimen/mtrl_calendar_dialog_background_inset = 0x7f060362
com.example.fragmentsleam:attr/fabAlignmentMode = 0x7f0301fb
com.example.fragmentsleam:dimen/mtrl_calendar_days_of_week_height = 0x7f060361
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1001fc
com.example.fragmentsleam:id/mtrl_calendar_main_pane = 0x7f080130
com.example.fragmentsleam:dimen/mtrl_calendar_day_width = 0x7f060360
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f10031d
com.example.fragmentsleam:style/Platform.V21.AppCompat.Light = 0x7f10014a
com.example.fragmentsleam:dimen/mtrl_calendar_day_today_stroke = 0x7f06035e
com.example.fragmentsleam:id/direct = 0x7f08009e
com.example.fragmentsleam:dimen/mtrl_calendar_day_horizontal_padding = 0x7f06035d
com.example.fragmentsleam:attr/arrowHeadLength = 0x7f030039
com.example.fragmentsleam:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f0601fd
com.example.fragmentsleam:dimen/mtrl_calendar_day_height = 0x7f06035c
com.example.fragmentsleam:macro/m3_comp_badge_large_label_text_color = 0x7f0c0011
com.example.fragmentsleam:dimen/mtrl_calendar_content_padding = 0x7f06035a
com.example.fragmentsleam:dimen/mtrl_calendar_bottom_padding = 0x7f060359
com.example.fragmentsleam:dimen/mtrl_calendar_action_padding = 0x7f060358
com.example.fragmentsleam:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f060356
com.example.fragmentsleam:dimen/m3_comp_app_bar_container_elevation = 0x7f0600ff
com.example.fragmentsleam:dimen/mtrl_btn_z = 0x7f060355
com.example.fragmentsleam:style/Theme.AppCompat.Light.NoActionBar = 0x7f100264
com.example.fragmentsleam:string/abc_searchview_description_clear = 0x7f0f0013
com.example.fragmentsleam:dimen/mtrl_btn_text_btn_padding_right = 0x7f060353
com.example.fragmentsleam:dimen/mtrl_btn_text_btn_icon_padding = 0x7f060351
com.example.fragmentsleam:layout/mtrl_alert_dialog_actions = 0x7f0b0048
com.example.fragmentsleam:id/gone = 0x7f0800d6
com.example.fragmentsleam:dimen/mtrl_btn_stroke_size = 0x7f060350
com.example.fragmentsleam:dimen/mtrl_slider_label_padding = 0x7f0603ca
com.example.fragmentsleam:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f06034f
com.example.fragmentsleam:id/exitUntilCollapsed = 0x7f0800bc
com.example.fragmentsleam:dimen/mtrl_btn_pressed_z = 0x7f06034e
com.example.fragmentsleam:dimen/mtrl_btn_padding_right = 0x7f06034c
com.example.fragmentsleam:id/unlabeled = 0x7f080202
com.example.fragmentsleam:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1000e0
com.example.fragmentsleam:attr/expandedMinWidth = 0x7f0301e3
com.example.fragmentsleam:dimen/mtrl_btn_icon_padding = 0x7f060346
com.example.fragmentsleam:attr/chipSurfaceColor = 0x7f0300d5
com.example.fragmentsleam:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060345
com.example.fragmentsleam:color/material_on_surface_stroke = 0x7f050322
com.example.fragmentsleam:dimen/mtrl_btn_disabled_z = 0x7f060341
com.example.fragmentsleam:dimen/mtrl_btn_dialog_btn_min_width = 0x7f06033f
com.example.fragmentsleam:dimen/mtrl_bottomappbar_height = 0x7f06033d
com.example.fragmentsleam:macro/m3_comp_button_small_label_text = 0x7f0c0054
com.example.fragmentsleam:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0c014c
com.example.fragmentsleam:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f06033b
com.example.fragmentsleam:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f050350
com.example.fragmentsleam:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f060339
com.example.fragmentsleam:dimen/mtrl_badge_with_text_size = 0x7f060337
com.example.fragmentsleam:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1000dc
com.example.fragmentsleam:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f060336
com.example.fragmentsleam:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f10044b
com.example.fragmentsleam:style/Widget.Material3.Button.ElevatedButton = 0x7f10039b
com.example.fragmentsleam:integer/material_motion_duration_short_2 = 0x7f09002e
com.example.fragmentsleam:attr/fabAlignmentModeEndMargin = 0x7f0301fc
com.example.fragmentsleam:dimen/mtrl_badge_size = 0x7f060332
com.example.fragmentsleam:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f060331
com.example.fragmentsleam:mipmap/ic_launcher_round = 0x7f0d0001
com.example.fragmentsleam:dimen/mtrl_badge_horizontal_edge_offset = 0x7f060330
com.example.fragmentsleam:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f06032f
com.example.fragmentsleam:dimen/mtrl_alert_dialog_background_inset_end = 0x7f06032c
com.example.fragmentsleam:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300f8
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary10 = 0x7f050114
com.example.fragmentsleam:drawable/abc_list_focused_holo = 0x7f07004f
com.example.fragmentsleam:id/month_navigation_fragment_toggle = 0x7f080128
com.example.fragmentsleam:dimen/material_time_picker_minimum_screen_width = 0x7f06032a
com.example.fragmentsleam:dimen/material_time_picker_minimum_screen_height = 0x7f060329
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f100136
com.example.fragmentsleam:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0c0195
com.example.fragmentsleam:dimen/material_textinput_max_width = 0x7f060326
com.example.fragmentsleam:id/x_left = 0x7f080217
com.example.fragmentsleam:id/textinput_placeholder = 0x7f0801e9
com.example.fragmentsleam:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060324
com.example.fragmentsleam:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f060322
com.example.fragmentsleam:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f060320
com.example.fragmentsleam:attr/customBoolean = 0x7f03017f
com.example.fragmentsleam:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f06031f
com.example.fragmentsleam:attr/layout_editor_absoluteX = 0x7f0302f1
com.example.fragmentsleam:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f06031d
com.example.fragmentsleam:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003d
com.example.fragmentsleam:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f06031c
com.example.fragmentsleam:id/open_search_view_header_container = 0x7f080165
com.example.fragmentsleam:dimen/material_emphasis_disabled_background = 0x7f060318
com.example.fragmentsleam:style/Widget.MaterialComponents.BottomAppBar = 0x7f10045d
com.example.fragmentsleam:dimen/material_divider_thickness = 0x7f060316
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f1002b2
com.example.fragmentsleam:dimen/material_cursor_inset = 0x7f060314
com.example.fragmentsleam:style/Widget.MaterialComponents.Chip.Entry = 0x7f100475
com.example.fragmentsleam:dimen/material_clock_period_toggle_width = 0x7f060312
com.example.fragmentsleam:dimen/material_clock_period_toggle_vertical_gap = 0x7f060311
com.example.fragmentsleam:attr/marginRightSystemWindowInsets = 0x7f030320
com.example.fragmentsleam:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1002d4
com.example.fragmentsleam:dimen/material_clock_period_toggle_horizontal_gap = 0x7f060310
com.example.fragmentsleam:dimen/material_clock_period_toggle_height = 0x7f06030f
com.example.fragmentsleam:dimen/material_clock_number_text_size = 0x7f06030e
com.example.fragmentsleam:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0603e0
com.example.fragmentsleam:dimen/material_clock_face_margin_top = 0x7f06030a
com.example.fragmentsleam:style/Base.Widget.AppCompat.ButtonBar = 0x7f1000d1
com.example.fragmentsleam:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060374
com.example.fragmentsleam:dimen/material_clock_display_width = 0x7f060308
com.example.fragmentsleam:attr/buttonPanelSideLayout = 0x7f030097
com.example.fragmentsleam:id/SHOW_PROGRESS = 0x7f08000a
com.example.fragmentsleam:dimen/material_clock_display_height = 0x7f060306
com.example.fragmentsleam:id/transition_image_transform = 0x7f0801f9
com.example.fragmentsleam:dimen/m3_timepicker_display_stroke_width = 0x7f060302
com.example.fragmentsleam:color/dim_foreground_disabled_material_dark = 0x7f050056
com.example.fragmentsleam:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f0602fe
com.example.fragmentsleam:layout/design_layout_tab_icon = 0x7f0b0022
com.example.fragmentsleam:attr/onPositiveCross = 0x7f0303b9
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_slow_spatial_stiffness = 0x7f0602f4
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_slow_spatial_damping = 0x7f0602f3
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_slow_effects_damping = 0x7f0602f1
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_fast_spatial_damping = 0x7f0602ef
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary30 = 0x7f050109
com.example.fragmentsleam:id/top = 0x7f0801f1
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_fast_effects_stiffness = 0x7f0602ee
com.example.fragmentsleam:id/material_clock_period_toggle = 0x7f080115
com.example.fragmentsleam:drawable/ic_search_black_24 = 0x7f07009d
com.example.fragmentsleam:dimen/m3_navigation_rail_item_padding_top = 0x7f06029b
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_default_spatial_stiffness = 0x7f0602ec
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_default_spatial_damping = 0x7f0602eb
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f0602e5
com.example.fragmentsleam:macro/m3_comp_nav_bar_container_color = 0x7f0c00d5
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f100216
com.example.fragmentsleam:style/Theme.MaterialComponents.NoActionBar = 0x7f1002b8
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f0602e4
com.example.fragmentsleam:attr/waveSpeed = 0x7f030561
com.example.fragmentsleam:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1000ec
com.example.fragmentsleam:attr/actionProviderClass = 0x7f030023
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f0602df
com.example.fragmentsleam:color/m3_button_foreground_color_selector = 0x7f050065
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f0602de
com.example.fragmentsleam:dimen/m3_comp_toolbar_standard_disabled_label_text_opacity = 0x7f06025a
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f0602dd
com.example.fragmentsleam:attr/textAppearanceHeadline4 = 0x7f0304b2
com.example.fragmentsleam:attr/actionModeTheme = 0x7f03001f
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f0602d4
com.example.fragmentsleam:dimen/m3_sys_elevation_level3 = 0x7f0602c2
com.example.fragmentsleam:color/m3_ref_palette_primary20 = 0x7f050199
com.example.fragmentsleam:attr/actionOverflowButtonStyle = 0x7f030021
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f0602d2
com.example.fragmentsleam:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f100483
com.example.fragmentsleam:attr/colorPrimary = 0x7f03011e
com.example.fragmentsleam:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0602cc
com.example.fragmentsleam:color/m3_ref_palette_grey_variant10 = 0x7f05014a
com.example.fragmentsleam:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0602ca
com.example.fragmentsleam:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f0602d9
com.example.fragmentsleam:id/action_divider = 0x7f08003d
com.example.fragmentsleam:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0602c8
com.example.fragmentsleam:style/Widget.Material3.Chip.Suggestion = 0x7f1003b9
com.example.fragmentsleam:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0602c6
com.example.fragmentsleam:id/north = 0x7f080155
com.example.fragmentsleam:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0602c5
com.example.fragmentsleam:dimen/material_textinput_default_width = 0x7f060325
com.example.fragmentsleam:color/material_personalized_primary_inverse_text_disable_only = 0x7f050355
com.example.fragmentsleam:interpolator/mtrl_linear_out_slow_in = 0x7f0a0011
com.example.fragmentsleam:dimen/m3_sys_elevation_level2 = 0x7f0602c1
com.example.fragmentsleam:styleable/CircularProgressIndicator = 0x7f11001f
com.example.fragmentsleam:attr/innerCornerSize = 0x7f030286
com.example.fragmentsleam:color/m3_ref_palette_pink95 = 0x7f050194
com.example.fragmentsleam:dimen/m3_sys_elevation_level0 = 0x7f0602bf
com.example.fragmentsleam:dimen/m3_snackbar_margin = 0x7f0602be
com.example.fragmentsleam:layout/design_navigation_item_separator = 0x7f0b0027
com.example.fragmentsleam:style/Base.Widget.Material3.ActionMode = 0x7f1000fc
com.example.fragmentsleam:dimen/m3_small_fab_max_image_size = 0x7f0602bb
com.example.fragmentsleam:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f060301
com.example.fragmentsleam:attr/layout_behavior = 0x7f0302bf
com.example.fragmentsleam:attr/endIconContentDescription = 0x7f0301c3
com.example.fragmentsleam:dimen/m3_slider_track_icon_padding = 0x7f0602ba
com.example.fragmentsleam:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f10019e
com.example.fragmentsleam:dimen/m3_simple_item_color_hovered_alpha = 0x7f0602b7
com.example.fragmentsleam:color/material_dynamic_neutral99 = 0x7f0502d9
com.example.fragmentsleam:dimen/m3_side_sheet_standard_elevation = 0x7f0602b5
com.example.fragmentsleam:dimen/m3_searchview_divider_size = 0x7f0602b0
com.example.fragmentsleam:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f100083
com.example.fragmentsleam:attr/mock_showLabel = 0x7f030374
com.example.fragmentsleam:dimen/m3_searchbar_margin_vertical = 0x7f0602ab
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f100073
com.example.fragmentsleam:color/m3_ref_palette_blue60 = 0x7f0500a7
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_extra_large_increased = 0x7f0602f7
com.example.fragmentsleam:dimen/m3_searchbar_margin_horizontal = 0x7f0602aa
com.example.fragmentsleam:attr/chipMinHeight = 0x7f0300cb
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.Snackbar = 0x7f10011f
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1001dd
com.example.fragmentsleam:dimen/m3_comp_time_picker_container_elevation = 0x7f060249
com.example.fragmentsleam:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0602a7
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_inactive_hovered_state_layer_color = 0x7f0c00ec
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f10032b
com.example.fragmentsleam:dimen/m3_navigation_subheader_vertical_padding = 0x7f0602a2
com.example.fragmentsleam:style/Widget.MaterialComponents.FloatingActionButton = 0x7f100482
com.example.fragmentsleam:style/Widget.Material3.AppBarLayout = 0x7f10038a
com.example.fragmentsleam:attr/layout_goneMarginBaseline = 0x7f0302f3
com.example.fragmentsleam:id/item_name = 0x7f0800f7
com.example.fragmentsleam:dimen/m3_navigation_subheader_top_margin = 0x7f0602a1
com.example.fragmentsleam:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f06029a
com.example.fragmentsleam:dimen/m3_navigation_rail_icon_label_padding = 0x7f060293
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f100023
com.example.fragmentsleam:attr/layout_goneMarginRight = 0x7f0302f7
com.example.fragmentsleam:attr/materialCardViewElevatedStyle = 0x7f03033d
com.example.fragmentsleam:dimen/m3_navigation_rail_expanded_leading_trailing_space = 0x7f060291
com.example.fragmentsleam:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f100441
com.example.fragmentsleam:dimen/m3_navigation_rail_expanded_item_spacing = 0x7f060290
com.example.fragmentsleam:macro/m3_comp_suggestion_chip_container_shape = 0x7f0c0182
com.example.fragmentsleam:dimen/m3_navigation_rail_elevation = 0x7f06028e
com.example.fragmentsleam:color/mtrl_filled_icon_tint = 0x7f05037a
com.example.fragmentsleam:attr/floatingActionButtonSmallTertiaryStyle = 0x7f030216
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_default_effects_damping = 0x7f0602e9
com.example.fragmentsleam:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f06028c
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f10046e
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f100097
com.example.fragmentsleam:attr/layout_editor_absoluteY = 0x7f0302f2
com.example.fragmentsleam:color/material_divider_color = 0x7f0502c4
com.example.fragmentsleam:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f06028b
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Display4 = 0x7f10001e
com.example.fragmentsleam:attr/tickRadiusActive = 0x7f030506
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary95 = 0x7f050110
com.example.fragmentsleam:attr/showTitle = 0x7f030433
com.example.fragmentsleam:dimen/m3_navigation_item_shape_inset_top = 0x7f060289
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0c0168
com.example.fragmentsleam:dimen/m3_navigation_item_shape_inset_start = 0x7f060288
com.example.fragmentsleam:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1001ef
com.example.fragmentsleam:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a7
com.example.fragmentsleam:dimen/m3_navigation_item_shape_inset_end = 0x7f060287
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1001e9
com.example.fragmentsleam:attr/minTouchTargetSize = 0x7f03036d
com.example.fragmentsleam:dimen/m3_navigation_item_shape_inset_bottom = 0x7f060286
com.example.fragmentsleam:attr/motionEasingDecelerated = 0x7f030387
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f100299
com.example.fragmentsleam:dimen/m3_navigation_item_leading_trailing_space = 0x7f060285
com.example.fragmentsleam:style/TextAppearance.Compat.Notification.Line2 = 0x7f1001ec
com.example.fragmentsleam:string/abc_menu_meta_shortcut_label = 0x7f0f000d
com.example.fragmentsleam:attr/behavior_hideable = 0x7f03006f
com.example.fragmentsleam:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f060281
com.example.fragmentsleam:dimen/design_tab_scrollable_min_width = 0x7f06008c
com.example.fragmentsleam:dimen/m3_menu_elevation = 0x7f06027a
com.example.fragmentsleam:dimen/m3_loading_indicator_shape_size = 0x7f060279
com.example.fragmentsleam:macro/m3_comp_extended_fab_surface_container_color = 0x7f0c0095
com.example.fragmentsleam:dimen/m3_large_text_vertical_offset_adjustment = 0x7f060277
com.example.fragmentsleam:dimen/m3_floatingtoolbar_min_width = 0x7f060274
com.example.fragmentsleam:dimen/m3_fab_translation_z_hovered = 0x7f060271
com.example.fragmentsleam:attr/containerShapePressed = 0x7f030151
com.example.fragmentsleam:dimen/m3_fab_translation_z_focused = 0x7f060270
com.example.fragmentsleam:attr/switchMinWidth = 0x7f030479
com.example.fragmentsleam:dimen/m3_comp_icon_button_medium_wide_leading_space = 0x7f06017c
com.example.fragmentsleam:dimen/m3_fab_disabled_translation_z = 0x7f06026e
com.example.fragmentsleam:animator/m3_extended_fab_show_motion_spec = 0x7f020015
com.example.fragmentsleam:id/dragStart = 0x7f0800aa
com.example.fragmentsleam:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f100425
com.example.fragmentsleam:color/material_dynamic_neutral95 = 0x7f0502d8
com.example.fragmentsleam:attr/indicatorDirectionLinear = 0x7f030281
com.example.fragmentsleam:color/m3_ref_palette_blue_variant0 = 0x7f0500ad
com.example.fragmentsleam:attr/motionProgress = 0x7f03039d
com.example.fragmentsleam:dimen/m3_fab_corner_size = 0x7f06026c
com.example.fragmentsleam:dimen/m3_extended_fab_translation_z_focused = 0x7f060268
com.example.fragmentsleam:dimen/m3_extended_fab_translation_z_base = 0x7f060267
com.example.fragmentsleam:attr/passwordToggleDrawable = 0x7f0303cd
com.example.fragmentsleam:dimen/m3_comp_fab_icon_size = 0x7f060153
com.example.fragmentsleam:color/m3_ref_palette_orange98 = 0x7f050188
com.example.fragmentsleam:dimen/m3_extended_fab_top_padding = 0x7f060266
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1003e8
com.example.fragmentsleam:style/Base.V22.Theme.AppCompat.Light = 0x7f1000ac
com.example.fragmentsleam:dimen/m3_extended_fab_bottom_padding = 0x7f06025f
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f10046a
com.example.fragmentsleam:dimen/m3_comp_search_view_container_elevation = 0x7f0601f1
com.example.fragmentsleam:dimen/m3_divider_heavy_thickness = 0x7f06025e
com.example.fragmentsleam:attr/chipCornerRadius = 0x7f0300c3
com.example.fragmentsleam:id/up = 0x7f080203
com.example.fragmentsleam:id/navigation_bar_item_active_indicator_view = 0x7f080144
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500d6
com.example.fragmentsleam:dimen/m3_comp_toolbar_vibrant_disabled_icon_opacity = 0x7f06025b
com.example.fragmentsleam:style/TextAppearance.Material3.LabelSmall = 0x7f100230
com.example.fragmentsleam:dimen/m3_comp_toolbar_floating_container_leading_space = 0x7f060255
com.example.fragmentsleam:dimen/m3_comp_toolbar_docked_container_height = 0x7f060251
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f100035
com.example.fragmentsleam:attr/colorOnSecondary = 0x7f030111
com.example.fragmentsleam:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f06024d
com.example.fragmentsleam:dimen/tooltip_precise_anchor_threshold = 0x7f060403
com.example.fragmentsleam:attr/textAppearanceTitleSmallEmphasized = 0x7f0304d2
com.example.fragmentsleam:attr/listMenuViewStyle = 0x7f03030f
com.example.fragmentsleam:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.example.fragmentsleam:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f060248
com.example.fragmentsleam:attr/actionModeBackground = 0x7f030012
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f1004ba
com.example.fragmentsleam:dimen/m3_comp_switch_track_width = 0x7f060244
com.example.fragmentsleam:layout/notification_template_part_chronometer = 0x7f0b006b
com.example.fragmentsleam:attr/boxStrokeErrorColor = 0x7f030088
com.example.fragmentsleam:attr/transformPivotTarget = 0x7f030542
com.example.fragmentsleam:dimen/m3_alert_dialog_action_bottom_padding = 0x7f06009f
com.example.fragmentsleam:attr/tintMode = 0x7f03050b
com.example.fragmentsleam:color/m3_timepicker_clock_text_color = 0x7f0502aa
com.example.fragmentsleam:attr/flow_firstVerticalBias = 0x7f03021e
com.example.fragmentsleam:color/design_error = 0x7f05004c
com.example.fragmentsleam:dimen/m3_comp_switch_track_height = 0x7f060243
com.example.fragmentsleam:attr/buttonIconTintMode = 0x7f030096
com.example.fragmentsleam:dimen/m3_navigation_divider_top_margin = 0x7f06027f
com.example.fragmentsleam:attr/transitionFlags = 0x7f030545
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f100317
com.example.fragmentsleam:id/material_clock_display_and_toggle = 0x7f08010f
com.example.fragmentsleam:attr/layout_constraintDimensionRatio = 0x7f0302ce
com.example.fragmentsleam:attr/haloColor = 0x7f03024e
com.example.fragmentsleam:dimen/m3_comp_button_xsmall_icon_label_space = 0x7f060130
com.example.fragmentsleam:dimen/m3_comp_switch_disabled_track_opacity = 0x7f06023d
com.example.fragmentsleam:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f060247
com.example.fragmentsleam:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f030389
com.example.fragmentsleam:style/ThemeOverlay.Material3.ActionBar = 0x7f1002c5
com.example.fragmentsleam:attr/closeIconSize = 0x7f0300e6
com.example.fragmentsleam:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f06023a
com.example.fragmentsleam:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070022
com.example.fragmentsleam:attr/materialButtonStyle = 0x7f03032b
com.example.fragmentsleam:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f060239
com.example.fragmentsleam:color/m3_ref_palette_green100 = 0x7f050131
com.example.fragmentsleam:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f060237
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0500f5
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f100311
com.example.fragmentsleam:dimen/m3_comp_split_button_xsmall_trailing_button_trailing_space = 0x7f060235
com.example.fragmentsleam:dimen/m3_comp_button_xlarge_trailing_space = 0x7f06012f
com.example.fragmentsleam:dimen/m3_comp_split_button_xsmall_trailing_button_leading_space = 0x7f060234
com.example.fragmentsleam:id/navigation_bar_item_large_label_view = 0x7f08014a
com.example.fragmentsleam:dimen/m3_comp_split_button_xsmall_leading_button_trailing_space = 0x7f060232
com.example.fragmentsleam:styleable/MotionLayout = 0x7f11006c
com.example.fragmentsleam:dimen/fastscroll_margin = 0x7f060093
com.example.fragmentsleam:id/imageView = 0x7f0800ed
com.example.fragmentsleam:attr/carousel_forwardTransition = 0x7f0300a9
com.example.fragmentsleam:string/abc_menu_ctrl_shortcut_label = 0x7f0f0009
com.example.fragmentsleam:attr/maxLines = 0x7f030360
com.example.fragmentsleam:dimen/m3_comp_split_button_xsmall_leading_button_leading_space = 0x7f060231
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f10048e
com.example.fragmentsleam:dimen/m3_comp_split_button_xlarge_trailing_button_icon_size = 0x7f06022e
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f100031
com.example.fragmentsleam:dimen/m3_comp_split_button_small_trailing_button_icon_size = 0x7f060229
com.example.fragmentsleam:drawable/btn_radio_off_mtrl = 0x7f07007e
com.example.fragmentsleam:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070028
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000e
com.example.fragmentsleam:dimen/m3_comp_split_button_small_leading_button_leading_space = 0x7f060227
com.example.fragmentsleam:dimen/m3_comp_split_button_medium_leading_button_trailing_space = 0x7f060223
com.example.fragmentsleam:dimen/m3_comp_split_button_medium_leading_button_leading_space = 0x7f060222
com.example.fragmentsleam:style/Widget.Material3.DockedToolbar.Button.Vibrant = 0x7f1003cd
com.example.fragmentsleam:dimen/m3_comp_split_button_large_trailing_button_icon_size = 0x7f06021f
com.example.fragmentsleam:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f0601f9
com.example.fragmentsleam:id/selection_type = 0x7f0801a5
com.example.fragmentsleam:color/mtrl_chip_close_icon_tint = 0x7f05036f
com.example.fragmentsleam:dimen/m3_comp_split_button_large_leading_button_trailing_space = 0x7f06021e
com.example.fragmentsleam:attr/clockFaceBackgroundColor = 0x7f0300df
com.example.fragmentsleam:dimen/m3_comp_split_button_large_leading_button_leading_space = 0x7f06021d
com.example.fragmentsleam:dimen/m3_comp_snackbar_container_elevation = 0x7f06021c
com.example.fragmentsleam:macro/m3_comp_button_tonal_icon_color = 0x7f0c0059
com.example.fragmentsleam:attr/fontProviderCerts = 0x7f030232
com.example.fragmentsleam:dimen/m3_comp_slider_xsmall_active_track_shape_leading = 0x7f06021b
com.example.fragmentsleam:style/Base.ThemeOverlay.AppCompat = 0x7f100078
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1001b6
com.example.fragmentsleam:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f100050
com.example.fragmentsleam:color/m3_ref_palette_secondary80 = 0x7f0501c7
com.example.fragmentsleam:dimen/m3_comp_slider_xsmall_active_track_height = 0x7f06021a
com.example.fragmentsleam:dimen/m3_comp_slider_xsmall_active_handle_height = 0x7f060219
com.example.fragmentsleam:dimen/m3_comp_button_xsmall_icon_size = 0x7f060131
com.example.fragmentsleam:dimen/m3_comp_slider_xlarge_icon_size = 0x7f060218
com.example.fragmentsleam:color/m3_ref_palette_grey100 = 0x7f05013e
com.example.fragmentsleam:color/m3_radiobutton_ripple_tint = 0x7f05009e
com.example.fragmentsleam:dimen/m3_comp_slider_xlarge_active_track_shape_leading = 0x7f060217
com.example.fragmentsleam:dimen/m3_comp_slider_xlarge_active_handle_height = 0x7f060215
com.example.fragmentsleam:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1002c7
com.example.fragmentsleam:anim/m3_bottom_sheet_slide_in = 0x7f010021
com.example.fragmentsleam:attr/circularflow_defaultAngle = 0x7f0300d9
com.example.fragmentsleam:dimen/m3_comp_slider_small_active_track_shape_leading = 0x7f060212
com.example.fragmentsleam:color/material_personalized_color_background = 0x7f050325
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_hovered_icon_color = 0x7f0c01d8
com.example.fragmentsleam:attr/textAppearanceListItemSecondary = 0x7f0304c4
com.example.fragmentsleam:dimen/m3_comp_slider_small_active_track_height = 0x7f060211
com.example.fragmentsleam:dimen/m3_comp_slider_small_active_handle_height = 0x7f060210
com.example.fragmentsleam:dimen/m3_comp_slider_large_icon_size = 0x7f06020b
com.example.fragmentsleam:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f10047e
com.example.fragmentsleam:style/Base.V7.Widget.AppCompat.EditText = 0x7f1000be
com.example.fragmentsleam:dimen/m3_comp_icon_button_large_narrow_trailing_space = 0x7f060172
com.example.fragmentsleam:dimen/m3_comp_slider_large_active_track_height = 0x7f060209
com.example.fragmentsleam:dimen/m3_comp_slider_inactive_stop_indicator_container_opacity = 0x7f060206
com.example.fragmentsleam:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060205
com.example.fragmentsleam:attr/fontProviderSystemFontFamily = 0x7f030237
com.example.fragmentsleam:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600b3
com.example.fragmentsleam:styleable/ConstraintOverride = 0x7f11002a
com.example.fragmentsleam:dimen/m3_comp_slider_active_stop_indicator_container_opacity = 0x7f060202
com.example.fragmentsleam:attr/customPixelDimension = 0x7f030186
com.example.fragmentsleam:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0c016f
com.example.fragmentsleam:id/tag_on_receive_content_mime_types = 0x7f0801d4
com.example.fragmentsleam:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f0601fb
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0c012b
com.example.fragmentsleam:macro/m3_comp_slider_inactive_track_color = 0x7f0c017b
com.example.fragmentsleam:dimen/mtrl_calendar_year_horizontal_padding = 0x7f06037c
com.example.fragmentsleam:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f0601fa
com.example.fragmentsleam:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f0601f8
com.example.fragmentsleam:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f0601f7
com.example.fragmentsleam:attr/tabIndicatorHeight = 0x7f030488
com.example.fragmentsleam:attr/counterTextAppearance = 0x7f030178
com.example.fragmentsleam:attr/contentInsetEnd = 0x7f030156
com.example.fragmentsleam:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f0601f5
com.example.fragmentsleam:styleable/ActionMenuItemView = 0x7f110002
com.example.fragmentsleam:style/Widget.Material3.TabLayout.OnSurface = 0x7f10043e
com.example.fragmentsleam:dimen/abc_dialog_padding_material = 0x7f060024
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_pressed_label_text_color = 0x7f0c01de
com.example.fragmentsleam:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f0601f3
com.example.fragmentsleam:style/Widget.Material3.SearchView.Prefix = 0x7f10042c
com.example.fragmentsleam:dimen/m3_comp_search_view_docked_header_container_height = 0x7f0601f2
com.example.fragmentsleam:dimen/m3_comp_nav_rail_collapsed_container_width = 0x7f0601a7
com.example.fragmentsleam:attr/actionBarTheme = 0x7f03000b
com.example.fragmentsleam:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f0601ea
com.example.fragmentsleam:dimen/mtrl_fab_translation_z_pressed = 0x7f06039d
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_linear_track_thickness = 0x7f0601e2
com.example.fragmentsleam:string/mtrl_picker_toggle_to_text_input_mode = 0x7f0f0092
com.example.fragmentsleam:style/ThemeOverlay.AppCompat.Light = 0x7f1002c2
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_linear_track_active_indicator_space = 0x7f0601e1
com.example.fragmentsleam:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f100309
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_linear_stop_indicator_size = 0x7f0601e0
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_linear_indeterminate_active_indicator_wave_wavelength = 0x7f0601df
com.example.fragmentsleam:styleable/KeyTimeCycle = 0x7f110048
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_linear_active_indicator_wave_wavelength = 0x7f0601de
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_circular_with_wave_size = 0x7f0601dc
com.example.fragmentsleam:id/touch_outside = 0x7f0801f4
com.example.fragmentsleam:attr/expandedTitleTextColor = 0x7f0301ee
com.example.fragmentsleam:attr/badgeWidePadding = 0x7f03005d
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_circular_track_thickness = 0x7f0601db
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f10018b
com.example.fragmentsleam:attr/materialSplitButtonStyle = 0x7f030354
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_circular_size = 0x7f0601d9
com.example.fragmentsleam:macro/m3_comp_icon_button_xlarge_selected_container_shape_round = 0x7f0c00ca
com.example.fragmentsleam:style/Widget.MaterialComponents.ProgressIndicator = 0x7f1004a9
com.example.fragmentsleam:id/save_non_transition_alpha = 0x7f08018f
com.example.fragmentsleam:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f0601d5
com.example.fragmentsleam:id/dependency_ordering = 0x7f080096
com.example.fragmentsleam:color/mtrl_choice_chip_ripple_color = 0x7f050373
com.example.fragmentsleam:dimen/m3_carousel_small_item_size_max = 0x7f0600f6
com.example.fragmentsleam:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f0601cf
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_inactive_label_text_color = 0x7f0c00ee
com.example.fragmentsleam:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0c01a3
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_pressed_state_layer_color = 0x7f0c01f6
com.example.fragmentsleam:dimen/m3_comp_outlined_card_outline_width = 0x7f0601c9
com.example.fragmentsleam:dimen/m3_comp_icon_button_xsmall_icon_size = 0x7f060190
com.example.fragmentsleam:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f1004a6
com.example.fragmentsleam:color/material_personalized_hint_foreground_inverse = 0x7f050354
com.example.fragmentsleam:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f10047b
com.example.fragmentsleam:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f06023c
com.example.fragmentsleam:dimen/m3_comp_outlined_card_icon_size = 0x7f0601c8
com.example.fragmentsleam:dimen/m3_comp_outlined_card_container_elevation = 0x7f0601c6
com.example.fragmentsleam:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f100431
com.example.fragmentsleam:attr/menu = 0x7f030366
com.example.fragmentsleam:attr/materialThemeOverlay = 0x7f030356
com.example.fragmentsleam:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f0601c4
com.example.fragmentsleam:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f0601c3
com.example.fragmentsleam:dimen/m3_bottom_nav_item_expanded_active_indicator_height = 0x7f0600c3
com.example.fragmentsleam:attr/tabSelectedTextColor = 0x7f030495
com.example.fragmentsleam:dimen/m3_comp_navigation_drawer_icon_size = 0x7f0601c1
com.example.fragmentsleam:attr/curveFit = 0x7f03017e
com.example.fragmentsleam:dimen/m3_comp_navigation_drawer_container_width = 0x7f0601be
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_vertical_icon_label_space = 0x7f0601bb
com.example.fragmentsleam:color/material_dynamic_neutral90 = 0x7f0502d7
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_vertical_active_indicator_width = 0x7f0601ba
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f100115
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_horizontal_full_width_trailing_space = 0x7f0601b5
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_container_vertical_space = 0x7f0601b1
com.example.fragmentsleam:color/m3_chip_ripple_color = 0x7f050072
com.example.fragmentsleam:attr/trackHeight = 0x7f030533
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_container_height = 0x7f0601b0
com.example.fragmentsleam:attr/actionMenuTextColor = 0x7f030011
com.example.fragmentsleam:attr/materialDisplayDividerStyle = 0x7f030343
com.example.fragmentsleam:attr/materialSearchViewPrefixStyle = 0x7f03034b
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f100161
com.example.fragmentsleam:id/contentPanel = 0x7f080085
com.example.fragmentsleam:attr/paddingStartSystemWindowInsets = 0x7f0303c6
com.example.fragmentsleam:dimen/m3_comp_nav_rail_collapsed_item_vertical_space = 0x7f0601a8
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_active_focused_state_layer_opacity = 0x7f0601ad
com.example.fragmentsleam:dimen/m3_comp_nav_rail_collapsed_container_elevation = 0x7f0601a6
com.example.fragmentsleam:id/accessibility_custom_action_20 = 0x7f08001e
com.example.fragmentsleam:dimen/m3_comp_nav_bar_container_height = 0x7f06019f
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f100076
com.example.fragmentsleam:attr/layout_constraintVertical_chainStyle = 0x7f0302e9
com.example.fragmentsleam:layout/material_timepicker_textinput_display = 0x7f0b0046
com.example.fragmentsleam:dimen/m3_btn_padding_bottom = 0x7f0600de
com.example.fragmentsleam:dimen/m3_comp_nav_bar_container_elevation = 0x7f06019e
com.example.fragmentsleam:color/m3_button_ripple_color = 0x7f050067
com.example.fragmentsleam:string/material_timepicker_pm = 0x7f0f005b
com.example.fragmentsleam:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060199
com.example.fragmentsleam:attr/trackColorActive = 0x7f03052c
com.example.fragmentsleam:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060198
com.example.fragmentsleam:dimen/m3_comp_icon_button_xsmall_wide_trailing_space = 0x7f060195
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.Display = 0x7f1004c2
com.example.fragmentsleam:dimen/m3_comp_icon_button_small_narrow_leading_space = 0x7f060181
com.example.fragmentsleam:color/m3_switch_thumb_tint = 0x7f0501f7
com.example.fragmentsleam:dimen/m3_comp_icon_button_small_icon_size = 0x7f060180
com.example.fragmentsleam:attr/trackColor = 0x7f03052b
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_focused_icon_color = 0x7f0c0046
com.example.fragmentsleam:dimen/m3_comp_input_chip_container_height = 0x7f060197
com.example.fragmentsleam:color/mtrl_filled_stroke_color = 0x7f05037b
com.example.fragmentsleam:color/abc_tint_switch_track = 0x7f050018
com.example.fragmentsleam:dimen/m3_comp_icon_button_medium_outlined_outline_width = 0x7f06017b
com.example.fragmentsleam:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060137
com.example.fragmentsleam:dimen/m3_comp_icon_button_medium_narrow_leading_space = 0x7f060179
com.example.fragmentsleam:dimen/m3_comp_icon_button_medium_default_trailing_space = 0x7f060177
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.Chip = 0x7f100117
com.example.fragmentsleam:dimen/m3_comp_icon_button_large_wide_trailing_space = 0x7f060175
com.example.fragmentsleam:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f050262
com.example.fragmentsleam:color/m3_ref_palette_purple10 = 0x7f0501a5
com.example.fragmentsleam:dimen/m3_comp_icon_button_large_narrow_leading_space = 0x7f060171
com.example.fragmentsleam:color/m3_chip_assist_text_color = 0x7f050070
com.example.fragmentsleam:macro/m3_comp_button_xsmall_container_shape_square = 0x7f0c0062
com.example.fragmentsleam:dimen/m3_comp_icon_button_large_icon_size = 0x7f060170
com.example.fragmentsleam:id/centerInside = 0x7f080070
com.example.fragmentsleam:dimen/m3_comp_icon_button_large_default_leading_space = 0x7f06016e
com.example.fragmentsleam:attr/textAppearanceHeadlineSmall = 0x7f0304b9
com.example.fragmentsleam:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f06016c
com.example.fragmentsleam:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f06016b
com.example.fragmentsleam:color/material_dynamic_tertiary70 = 0x7f050309
com.example.fragmentsleam:attr/colorOnSecondaryFixedVariant = 0x7f030114
com.example.fragmentsleam:dimen/m3_comp_filled_card_container_elevation = 0x7f060162
com.example.fragmentsleam:attr/bottomAppBarStyle = 0x7f03007a
com.example.fragmentsleam:style/TextAppearance.Design.Counter.Overflow = 0x7f1001f1
com.example.fragmentsleam:drawable/notification_action_background = 0x7f0700dd
com.example.fragmentsleam:dimen/m3_comp_fab_small_icon_size = 0x7f060160
com.example.fragmentsleam:dimen/m3_comp_fab_medium_icon_size = 0x7f060157
com.example.fragmentsleam:dimen/m3_comp_fab_large_container_height = 0x7f060154
com.example.fragmentsleam:dimen/notification_large_icon_width = 0x7f0603f5
com.example.fragmentsleam:id/allStates = 0x7f08004c
com.example.fragmentsleam:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.example.fragmentsleam:dimen/m3_searchbar_height = 0x7f0602a9
com.example.fragmentsleam:attr/expandedActiveIndicatorPaddingEnd = 0x7f0301db
com.example.fragmentsleam:color/material_dynamic_secondary40 = 0x7f0502f9
com.example.fragmentsleam:dimen/m3_comp_extended_fab_small_icon_label_space = 0x7f06014e
com.example.fragmentsleam:dimen/m3_comp_extended_fab_primary_container_pressed_container_elevation = 0x7f06014b
com.example.fragmentsleam:dimen/m3_comp_extended_fab_primary_container_hovered_state_layer_opacity = 0x7f06014a
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f100497
com.example.fragmentsleam:dimen/m3_comp_extended_fab_primary_container_hovered_container_elevation = 0x7f060149
com.example.fragmentsleam:dimen/m3_comp_extended_fab_primary_container_focused_state_layer_opacity = 0x7f060148
com.example.fragmentsleam:dimen/m3_comp_extended_fab_primary_container_container_elevation = 0x7f060146
com.example.fragmentsleam:dimen/m3_comp_extended_fab_medium_trailing_space = 0x7f060145
com.example.fragmentsleam:dimen/m3_comp_extended_fab_medium_leading_space = 0x7f060144
com.example.fragmentsleam:attr/colorErrorContainer = 0x7f030105
com.example.fragmentsleam:attr/materialDividerStyle = 0x7f030345
com.example.fragmentsleam:attr/yearSelectedStyle = 0x7f030571
com.example.fragmentsleam:dimen/m3_comp_extended_fab_medium_icon_label_space = 0x7f060142
com.example.fragmentsleam:dimen/m3_comp_extended_fab_medium_container_height = 0x7f060141
com.example.fragmentsleam:string/mtrl_picker_save = 0x7f0f0087
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_vertical_trailing_space = 0x7f0601bd
com.example.fragmentsleam:dimen/m3_comp_extended_fab_large_trailing_space = 0x7f060140
com.example.fragmentsleam:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0602c7
com.example.fragmentsleam:style/Widget.AppCompat.PopupMenu = 0x7f100367
com.example.fragmentsleam:dimen/m3_comp_extended_fab_large_leading_space = 0x7f06013f
com.example.fragmentsleam:dimen/m3_comp_extended_fab_large_container_height = 0x7f06013c
com.example.fragmentsleam:dimen/m3_comp_elevated_card_container_elevation = 0x7f06013a
com.example.fragmentsleam:color/material_personalized_color_surface_container_low = 0x7f050347
com.example.fragmentsleam:color/mtrl_navigation_item_background_color = 0x7f050381
com.example.fragmentsleam:dimen/notification_small_icon_size_as_large = 0x7f0603fb
com.example.fragmentsleam:id/embed = 0x7f0800b5
com.example.fragmentsleam:dimen/m3_comp_button_xsmall_outlined_outline_width = 0x7f060133
com.example.fragmentsleam:style/Widget.Material3.SplitButton.LeadingButton.Filled.Tonal = 0x7f10043c
com.example.fragmentsleam:id/select_dialog_listview = 0x7f0801a3
com.example.fragmentsleam:attr/fontProviderAuthority = 0x7f030231
com.example.fragmentsleam:dimen/m3_comp_button_text_hovered_state_layer_opacity = 0x7f060128
com.example.fragmentsleam:color/m3_ref_palette_primary30 = 0x7f05019a
com.example.fragmentsleam:attr/forceApplySystemWindowInsetTop = 0x7f03023b
com.example.fragmentsleam:dimen/m3_comp_button_small_leading_space = 0x7f060124
com.example.fragmentsleam:dimen/m3_comp_icon_button_medium_narrow_trailing_space = 0x7f06017a
com.example.fragmentsleam:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700d4
com.example.fragmentsleam:attr/imageButtonStyle = 0x7f030275
com.example.fragmentsleam:attr/cornerFamilyTopLeft = 0x7f03016c
com.example.fragmentsleam:attr/extendedFloatingActionButtonStyle = 0x7f0301f7
com.example.fragmentsleam:dimen/m3_comp_button_outlined_hovered_state_layer_opacity = 0x7f060120
com.example.fragmentsleam:attr/textAppearanceBody2 = 0x7f0304a0
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f05023a
com.example.fragmentsleam:dimen/m3_comp_button_outlined_focused_state_layer_opacity = 0x7f06011f
com.example.fragmentsleam:string/abc_searchview_description_query = 0x7f0f0014
com.example.fragmentsleam:dimen/m3_comp_button_medium_outlined_outline_width = 0x7f06011d
com.example.fragmentsleam:dimen/m3_comp_button_medium_icon_label_space = 0x7f06011a
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_active_hovered_state_layer_opacity = 0x7f0601ae
com.example.fragmentsleam:dimen/m3_comp_button_large_outlined_outline_width = 0x7f060118
com.example.fragmentsleam:string/mtrl_picker_range_header_unselected = 0x7f0f0086
com.example.fragmentsleam:attr/checkedIconVisible = 0x7f0300bf
com.example.fragmentsleam:dimen/m3_comp_button_large_leading_space = 0x7f060117
com.example.fragmentsleam:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f10008f
com.example.fragmentsleam:dimen/m3_comp_button_filled_disabled_container_elevation = 0x7f060112
com.example.fragmentsleam:macro/m3_comp_button_medium_selected_container_shape_round = 0x7f0c0029
com.example.fragmentsleam:dimen/m3_comp_button_elevated_disabled_container_elevation = 0x7f060110
com.example.fragmentsleam:attr/endIconDrawable = 0x7f0301c4
com.example.fragmentsleam:attr/dragThreshold = 0x7f0301a6
com.example.fragmentsleam:dimen/m3_comp_badge_size = 0x7f06010c
com.example.fragmentsleam:attr/autoShowKeyboard = 0x7f03003f
com.example.fragmentsleam:drawable/abc_ratingbar_small_material = 0x7f07005d
com.example.fragmentsleam:dimen/m3_comp_badge_large_size = 0x7f06010b
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.Full = 0x7f100196
com.example.fragmentsleam:id/FUNCTION = 0x7f080004
com.example.fragmentsleam:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f060108
com.example.fragmentsleam:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f060107
com.example.fragmentsleam:color/m3_default_color_primary_text = 0x7f05007a
com.example.fragmentsleam:attr/shapeCornerSizeExtraSmall = 0x7f030424
com.example.fragmentsleam:attr/expandedWidth = 0x7f0301ef
com.example.fragmentsleam:attr/paddingTopSystemWindowInsets = 0x7f0303c8
com.example.fragmentsleam:attr/carousel_touchUp_dampeningFactor = 0x7f0300ae
com.example.fragmentsleam:macro/m3_comp_button_outlined_disabled_outline_color = 0x7f0c002a
com.example.fragmentsleam:dimen/m3_comp_split_button_xsmall_trailing_button_icon_size = 0x7f060233
com.example.fragmentsleam:attr/viewTransitionMode = 0x7f030554
com.example.fragmentsleam:drawable/ic_mtrl_chip_checked_circle = 0x7f07009b
com.example.fragmentsleam:attr/yearStyle = 0x7f030572
com.example.fragmentsleam:attr/multiChoiceItemLayout = 0x7f0303a9
com.example.fragmentsleam:dimen/m3_chip_hovered_translation_z = 0x7f0600fd
com.example.fragmentsleam:layout/design_text_input_end_icon = 0x7f0b002b
com.example.fragmentsleam:id/easeOut = 0x7f0800af
com.example.fragmentsleam:color/m3_icon_button_icon_color_selector = 0x7f050090
com.example.fragmentsleam:dimen/m3_comp_nav_bar_item_vertical_active_indicator_height = 0x7f0601a3
com.example.fragmentsleam:layout/abc_list_menu_item_icon = 0x7f0b000f
com.example.fragmentsleam:attr/motionDurationMedium2 = 0x7f03037f
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_focused_icon_color = 0x7f0c003b
com.example.fragmentsleam:dimen/m3_chip_dragged_translation_z = 0x7f0600fb
com.example.fragmentsleam:macro/m3_comp_app_bar_medium_title_font = 0x7f0c0007
com.example.fragmentsleam:attr/showText = 0x7f030432
com.example.fragmentsleam:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600f8
com.example.fragmentsleam:dimen/m3_card_hovered_z = 0x7f0600f0
com.example.fragmentsleam:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1002f9
com.example.fragmentsleam:dimen/m3_card_elevation = 0x7f0600ef
com.example.fragmentsleam:attr/motionDurationLong3 = 0x7f03037c
com.example.fragmentsleam:dimen/m3_card_elevated_disabled_z = 0x7f0600eb
com.example.fragmentsleam:dimen/m3_card_dragged_z = 0x7f0600ea
com.example.fragmentsleam:attr/defaultQueryHint = 0x7f030190
com.example.fragmentsleam:dimen/m3_btn_translation_z_base = 0x7f0600e7
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1001fe
com.example.fragmentsleam:dimen/m3_navigation_rail_max_expanded_width = 0x7f06029e
com.example.fragmentsleam:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f10035b
com.example.fragmentsleam:dimen/m3_btn_text_btn_padding_right = 0x7f0600e6
com.example.fragmentsleam:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.example.fragmentsleam:attr/titleMarginTop = 0x7f030515
com.example.fragmentsleam:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600e4
com.example.fragmentsleam:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f100047
com.example.fragmentsleam:string/abc_prepend_shortcut_label = 0x7f0f0011
com.example.fragmentsleam:attr/layout_constraintVertical_bias = 0x7f0302e8
com.example.fragmentsleam:attr/colorOnSecondaryFixed = 0x7f030113
com.example.fragmentsleam:color/m3expressive_bottom_nav_item_ripple_tint = 0x7f0502b8
com.example.fragmentsleam:dimen/m3_btn_stroke_size = 0x7f0600e2
com.example.fragmentsleam:attr/endIconScaleType = 0x7f0301c7
com.example.fragmentsleam:styleable/CoordinatorLayout_Layout = 0x7f11002d
com.example.fragmentsleam:id/transition_clip = 0x7f0801f7
com.example.fragmentsleam:attr/editTextStyle = 0x7f0301bb
com.example.fragmentsleam:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f0601d3
com.example.fragmentsleam:dimen/m3_btn_inset = 0x7f0600dc
com.example.fragmentsleam:style/Widget.Material3.SplitButton.IconButton.Filled = 0x7f100439
com.example.fragmentsleam:macro/m3_comp_switch_selected_icon_color = 0x7f0c0194
com.example.fragmentsleam:dimen/m3_btn_icon_btn_padding_left = 0x7f0600d6
com.example.fragmentsleam:dimen/m3_btn_elevation = 0x7f0600d5
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1004bc
com.example.fragmentsleam:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f10007f
com.example.fragmentsleam:dimen/m3_btn_dialog_btn_spacing = 0x7f0600d1
com.example.fragmentsleam:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600cd
com.example.fragmentsleam:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600ca
com.example.fragmentsleam:attr/textOutlineThickness = 0x7f0304e7
com.example.fragmentsleam:dimen/m3_bottom_sheet_elevation = 0x7f0600c8
com.example.fragmentsleam:attr/setsTag = 0x7f030412
com.example.fragmentsleam:color/m3_sys_color_dark_inverse_primary = 0x7f0501fd
com.example.fragmentsleam:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600c1
com.example.fragmentsleam:dimen/m3_badge_with_text_vertical_padding = 0x7f0600bf
com.example.fragmentsleam:dimen/fastscroll_minimum_range = 0x7f060094
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f10015f
com.example.fragmentsleam:id/accessibility_custom_action_26 = 0x7f080024
com.example.fragmentsleam:dimen/mtrl_calendar_action_height = 0x7f060357
com.example.fragmentsleam:dimen/m3_badge_with_text_vertical_offset = 0x7f0600be
com.example.fragmentsleam:attr/barrierMargin = 0x7f030067
com.example.fragmentsleam:id/left = 0x7f0800fe
com.example.fragmentsleam:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f0502ae
com.example.fragmentsleam:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0c01ad
com.example.fragmentsleam:dimen/m3_badge_with_text_offset = 0x7f0600bc
com.example.fragmentsleam:dimen/m3_badge_vertical_offset = 0x7f0600ba
com.example.fragmentsleam:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f100449
com.example.fragmentsleam:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1000a3
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500ea
com.example.fragmentsleam:dimen/m3_card_stroke_width = 0x7f0600f1
com.example.fragmentsleam:macro/m3_comp_slider_active_stop_indicator_container_color = 0x7f0c0172
com.example.fragmentsleam:color/material_on_surface_emphasis_medium = 0x7f050321
com.example.fragmentsleam:color/m3_sys_color_light_on_secondary = 0x7f050276
com.example.fragmentsleam:dimen/m3_badge_offset = 0x7f0600b8
com.example.fragmentsleam:color/cardview_shadow_end_color = 0x7f05002e
com.example.fragmentsleam:attr/textAppearanceBodySmall = 0x7f0304a5
com.example.fragmentsleam:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b4
com.example.fragmentsleam:dimen/m3_navigation_content_horizontal_margin = 0x7f06027d
com.example.fragmentsleam:attr/collapsedSubtitleTextAppearance = 0x7f0300ef
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_active_label_text_color = 0x7f0c00e8
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0c0070
com.example.fragmentsleam:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060136
com.example.fragmentsleam:color/m3_dark_default_color_secondary_text = 0x7f050076
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_disabled_label_text_color = 0x7f0c01d4
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001d
com.example.fragmentsleam:dimen/mtrl_navigation_rail_text_size = 0x7f0603b7
com.example.fragmentsleam:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600b2
com.example.fragmentsleam:dimen/m3_navigation_item_vertical_padding = 0x7f06028a
com.example.fragmentsleam:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600b0
com.example.fragmentsleam:attr/textAppearanceSmallPopupMenu = 0x7f0304ca
com.example.fragmentsleam:attr/textAppearanceHeadlineMediumEmphasized = 0x7f0304b8
com.example.fragmentsleam:id/search_bar = 0x7f08019a
com.example.fragmentsleam:color/m3expressive_button_outline_color_selector = 0x7f0502b9
com.example.fragmentsleam:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0603a8
com.example.fragmentsleam:macro/m3_comp_search_bar_input_text_color = 0x7f0c0150
com.example.fragmentsleam:dimen/notification_big_circle_margin = 0x7f0603f2
com.example.fragmentsleam:color/m3_ref_palette_yellow80 = 0x7f0501e4
com.example.fragmentsleam:attr/telltales_tailColor = 0x7f03049b
com.example.fragmentsleam:dimen/m3_appbar_size_compact = 0x7f0600ab
com.example.fragmentsleam:style/Theme.AppCompat = 0x7f100250
com.example.fragmentsleam:id/ignore = 0x7f0800e9
com.example.fragmentsleam:dimen/m3_alert_dialog_corner_size = 0x7f0600a1
com.example.fragmentsleam:color/m3_ref_palette_primary95 = 0x7f0501a1
com.example.fragmentsleam:macro/m3_comp_filter_chip_label_text_type = 0x7f0c00b7
com.example.fragmentsleam:drawable/avd_hide_password = 0x7f070078
com.example.fragmentsleam:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009e
com.example.fragmentsleam:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009d
com.example.fragmentsleam:dimen/hint_pressed_alpha_material_light = 0x7f06009b
com.example.fragmentsleam:dimen/hint_pressed_alpha_material_dark = 0x7f06009a
com.example.fragmentsleam:attr/homeLayout = 0x7f030262
com.example.fragmentsleam:dimen/fastscroll_default_thickness = 0x7f060092
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f10024a
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f10015b
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_inactive_icon_color = 0x7f0c00ed
com.example.fragmentsleam:color/m3_ref_palette_orange80 = 0x7f050185
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_medium = 0x7f0602fb
com.example.fragmentsleam:layout/material_chip_input_combo = 0x7f0b0039
com.example.fragmentsleam:dimen/design_tab_max_width = 0x7f06008b
com.example.fragmentsleam:attr/collapsedSize = 0x7f0300ee
com.example.fragmentsleam:style/Widget.Material3.Toolbar.OnSurface = 0x7f10044d
com.example.fragmentsleam:dimen/design_snackbar_action_text_color_alpha = 0x7f060081
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_circular_track_active_indicator_space = 0x7f0601da
com.example.fragmentsleam:color/design_fab_stroke_end_outer_color = 0x7f050051
com.example.fragmentsleam:color/material_dynamic_neutral30 = 0x7f0502d1
com.example.fragmentsleam:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600c4
com.example.fragmentsleam:layout/mtrl_calendar_vertical = 0x7f0b0056
com.example.fragmentsleam:dimen/design_snackbar_action_inline_max_width = 0x7f060080
com.example.fragmentsleam:dimen/design_navigation_separator_vertical_padding = 0x7f06007f
com.example.fragmentsleam:dimen/design_navigation_padding_bottom = 0x7f06007e
com.example.fragmentsleam:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301f8
com.example.fragmentsleam:dimen/m3_comp_slider_medium_active_track_shape_leading = 0x7f06020e
com.example.fragmentsleam:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0c0185
com.example.fragmentsleam:macro/m3_comp_slider_disabled_active_stop_indicator_container_color = 0x7f0c0174
com.example.fragmentsleam:dimen/design_navigation_max_width = 0x7f06007d
com.example.fragmentsleam:style/TextAppearance.Compat.Notification.Title = 0x7f1001ee
com.example.fragmentsleam:dimen/design_navigation_item_vertical_padding = 0x7f06007c
com.example.fragmentsleam:style/Widget.AppCompat.ActivityChooserView = 0x7f10033b
com.example.fragmentsleam:attr/layout_constraintBaseline_creator = 0x7f0302c4
com.example.fragmentsleam:dimen/design_navigation_icon_size = 0x7f060079
com.example.fragmentsleam:dimen/design_navigation_icon_padding = 0x7f060078
com.example.fragmentsleam:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1000bc
com.example.fragmentsleam:dimen/m3_snackbar_action_text_color_alpha = 0x7f0602bd
com.example.fragmentsleam:dimen/m3_btn_padding_right = 0x7f0600e0
com.example.fragmentsleam:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.example.fragmentsleam:style/Base.Widget.AppCompat.ListView = 0x7f1000e5
com.example.fragmentsleam:dimen/design_fab_size_normal = 0x7f060074
com.example.fragmentsleam:dimen/design_fab_image_size = 0x7f060072
com.example.fragmentsleam:dimen/design_fab_elevation = 0x7f060071
com.example.fragmentsleam:id/arc = 0x7f080052
com.example.fragmentsleam:attr/dividerInsetStart = 0x7f03019e
com.example.fragmentsleam:dimen/design_fab_border_width = 0x7f060070
com.example.fragmentsleam:dimen/design_bottom_navigation_text_size = 0x7f06006c
com.example.fragmentsleam:dimen/design_bottom_navigation_label_padding = 0x7f060069
com.example.fragmentsleam:attr/fontVariationSettings = 0x7f030239
com.example.fragmentsleam:style/Theme.AppCompat.Dialog = 0x7f100259
com.example.fragmentsleam:styleable/MaterialButton = 0x7f110053
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall.Emphasized = 0x7f10020b
com.example.fragmentsleam:anim/abc_slide_in_top = 0x7f010007
com.example.fragmentsleam:style/Base.Theme.AppCompat.CompactMenu = 0x7f10004b
com.example.fragmentsleam:drawable/abc_item_background_holo_dark = 0x7f07004b
com.example.fragmentsleam:color/m3_ref_palette_red40 = 0x7f0501b6
com.example.fragmentsleam:dimen/design_bottom_navigation_active_item_min_width = 0x7f060062
com.example.fragmentsleam:dimen/m3_card_elevated_elevation = 0x7f0600ed
com.example.fragmentsleam:string/bottom_sheet_behavior = 0x7f0f001e
com.example.fragmentsleam:dimen/design_appbar_elevation = 0x7f060060
com.example.fragmentsleam:dimen/def_drawer_elevation = 0x7f06005d
com.example.fragmentsleam:attr/textureEffect = 0x7f0304ec
com.example.fragmentsleam:dimen/compat_control_corner_material = 0x7f06005a
com.example.fragmentsleam:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f050384
com.example.fragmentsleam:dimen/compat_button_padding_vertical_material = 0x7f060059
com.example.fragmentsleam:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600bb
com.example.fragmentsleam:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.example.fragmentsleam:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f050351
com.example.fragmentsleam:attr/boxCornerRadiusTopStart = 0x7f030086
com.example.fragmentsleam:dimen/abc_text_size_menu_material = 0x7f06004b
com.example.fragmentsleam:dimen/abc_text_size_display_4_material = 0x7f060046
com.example.fragmentsleam:dimen/abc_text_size_display_2_material = 0x7f060044
com.example.fragmentsleam:dimen/design_snackbar_padding_vertical_2lines = 0x7f060089
com.example.fragmentsleam:string/abc_action_mode_done = 0x7f0f0003
com.example.fragmentsleam:color/m3_ref_palette_primary50 = 0x7f05019c
com.example.fragmentsleam:dimen/abc_text_size_caption_material = 0x7f060042
com.example.fragmentsleam:color/m3_navigation_item_icon_tint = 0x7f050095
com.example.fragmentsleam:dimen/abc_text_size_button_material = 0x7f060041
com.example.fragmentsleam:attr/materialTimePickerTheme = 0x7f030358
com.example.fragmentsleam:attr/thumbIcon = 0x7f0304f4
com.example.fragmentsleam:attr/expandedTitleTextAppearance = 0x7f0301ed
com.example.fragmentsleam:attr/closeIcon = 0x7f0300e3
com.example.fragmentsleam:dimen/abc_star_big = 0x7f06003b
com.example.fragmentsleam:style/Widget.Material3.Button.UnelevatedButton = 0x7f1003ac
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f100404
com.example.fragmentsleam:color/m3_sys_color_light_surface_bright = 0x7f050283
com.example.fragmentsleam:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f100092
com.example.fragmentsleam:attr/track = 0x7f03052a
com.example.fragmentsleam:dimen/abc_star_medium = 0x7f06003c
com.example.fragmentsleam:id/shortcut = 0x7f0801a8
com.example.fragmentsleam:dimen/abc_search_view_preferred_height = 0x7f060036
com.example.fragmentsleam:macro/m3_comp_search_bar_leading_icon_color = 0x7f0c0152
com.example.fragmentsleam:style/Base.Widget.Design.TabLayout = 0x7f1000fa
com.example.fragmentsleam:color/m3_ref_palette_grey95 = 0x7f050147
com.example.fragmentsleam:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f100318
com.example.fragmentsleam:dimen/abc_list_item_height_material = 0x7f060031
com.example.fragmentsleam:animator/m3_appbar_state_list_animator = 0x7f02000b
com.example.fragmentsleam:macro/m3_comp_button_xlarge_label_text = 0x7f0c005f
com.example.fragmentsleam:dimen/abc_list_item_height_large_material = 0x7f060030
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001f
com.example.fragmentsleam:id/report_drawn = 0x7f080186
com.example.fragmentsleam:style/TextAppearance.Compat.Notification = 0x7f1001ea
com.example.fragmentsleam:dimen/abc_text_size_body_1_material = 0x7f06003f
com.example.fragmentsleam:dimen/m3_comp_icon_button_large_wide_leading_space = 0x7f060174
com.example.fragmentsleam:attr/materialIconButtonFilledTonalStyle = 0x7f030347
com.example.fragmentsleam:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.example.fragmentsleam:color/m3_sys_color_on_primary_fixed_variant = 0x7f05028e
com.example.fragmentsleam:styleable/DrawerLayout = 0x7f110031
com.example.fragmentsleam:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.example.fragmentsleam:dimen/abc_control_padding_material = 0x7f06001a
com.example.fragmentsleam:style/ThemeOverlay.Material3.Dialog = 0x7f1002dd
com.example.fragmentsleam:color/m3_ref_palette_secondary60 = 0x7f0501c5
com.example.fragmentsleam:attr/subtitleCentered = 0x7f030470
com.example.fragmentsleam:dimen/m3_comp_fab_primary_container_focused_container_elevation = 0x7f060159
com.example.fragmentsleam:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.example.fragmentsleam:dimen/m3_side_sheet_modal_elevation = 0x7f0602b4
com.example.fragmentsleam:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Small.Container.Shape.Round = 0x7f100166
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Xsmall.Selected.Container.Shape.Square = 0x7f100177
com.example.fragmentsleam:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.example.fragmentsleam:color/design_default_color_primary_dark = 0x7f050047
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f0602cf
com.example.fragmentsleam:color/material_on_background_emphasis_medium = 0x7f05031b
com.example.fragmentsleam:layout/abc_search_view = 0x7f0b0019
com.example.fragmentsleam:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.example.fragmentsleam:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f10010f
com.example.fragmentsleam:attr/ifTagSet = 0x7f030274
com.example.fragmentsleam:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.example.fragmentsleam:color/material_dynamic_neutral_variant40 = 0x7f0502df
com.example.fragmentsleam:interpolator/mtrl_fast_out_slow_in = 0x7f0a000f
com.example.fragmentsleam:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500d4
com.example.fragmentsleam:attr/colorContainerChecked = 0x7f0300ff
com.example.fragmentsleam:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.example.fragmentsleam:attr/windowActionBar = 0x7f030567
com.example.fragmentsleam:color/white = 0x7f0503b1
com.example.fragmentsleam:color/tooltip_background_light = 0x7f0503b0
com.example.fragmentsleam:attr/errorIconTint = 0x7f0301d3
com.example.fragmentsleam:color/tooltip_background_dark = 0x7f0503af
com.example.fragmentsleam:dimen/m3_comp_split_button_small_leading_button_trailing_space = 0x7f060228
com.example.fragmentsleam:attr/springStiffness = 0x7f03044b
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f100298
com.example.fragmentsleam:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f0601e5
com.example.fragmentsleam:color/switch_thumb_disabled_material_light = 0x7f0503aa
com.example.fragmentsleam:color/switch_thumb_disabled_material_dark = 0x7f0503a9
com.example.fragmentsleam:color/secondary_text_disabled_material_light = 0x7f0503a8
com.example.fragmentsleam:color/secondary_text_disabled_material_dark = 0x7f0503a7
com.example.fragmentsleam:color/secondary_text_default_material_dark = 0x7f0503a5
com.example.fragmentsleam:style/TextAppearance.Material3.ActionBar.Title = 0x7f100219
com.example.fragmentsleam:style/Animation.Material3.SideSheetDialog.Right = 0x7f100009
com.example.fragmentsleam:dimen/m3_comp_split_button_xlarge_leading_button_trailing_space = 0x7f06022d
com.example.fragmentsleam:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f100253
com.example.fragmentsleam:dimen/m3_comp_slider_active_handle_leading_space = 0x7f060200
com.example.fragmentsleam:attr/tabPaddingBottom = 0x7f03048e
com.example.fragmentsleam:color/primary_text_default_material_light = 0x7f0503a0
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1001e6
com.example.fragmentsleam:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f06029c
com.example.fragmentsleam:dimen/mtrl_alert_dialog_background_inset_start = 0x7f06032d
com.example.fragmentsleam:color/ripple_material_light = 0x7f0503a4
com.example.fragmentsleam:color/material_on_primary_disabled = 0x7f05031c
com.example.fragmentsleam:color/primary_dark_material_light = 0x7f05039c
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.ExtraLargeIncreased = 0x7f100194
com.example.fragmentsleam:color/primary_dark_material_dark = 0x7f05039b
com.example.fragmentsleam:color/m3_ref_palette_grey_variant40 = 0x7f05014e
com.example.fragmentsleam:color/notification_icon_bg_color = 0x7f05039a
com.example.fragmentsleam:color/notification_action_color_filter = 0x7f050399
com.example.fragmentsleam:color/m3_ref_palette_grey_variant0 = 0x7f050149
com.example.fragmentsleam:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f060296
com.example.fragmentsleam:attr/contentInsetRight = 0x7f030159
com.example.fragmentsleam:color/material_dynamic_neutral_variant50 = 0x7f0502e0
com.example.fragmentsleam:color/mtrl_textinput_focused_box_stroke_color = 0x7f050397
com.example.fragmentsleam:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f100280
com.example.fragmentsleam:string/material_minute_suffix = 0x7f0f004e
com.example.fragmentsleam:dimen/material_emphasis_high_type = 0x7f060319
com.example.fragmentsleam:color/switch_thumb_material_light = 0x7f0503ac
com.example.fragmentsleam:dimen/abc_panel_menu_list_width = 0x7f060034
com.example.fragmentsleam:color/bright_foreground_material_light = 0x7f050027
com.example.fragmentsleam:color/material_dynamic_tertiary0 = 0x7f050301
com.example.fragmentsleam:color/mtrl_textinput_filled_box_default_background_color = 0x7f050396
com.example.fragmentsleam:styleable/ConstraintSet = 0x7f11002b
com.example.fragmentsleam:attr/borderWidth = 0x7f030078
com.example.fragmentsleam:color/mtrl_textinput_disabled_color = 0x7f050395
com.example.fragmentsleam:color/mtrl_tabs_ripple_color = 0x7f050392
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Headline2 = 0x7f100243
com.example.fragmentsleam:string/mtrl_picker_toggled_to_year_selection = 0x7f0f0095
com.example.fragmentsleam:color/mtrl_switch_track_tint = 0x7f05038d
com.example.fragmentsleam:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f0602da
com.example.fragmentsleam:color/mtrl_switch_track_decoration_tint = 0x7f05038c
com.example.fragmentsleam:color/mtrl_switch_thumb_tint = 0x7f05038b
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f100121
com.example.fragmentsleam:attr/clickAction = 0x7f0300de
com.example.fragmentsleam:dimen/m3_card_elevated_dragged_z = 0x7f0600ec
com.example.fragmentsleam:color/m3_timepicker_button_ripple_color = 0x7f0502a8
com.example.fragmentsleam:color/mtrl_scrim_color = 0x7f050389
com.example.fragmentsleam:id/line1 = 0x7f080101
com.example.fragmentsleam:id/end = 0x7f0800b6
com.example.fragmentsleam:color/mtrl_popupmenu_overlay_color = 0x7f050388
com.example.fragmentsleam:attr/progressBarPadding = 0x7f0303e7
com.example.fragmentsleam:string/m3_ref_typeface_brand_medium = 0x7f0f0039
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0c007e
com.example.fragmentsleam:attr/telltales_tailScale = 0x7f03049c
com.example.fragmentsleam:color/m3_sys_color_light_secondary_container = 0x7f050281
com.example.fragmentsleam:color/mtrl_outlined_icon_tint = 0x7f050386
com.example.fragmentsleam:color/m3_tabs_ripple_color = 0x7f05029b
com.example.fragmentsleam:color/mtrl_navigation_bar_item_tint = 0x7f05037f
com.example.fragmentsleam:attr/tabBackground = 0x7f03047d
com.example.fragmentsleam:color/m3_ref_palette_blue_variant98 = 0x7f0500b9
com.example.fragmentsleam:color/mtrl_fab_ripple_color = 0x7f050378
com.example.fragmentsleam:color/mtrl_fab_icon_text_color_selector = 0x7f050377
com.example.fragmentsleam:attr/marginHorizontal = 0x7f03031e
com.example.fragmentsleam:color/m3_ref_palette_red10 = 0x7f0501b2
com.example.fragmentsleam:color/mtrl_error = 0x7f050375
com.example.fragmentsleam:attr/containerPaddingTop = 0x7f03014e
com.example.fragmentsleam:attr/perpendicularPath_percent = 0x7f0303d7
com.example.fragmentsleam:integer/m3_card_anim_duration_ms = 0x7f09000d
com.example.fragmentsleam:id/scrollView = 0x7f080197
com.example.fragmentsleam:attr/colorOnContainer = 0x7f030107
com.example.fragmentsleam:id/frrg1 = 0x7f0800d1
com.example.fragmentsleam:layout/mtrl_calendar_month = 0x7f0b0052
com.example.fragmentsleam:attr/indeterminateProgressStyle = 0x7f03027d
com.example.fragmentsleam:attr/defaultMarginsEnabled = 0x7f03018f
com.example.fragmentsleam:color/mtrl_chip_text_color = 0x7f050371
com.example.fragmentsleam:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.example.fragmentsleam:macro/m3_comp_snackbar_supporting_text_color = 0x7f0c0180
com.example.fragmentsleam:color/mtrl_chip_background_color = 0x7f05036e
com.example.fragmentsleam:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f1004a3
com.example.fragmentsleam:drawable/abc_list_selector_background_transition_holo_light = 0x7f070054
com.example.fragmentsleam:color/mtrl_card_view_ripple = 0x7f05036d
com.example.fragmentsleam:attr/motionDurationExtraLong1 = 0x7f030376
com.example.fragmentsleam:color/mtrl_card_view_foreground = 0x7f05036c
com.example.fragmentsleam:attr/layout_constraintLeft_toLeftOf = 0x7f0302dd
com.example.fragmentsleam:color/mtrl_calendar_selected_range = 0x7f05036b
com.example.fragmentsleam:color/mtrl_btn_transparent_bg_color = 0x7f050369
com.example.fragmentsleam:color/mtrl_btn_text_color_selector = 0x7f050368
com.example.fragmentsleam:dimen/design_snackbar_max_width = 0x7f060085
com.example.fragmentsleam:color/mtrl_btn_text_color_disabled = 0x7f050367
com.example.fragmentsleam:attr/flow_verticalAlign = 0x7f03022a
com.example.fragmentsleam:color/mtrl_btn_text_btn_bg_color_selector = 0x7f050365
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f100295
com.example.fragmentsleam:attr/textInputLayoutFocusedRectEnabled = 0x7f0304e0
com.example.fragmentsleam:dimen/m3_comp_search_bar_avatar_size = 0x7f0601ec
com.example.fragmentsleam:color/mtrl_btn_stroke_color_selector = 0x7f050364
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f10003e
com.example.fragmentsleam:color/m3_vibrant_toolbar_button_text_color_selector = 0x7f0502b2
com.example.fragmentsleam:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f050352
com.example.fragmentsleam:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f10004d
com.example.fragmentsleam:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1002ce
com.example.fragmentsleam:color/material_personalized_color_text_primary_inverse = 0x7f05034f
com.example.fragmentsleam:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a8
com.example.fragmentsleam:attr/actionOverflowMenuStyle = 0x7f030022
com.example.fragmentsleam:style/TextAppearance.Material3.HeadlineLarge.Emphasized = 0x7f100227
com.example.fragmentsleam:integer/mtrl_switch_thumb_pressed_duration = 0x7f09003c
com.example.fragmentsleam:attr/shapeAppearanceSmallComponent = 0x7f03041f
com.example.fragmentsleam:color/m3_ref_palette_primary0 = 0x7f050196
com.example.fragmentsleam:color/material_dynamic_tertiary100 = 0x7f050303
com.example.fragmentsleam:attr/collapsedTitleTextColor = 0x7f0300f4
com.example.fragmentsleam:color/material_personalized_color_text_hint_foreground_inverse = 0x7f05034e
com.example.fragmentsleam:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
com.example.fragmentsleam:id/with_icon = 0x7f080212
com.example.fragmentsleam:id/currentState = 0x7f08008c
com.example.fragmentsleam:color/material_personalized_color_surface_container_lowest = 0x7f050348
com.example.fragmentsleam:color/material_personalized_color_surface_container_high = 0x7f050345
com.example.fragmentsleam:color/material_personalized_color_surface_container = 0x7f050344
com.example.fragmentsleam:color/material_personalized_color_surface = 0x7f050342
com.example.fragmentsleam:color/material_personalized_color_secondary_text = 0x7f050340
com.example.fragmentsleam:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.example.fragmentsleam:attr/onNegativeCross = 0x7f0303b8
com.example.fragmentsleam:dimen/m3_btn_elevated_translation_z_hovered = 0x7f0600d4
com.example.fragmentsleam:color/material_personalized_color_secondary = 0x7f05033e
com.example.fragmentsleam:attr/colorOnTertiary = 0x7f030118
com.example.fragmentsleam:attr/removeEmbeddedFabElevation = 0x7f0303fd
com.example.fragmentsleam:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f060297
com.example.fragmentsleam:dimen/m3_comp_split_button_large_trailing_button_trailing_space = 0x7f060221
com.example.fragmentsleam:dimen/mtrl_btn_disabled_elevation = 0x7f060340
com.example.fragmentsleam:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600aa
com.example.fragmentsleam:color/material_personalized_color_primary_inverse = 0x7f05033b
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0c01ba
com.example.fragmentsleam:color/design_box_stroke_color = 0x7f050031
com.example.fragmentsleam:dimen/default_navigation_text_size = 0x7f06005f
com.example.fragmentsleam:color/material_personalized_color_primary_container = 0x7f05033a
com.example.fragmentsleam:color/material_personalized_color_outline = 0x7f050337
com.example.fragmentsleam:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700b2
com.example.fragmentsleam:dimen/m3_comp_icon_button_xlarge_wide_trailing_space = 0x7f06018d
com.example.fragmentsleam:attr/buttonBarStyle = 0x7f030090
com.example.fragmentsleam:color/material_personalized_color_on_surface_inverse = 0x7f050333
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1001b4
com.example.fragmentsleam:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f10009c
com.example.fragmentsleam:color/material_personalized_color_on_surface = 0x7f050332
com.example.fragmentsleam:color/material_personalized_color_on_secondary_container = 0x7f050331
com.example.fragmentsleam:layout/m3_alert_dialog_actions = 0x7f0b0033
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f0602d0
com.example.fragmentsleam:color/material_personalized_color_on_secondary = 0x7f050330
com.example.fragmentsleam:style/Widget.AppCompat.ProgressBar = 0x7f10036a
com.example.fragmentsleam:styleable/ShapeAppearance = 0x7f110080
com.example.fragmentsleam:color/material_personalized_color_on_primary = 0x7f05032e
com.example.fragmentsleam:color/material_personalized_color_on_error_container = 0x7f05032d
com.example.fragmentsleam:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f0601f0
com.example.fragmentsleam:color/material_personalized_color_on_error = 0x7f05032c
com.example.fragmentsleam:color/material_personalized_color_on_background = 0x7f05032b
com.example.fragmentsleam:color/material_personalized_color_error = 0x7f050329
com.example.fragmentsleam:attr/errorIconTintMode = 0x7f0301d4
com.example.fragmentsleam:color/material_personalized__highlighted_text = 0x7f050323
com.example.fragmentsleam:macro/m3_comp_button_xsmall_label_text = 0x7f0c0063
com.example.fragmentsleam:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.example.fragmentsleam:color/material_on_surface_disabled = 0x7f05031f
com.example.fragmentsleam:color/material_personalized_color_control_activated = 0x7f050326
com.example.fragmentsleam:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1000c3
com.example.fragmentsleam:string/mtrl_checkbox_button_icon_path_group_name = 0x7f0f0062
com.example.fragmentsleam:color/material_on_background_emphasis_high_type = 0x7f05031a
com.example.fragmentsleam:id/edit_query = 0x7f0800b2
com.example.fragmentsleam:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f0601d6
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Medium = 0x7f1003e3
com.example.fragmentsleam:id/cradle = 0x7f08008b
com.example.fragmentsleam:id/aligned = 0x7f08004a
com.example.fragmentsleam:attr/chipStrokeWidth = 0x7f0300d3
com.example.fragmentsleam:color/material_on_background_disabled = 0x7f050319
com.example.fragmentsleam:color/material_harmonized_color_on_error_container = 0x7f050318
com.example.fragmentsleam:attr/enableEdgeToEdge = 0x7f0301c1
com.example.fragmentsleam:attr/motionDurationShort4 = 0x7f030385
com.example.fragmentsleam:attr/animateNavigationIcon = 0x7f030033
com.example.fragmentsleam:color/material_harmonized_color_on_error = 0x7f050317
com.example.fragmentsleam:id/material_clock_hand = 0x7f080111
com.example.fragmentsleam:color/m3_sys_color_light_on_surface_variant = 0x7f050279
com.example.fragmentsleam:color/material_grey_900 = 0x7f050314
com.example.fragmentsleam:color/material_grey_800 = 0x7f050312
com.example.fragmentsleam:dimen/m3_comp_toolbar_docked_container_leading_space = 0x7f060252
com.example.fragmentsleam:attr/transitionPathRotate = 0x7f030546
com.example.fragmentsleam:attr/expandedActiveIndicatorPaddingStart = 0x7f0301dc
com.example.fragmentsleam:color/material_grey_600 = 0x7f050311
com.example.fragmentsleam:attr/itemMaxLines = 0x7f030296
com.example.fragmentsleam:color/material_grey_300 = 0x7f05030f
com.example.fragmentsleam:id/reverseSawtooth = 0x7f080188
com.example.fragmentsleam:drawable/m3_tabs_rounded_line_indicator = 0x7f0700a9
com.example.fragmentsleam:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0603a5
com.example.fragmentsleam:color/material_dynamic_tertiary40 = 0x7f050306
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0c0169
com.example.fragmentsleam:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070044
com.example.fragmentsleam:attr/verticalOffsetWithText = 0x7f030552
com.example.fragmentsleam:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f0601ca
com.example.fragmentsleam:color/material_dynamic_tertiary20 = 0x7f050304
com.example.fragmentsleam:style/Theme.AppCompat.DayNight.Dialog = 0x7f100254
com.example.fragmentsleam:id/submit_area = 0x7f0801cb
com.example.fragmentsleam:style/Base.Theme.AppCompat = 0x7f10004a
com.example.fragmentsleam:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.example.fragmentsleam:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600c0
com.example.fragmentsleam:attr/tabIndicatorFullWidth = 0x7f030486
com.example.fragmentsleam:color/m3_assist_chip_stroke_color = 0x7f050062
com.example.fragmentsleam:color/material_dynamic_secondary50 = 0x7f0502fa
com.example.fragmentsleam:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006d
com.example.fragmentsleam:color/material_dynamic_secondary30 = 0x7f0502f8
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1000c9
com.example.fragmentsleam:color/material_dynamic_secondary100 = 0x7f0502f6
com.example.fragmentsleam:color/material_dynamic_primary95 = 0x7f0502f2
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_focused_state_layer_color = 0x7f0c0049
com.example.fragmentsleam:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.example.fragmentsleam:color/material_dynamic_primary90 = 0x7f0502f1
com.example.fragmentsleam:attr/badgeShapeAppearanceOverlay = 0x7f030057
com.example.fragmentsleam:dimen/material_clock_face_margin_bottom = 0x7f060309
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f0602e7
com.example.fragmentsleam:color/material_dynamic_primary60 = 0x7f0502ee
com.example.fragmentsleam:drawable/abc_ic_go_search_api_material = 0x7f070042
com.example.fragmentsleam:attr/showAsAction = 0x7f03042c
com.example.fragmentsleam:color/m3_textfield_input_text_color = 0x7f0502a4
com.example.fragmentsleam:color/material_blue_grey_950 = 0x7f0502c0
com.example.fragmentsleam:color/abc_primary_text_material_dark = 0x7f05000b
com.example.fragmentsleam:attr/trackInsideCornerSize = 0x7f03053c
com.example.fragmentsleam:dimen/m3_comp_elevated_card_icon_size = 0x7f06013b
com.example.fragmentsleam:id/material_hour_tv = 0x7f080117
com.example.fragmentsleam:dimen/cardview_default_elevation = 0x7f060053
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents = 0x7f100091
com.example.fragmentsleam:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.example.fragmentsleam:attr/suffixTextColor = 0x7f030477
com.example.fragmentsleam:color/material_dynamic_primary20 = 0x7f0502ea
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f10012a
com.example.fragmentsleam:style/Animation.Material3.SideSheetDialog = 0x7f100007
com.example.fragmentsleam:color/material_dynamic_primary10 = 0x7f0502e8
com.example.fragmentsleam:color/material_dynamic_neutral_variant99 = 0x7f0502e6
com.example.fragmentsleam:color/material_dynamic_neutral_variant90 = 0x7f0502e4
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary60 = 0x7f05010c
com.example.fragmentsleam:color/material_dynamic_secondary70 = 0x7f0502fc
com.example.fragmentsleam:macro/m3_comp_slider_disabled_handle_color = 0x7f0c0176
com.example.fragmentsleam:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1000db
com.example.fragmentsleam:color/material_dynamic_neutral_variant20 = 0x7f0502dd
com.example.fragmentsleam:id/listMode = 0x7f080106
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f05021e
com.example.fragmentsleam:color/material_dynamic_neutral_variant10 = 0x7f0502db
com.example.fragmentsleam:id/mtrl_calendar_selection_frame = 0x7f080132
com.example.fragmentsleam:id/material_value_index = 0x7f080121
com.example.fragmentsleam:attr/iconTint = 0x7f030270
com.example.fragmentsleam:color/mtrl_textinput_default_box_stroke_color = 0x7f050394
com.example.fragmentsleam:color/foreground_material_dark = 0x7f05005c
com.example.fragmentsleam:color/material_dynamic_neutral50 = 0x7f0502d3
com.example.fragmentsleam:styleable/CardView = 0x7f11001a
com.example.fragmentsleam:attr/dividerColor = 0x7f03019b
com.example.fragmentsleam:dimen/m3_comp_icon_button_large_outlined_outline_width = 0x7f060173
com.example.fragmentsleam:macro/m3_comp_icon_button_standard_unselected_icon_color = 0x7f0c00c5
com.example.fragmentsleam:attr/actionModeCloseButtonStyle = 0x7f030013
com.example.fragmentsleam:dimen/m3_comp_slider_large_active_track_shape_leading = 0x7f06020a
com.example.fragmentsleam:color/material_dynamic_neutral10 = 0x7f0502ce
com.example.fragmentsleam:string/mtrl_badge_numberless_content_description = 0x7f0f005e
com.example.fragmentsleam:color/material_dynamic_color_light_on_error_container = 0x7f0502cc
com.example.fragmentsleam:attr/flow_firstHorizontalBias = 0x7f03021c
com.example.fragmentsleam:attr/defaultScrollFlagsEnabled = 0x7f030191
com.example.fragmentsleam:dimen/design_tab_text_size = 0x7f06008d
com.example.fragmentsleam:color/material_dynamic_color_light_on_error = 0x7f0502cb
com.example.fragmentsleam:macro/m3_comp_extended_fab_large_container_shape = 0x7f0c008b
com.example.fragmentsleam:layout/fragment_pink = 0x7f0b002e
com.example.fragmentsleam:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
com.example.fragmentsleam:color/design_default_color_on_primary = 0x7f050043
com.example.fragmentsleam:dimen/m3_comp_nav_bar_item_vertical_active_indicator_width = 0x7f0601a4
com.example.fragmentsleam:color/material_dynamic_color_light_error = 0x7f0502c9
com.example.fragmentsleam:style/TextAppearance.Material3.HeadlineSmall = 0x7f10022a
com.example.fragmentsleam:color/material_dynamic_color_dark_on_error_container = 0x7f0502c8
com.example.fragmentsleam:color/material_dynamic_color_dark_on_error = 0x7f0502c7
com.example.fragmentsleam:color/material_dynamic_color_dark_error = 0x7f0502c5
com.example.fragmentsleam:color/material_deep_teal_500 = 0x7f0502c3
com.example.fragmentsleam:style/Widget.Material3.ChipGroup = 0x7f1003bb
com.example.fragmentsleam:dimen/m3_comp_button_xlarge_icon_size = 0x7f06012c
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f100042
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_hovered_state_layer_color = 0x7f0c01f3
com.example.fragmentsleam:color/material_cursor_color = 0x7f0502c1
com.example.fragmentsleam:color/material_blue_grey_900 = 0x7f0502bf
com.example.fragmentsleam:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f050263
com.example.fragmentsleam:color/m3expressive_nav_rail_item_label_tint = 0x7f0502bc
com.example.fragmentsleam:color/m3_ref_palette_yellow0 = 0x7f0501db
com.example.fragmentsleam:attr/actionModePasteDrawable = 0x7f030019
com.example.fragmentsleam:layout/material_clock_period_toggle_land = 0x7f0b003d
com.example.fragmentsleam:color/m3expressive_nav_rail_item_icon_tint = 0x7f0502bb
com.example.fragmentsleam:layout/m3_alert_dialog_title = 0x7f0b0034
com.example.fragmentsleam:color/m3expressive_button_outlined_background_color_selector = 0x7f0502ba
com.example.fragmentsleam:dimen/default_navigation_active_text_size = 0x7f06005e
com.example.fragmentsleam:color/m3expressive_bottom_nav_item_label_tint = 0x7f0502b7
com.example.fragmentsleam:color/m3_vibrant_toolbar_icon_button_ripple_color_selector = 0x7f0502b5
com.example.fragmentsleam:color/m3_vibrant_toolbar_icon_button_container_color_selector = 0x7f0502b3
com.example.fragmentsleam:color/m3_tonal_button_ripple_color_selector = 0x7f0502b1
com.example.fragmentsleam:color/m3_timepicker_display_text_color = 0x7f0502ad
com.example.fragmentsleam:color/m3_ref_palette_yellow95 = 0x7f0501e6
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f10011c
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500cb
com.example.fragmentsleam:color/material_personalized_hint_foreground = 0x7f050353
com.example.fragmentsleam:color/m3_timepicker_button_background_color = 0x7f0502a7
com.example.fragmentsleam:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f100048
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f0602d3
com.example.fragmentsleam:color/m3_textfield_stroke_color = 0x7f0502a6
com.example.fragmentsleam:color/m3_dynamic_dark_primary_text_disable_only = 0x7f050080
com.example.fragmentsleam:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060164
com.example.fragmentsleam:color/m3_textfield_indicator_text_color = 0x7f0502a3
com.example.fragmentsleam:id/tag_transition_group = 0x7f0801d7
com.example.fragmentsleam:color/m3_textfield_filled_background_color = 0x7f0502a2
com.example.fragmentsleam:attr/chipSpacingHorizontal = 0x7f0300ce
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents = 0x7f10030a
com.example.fragmentsleam:id/clip_horizontal = 0x7f08007b
com.example.fragmentsleam:attr/itemRippleColor = 0x7f03029b
com.example.fragmentsleam:color/material_dynamic_tertiary80 = 0x7f05030a
com.example.fragmentsleam:color/m3_text_button_foreground_color_selector = 0x7f0502a0
com.example.fragmentsleam:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f06023f
com.example.fragmentsleam:id/clip_vertical = 0x7f08007c
com.example.fragmentsleam:color/m3_tabs_text_color_secondary = 0x7f05029e
com.example.fragmentsleam:macro/m3_comp_button_filled_selected_container_color = 0x7f0c001c
com.example.fragmentsleam:color/m3_ref_palette_neutral20 = 0x7f05015b
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_hovered_label_text_color = 0x7f0c01fb
com.example.fragmentsleam:attr/tabPaddingTop = 0x7f030491
com.example.fragmentsleam:styleable/SearchView = 0x7f11007f
com.example.fragmentsleam:color/m3_sys_color_secondary_fixed_dim = 0x7f050296
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0c01bb
com.example.fragmentsleam:styleable/NavigationBarView = 0x7f110070
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500d8
com.example.fragmentsleam:dimen/m3_comp_icon_button_medium_wide_trailing_space = 0x7f06017d
com.example.fragmentsleam:color/m3_sys_color_on_tertiary_fixed = 0x7f050291
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f0602e3
com.example.fragmentsleam:color/material_timepicker_clock_text_color = 0x7f05035f
com.example.fragmentsleam:color/m3_sys_color_on_secondary_fixed = 0x7f05028f
com.example.fragmentsleam:color/m3_sys_color_on_primary_fixed = 0x7f05028d
com.example.fragmentsleam:dimen/m3_btn_icon_only_icon_padding = 0x7f0600da
com.example.fragmentsleam:attr/dividerThickness = 0x7f0301a0
com.example.fragmentsleam:color/m3_sys_color_light_surface_container_lowest = 0x7f050288
com.example.fragmentsleam:color/m3_sys_color_light_surface_container_highest = 0x7f050286
com.example.fragmentsleam:styleable/ActionBarLayout = 0x7f110001
com.example.fragmentsleam:style/Base.CardView = 0x7f100010
com.example.fragmentsleam:attr/measureWithLargestChild = 0x7f030365
com.example.fragmentsleam:dimen/m3_comp_icon_button_small_wide_leading_space = 0x7f060184
com.example.fragmentsleam:attr/materialCalendarDayOfWeekLabel = 0x7f03032f
com.example.fragmentsleam:color/m3_sys_color_light_surface_container_high = 0x7f050285
com.example.fragmentsleam:color/material_personalized_color_surface_inverse = 0x7f05034a
com.example.fragmentsleam:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f100457
com.example.fragmentsleam:attr/liftOnScroll = 0x7f030302
com.example.fragmentsleam:id/axisRelative = 0x7f08005c
com.example.fragmentsleam:dimen/m3_sys_motion_standard_spring_fast_effects_damping = 0x7f0602ed
com.example.fragmentsleam:color/m3_sys_color_light_primary_container = 0x7f05027f
com.example.fragmentsleam:color/m3_sys_color_light_on_tertiary_container = 0x7f05027b
com.example.fragmentsleam:color/m3_sys_color_light_on_tertiary = 0x7f05027a
com.example.fragmentsleam:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.example.fragmentsleam:color/mtrl_navigation_bar_colored_item_tint = 0x7f05037d
com.example.fragmentsleam:color/m3_sys_color_light_on_error_container = 0x7f050273
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Small.Container.Shape.Round = 0x7f100172
com.example.fragmentsleam:dimen/m3_fab_translation_z_pressed = 0x7f060272
com.example.fragmentsleam:color/m3_sys_color_light_on_error = 0x7f050272
com.example.fragmentsleam:color/m3_sys_color_light_inverse_surface = 0x7f050270
com.example.fragmentsleam:attr/autoSizeMaxTextSize = 0x7f030040
com.example.fragmentsleam:dimen/m3_comp_extended_fab_small_icon_size = 0x7f06014f
com.example.fragmentsleam:dimen/mtrl_progress_circular_radius = 0x7f0603bc
com.example.fragmentsleam:dimen/design_snackbar_padding_horizontal = 0x7f060087
com.example.fragmentsleam:integer/m3_sys_shape_corner_large_increased_corner_family = 0x7f090026
com.example.fragmentsleam:color/design_default_color_on_surface = 0x7f050045
com.example.fragmentsleam:id/vertical = 0x7f080205
com.example.fragmentsleam:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f050269
com.example.fragmentsleam:dimen/mtrl_calendar_title_baseline_to_top = 0x7f060378
com.example.fragmentsleam:macro/m3_comp_dialog_supporting_text_color = 0x7f0c0086
com.example.fragmentsleam:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f050268
com.example.fragmentsleam:macro/m3_comp_fab_surface_icon_color = 0x7f0c00a6
com.example.fragmentsleam:attr/layoutDescription = 0x7f0302ba
com.example.fragmentsleam:attr/collapsingToolbarLayoutLargeSize = 0x7f0300f5
com.example.fragmentsleam:color/m3_sys_color_dynamic_primary_fixed = 0x7f050265
com.example.fragmentsleam:dimen/m3_comp_sheet_side_docked_container_width = 0x7f0601fc
com.example.fragmentsleam:color/material_dynamic_neutral20 = 0x7f0502d0
com.example.fragmentsleam:animator/mtrl_fab_show_motion_spec = 0x7f020025
com.example.fragmentsleam:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f050261
com.example.fragmentsleam:attr/dialogPreferredPadding = 0x7f030197
com.example.fragmentsleam:attr/opticalCenterEnabled = 0x7f0303bd
com.example.fragmentsleam:dimen/design_fab_size_mini = 0x7f060073
com.example.fragmentsleam:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f05025f
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Year = 0x7f10040f
com.example.fragmentsleam:color/material_timepicker_modebutton_tint = 0x7f050361
com.example.fragmentsleam:color/m3_ref_palette_blue50 = 0x7f0500a6
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f05025a
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f050259
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface_container = 0x7f050256
com.example.fragmentsleam:attr/dropDownListViewStyle = 0x7f0301b5
com.example.fragmentsleam:attr/tickMark = 0x7f030503
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_secondary_container = 0x7f050253
com.example.fragmentsleam:dimen/m3_comp_slider_medium_icon_size = 0x7f06020f
com.example.fragmentsleam:attr/startIconDrawable = 0x7f030452
com.example.fragmentsleam:color/material_dynamic_primary100 = 0x7f0502e9
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_pressed_state_layer_color = 0x7f0c0052
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_outline_variant = 0x7f05024f
com.example.fragmentsleam:dimen/m3_btn_disabled_translation_z = 0x7f0600d3
com.example.fragmentsleam:attr/flow_maxElementsWrap = 0x7f030228
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f05024b
com.example.fragmentsleam:style/Base.Theme.AppCompat.Dialog = 0x7f10004c
com.example.fragmentsleam:dimen/abc_action_button_min_height_material = 0x7f06000d
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f050249
com.example.fragmentsleam:dimen/m3_comp_menu_container_elevation = 0x7f06019d
com.example.fragmentsleam:dimen/m3_navigation_rail_expanded_active_indicator_height = 0x7f06028f
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_primary = 0x7f050246
com.example.fragmentsleam:style/Widget.Material3.Button.TextButton.Dialog = 0x7f1003a5
com.example.fragmentsleam:styleable/ForegroundLinearLayout = 0x7f11003a
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.Large = 0x7f100197
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f050240
com.example.fragmentsleam:attr/grid_columns = 0x7f030243
com.example.fragmentsleam:id/invisible = 0x7f0800f2
com.example.fragmentsleam:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f100452
com.example.fragmentsleam:id/accessibility_custom_action_10 = 0x7f080013
com.example.fragmentsleam:animator/mtrl_extended_fab_hide_motion_spec = 0x7f020021
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_error_container = 0x7f05023f
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_background = 0x7f05023d
com.example.fragmentsleam:attr/textAppearanceLineHeightEnabled = 0x7f0304c2
com.example.fragmentsleam:color/m3_sys_color_light_error_container = 0x7f05026d
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f05023c
com.example.fragmentsleam:attr/constraint_referenced_ids = 0x7f030140
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_tertiary = 0x7f05023b
com.example.fragmentsleam:attr/drawableTopCompat = 0x7f0301b0
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f050238
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f050237
com.example.fragmentsleam:layout/design_bottom_sheet_dialog = 0x7f0b001f
com.example.fragmentsleam:id/accessibility_custom_action_25 = 0x7f080023
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f050233
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f050231
com.example.fragmentsleam:color/m3_ref_palette_blue40 = 0x7f0500a5
com.example.fragmentsleam:dimen/m3_extended_fab_translation_z_pressed = 0x7f06026a
com.example.fragmentsleam:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_outline = 0x7f05022c
com.example.fragmentsleam:id/pooling_container_listener_holder_tag = 0x7f08017c
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f050235
com.example.fragmentsleam:styleable/MaterialCalendarItem = 0x7f110057
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f050227
com.example.fragmentsleam:color/m3_sys_color_dark_surface_container_lowest = 0x7f050216
com.example.fragmentsleam:drawable/abc_list_selector_holo_dark = 0x7f070057
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant0 = 0x7f05016e
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f050226
com.example.fragmentsleam:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0c016d
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f050225
com.example.fragmentsleam:style/Widget.MaterialComponents.Chip.Filter = 0x7f100476
com.example.fragmentsleam:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f100014
com.example.fragmentsleam:color/m3_elevated_chip_background_color = 0x7f050087
com.example.fragmentsleam:attr/textAppearanceListItemSmall = 0x7f0304c5
com.example.fragmentsleam:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.example.fragmentsleam:attr/listDividerAlertDialog = 0x7f03030c
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_background = 0x7f050221
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f10003f
com.example.fragmentsleam:dimen/m3_comp_split_button_small_trailing_button_trailing_space = 0x7f06022b
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f05021f
com.example.fragmentsleam:id/grouping = 0x7f0800db
com.example.fragmentsleam:color/material_personalized_color_surface_variant = 0x7f05034b
com.example.fragmentsleam:drawable/abc_vector_test = 0x7f070077
com.example.fragmentsleam:color/m3_sys_color_dark_tertiary = 0x7f050219
com.example.fragmentsleam:styleable/include = 0x7f11009f
com.example.fragmentsleam:color/m3_sys_color_dark_surface_container_low = 0x7f050215
com.example.fragmentsleam:color/m3_sys_color_dark_surface_container_high = 0x7f050213
com.example.fragmentsleam:attr/colorSurfaceContainer = 0x7f03012d
com.example.fragmentsleam:macro/m3_comp_fab_primary_container_icon_color = 0x7f0c009e
com.example.fragmentsleam:color/m3_sys_color_dark_surface_bright = 0x7f050211
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f050229
com.example.fragmentsleam:color/m3_sys_color_dark_outline_variant = 0x7f05020b
com.example.fragmentsleam:color/m3_sys_color_dark_outline = 0x7f05020a
com.example.fragmentsleam:color/m3_sys_color_dark_on_tertiary = 0x7f050208
com.example.fragmentsleam:color/abc_decor_view_status_guard = 0x7f050005
com.example.fragmentsleam:dimen/design_navigation_item_horizontal_padding = 0x7f06007a
com.example.fragmentsleam:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.example.fragmentsleam:color/m3_dark_primary_text_disable_only = 0x7f050079
com.example.fragmentsleam:color/m3_sys_color_dark_on_surface = 0x7f050206
com.example.fragmentsleam:style/ShapeAppearance.Material3.Corner.ExtraExtraLarge = 0x7f100192
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f100075
com.example.fragmentsleam:color/m3_sys_color_dark_on_secondary_container = 0x7f050205
com.example.fragmentsleam:color/m3_sys_color_dark_on_error = 0x7f050200
com.example.fragmentsleam:id/entireSpace = 0x7f0800ba
com.example.fragmentsleam:attr/floatingActionButtonLargePrimaryStyle = 0x7f03020a
com.example.fragmentsleam:color/material_slider_thumb_color = 0x7f05035c
com.example.fragmentsleam:color/m3_sys_color_dark_error = 0x7f0501fa
com.example.fragmentsleam:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f06019a
com.example.fragmentsleam:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1000a8
com.example.fragmentsleam:color/m3_standard_toolbar_icon_button_icon_color_selector = 0x7f0501f5
com.example.fragmentsleam:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f100262
com.example.fragmentsleam:color/m3_standard_toolbar_icon_button_container_color_selector = 0x7f0501f4
com.example.fragmentsleam:color/m3_slider_thumb_color_legacy = 0x7f0501f2
com.example.fragmentsleam:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f100344
com.example.fragmentsleam:attr/navigationViewStyle = 0x7f0303af
com.example.fragmentsleam:color/m3_slider_thumb_color = 0x7f0501f1
com.example.fragmentsleam:color/m3_slider_inactive_track_color_legacy = 0x7f0501f0
com.example.fragmentsleam:color/m3_slider_inactive_track_color = 0x7f0501ef
com.example.fragmentsleam:dimen/m3_card_elevated_hovered_z = 0x7f0600ee
com.example.fragmentsleam:attr/textInputFilledDenseStyle = 0x7f0304dd
com.example.fragmentsleam:color/m3_slider_active_track_color = 0x7f0501eb
com.example.fragmentsleam:attr/colorButtonNormal = 0x7f0300fd
com.example.fragmentsleam:dimen/design_bottom_navigation_shadow_height = 0x7f06006b
com.example.fragmentsleam:color/m3_slider_active_tick_marks_color = 0x7f0501ea
com.example.fragmentsleam:color/m3_ref_palette_yellow98 = 0x7f0501e7
com.example.fragmentsleam:attr/colorBackgroundFloating = 0x7f0300fc
com.example.fragmentsleam:color/material_dynamic_neutral_variant100 = 0x7f0502dc
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents.Light = 0x7f100095
com.example.fragmentsleam:string/material_timepicker_text_input_mode_description = 0x7f0f005d
com.example.fragmentsleam:attr/motion_postLayoutCollision = 0x7f0303a6
com.example.fragmentsleam:color/m3_ref_palette_yellow90 = 0x7f0501e5
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral100 = 0x7f0500c9
com.example.fragmentsleam:color/m3_ref_palette_yellow60 = 0x7f0501e2
com.example.fragmentsleam:style/Widget.MaterialComponents.Snackbar = 0x7f1004ac
com.example.fragmentsleam:color/m3_ref_palette_yellow50 = 0x7f0501e1
com.example.fragmentsleam:color/m3_ref_palette_yellow30 = 0x7f0501df
com.example.fragmentsleam:color/m3_ref_palette_yellow20 = 0x7f0501de
com.example.fragmentsleam:macro/m3_comp_slider_handle_color = 0x7f0c0179
com.example.fragmentsleam:color/m3_ref_palette_white = 0x7f0501da
com.example.fragmentsleam:color/m3_ref_palette_tertiary90 = 0x7f0501d6
com.example.fragmentsleam:color/m3_ref_palette_red90 = 0x7f0501bb
com.example.fragmentsleam:color/switch_thumb_normal_material_light = 0x7f0503ae
com.example.fragmentsleam:attr/showDividers = 0x7f03042e
com.example.fragmentsleam:color/design_default_color_secondary_variant = 0x7f05004a
com.example.fragmentsleam:layout/mtrl_alert_select_dialog_item = 0x7f0b004a
com.example.fragmentsleam:color/m3_ref_palette_tertiary80 = 0x7f0501d5
com.example.fragmentsleam:color/m3_ref_palette_tertiary70 = 0x7f0501d4
com.example.fragmentsleam:color/m3_ref_palette_tertiary60 = 0x7f0501d3
com.example.fragmentsleam:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f10027c
com.example.fragmentsleam:color/m3_ref_palette_tertiary40 = 0x7f0501d1
com.example.fragmentsleam:color/m3_ref_palette_secondary95 = 0x7f0501c9
com.example.fragmentsleam:dimen/mtrl_progress_circular_inset_small = 0x7f0603bb
com.example.fragmentsleam:color/m3_sys_color_light_on_primary_container = 0x7f050275
com.example.fragmentsleam:attr/colorOnPrimarySurface = 0x7f030110
com.example.fragmentsleam:drawable/mtrl_checkbox_button_icon = 0x7f0700b9
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_secondary = 0x7f050252
com.example.fragmentsleam:color/m3_selection_control_ripple_color_selector = 0x7f0501e8
com.example.fragmentsleam:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1000d3
com.example.fragmentsleam:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f060338
com.example.fragmentsleam:color/material_dynamic_primary0 = 0x7f0502e7
com.example.fragmentsleam:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.example.fragmentsleam:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1002e5
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_vertical_active_indicator_height = 0x7f0601b9
com.example.fragmentsleam:color/m3_ref_palette_secondary50 = 0x7f0501c4
com.example.fragmentsleam:dimen/mtrl_card_elevation = 0x7f060383
com.example.fragmentsleam:color/m3_ref_palette_secondary30 = 0x7f0501c2
com.example.fragmentsleam:dimen/design_bottom_navigation_item_max_width = 0x7f060067
com.example.fragmentsleam:color/m3_ref_palette_secondary100 = 0x7f0501c0
com.example.fragmentsleam:color/m3_ref_palette_secondary0 = 0x7f0501be
com.example.fragmentsleam:color/m3_ref_palette_red98 = 0x7f0501bd
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f100323
com.example.fragmentsleam:color/m3_ref_palette_red95 = 0x7f0501bc
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f05022d
com.example.fragmentsleam:attr/badgeStyle = 0x7f030058
com.example.fragmentsleam:color/m3_ref_palette_red80 = 0x7f0501ba
com.example.fragmentsleam:attr/borderRoundPercent = 0x7f030077
com.example.fragmentsleam:color/m3_ref_palette_red70 = 0x7f0501b9
com.example.fragmentsleam:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0c0196
com.example.fragmentsleam:color/mtrl_navigation_bar_colored_ripple_color = 0x7f05037e
com.example.fragmentsleam:color/m3_ref_palette_red60 = 0x7f0501b8
com.example.fragmentsleam:color/m3_ref_palette_red50 = 0x7f0501b7
com.example.fragmentsleam:attr/actionBarItemBackground = 0x7f030003
com.example.fragmentsleam:color/m3_ref_palette_red30 = 0x7f0501b5
com.example.fragmentsleam:color/m3_ref_palette_red20 = 0x7f0501b4
com.example.fragmentsleam:attr/activeIndicatorLabelPadding = 0x7f030026
com.example.fragmentsleam:dimen/m3_ripple_pressed_alpha = 0x7f0602a6
com.example.fragmentsleam:styleable/Transition = 0x7f110098
com.example.fragmentsleam:attr/logoDescription = 0x7f03031b
com.example.fragmentsleam:color/m3_navigation_item_background_color = 0x7f050094
com.example.fragmentsleam:dimen/abc_search_view_preferred_width = 0x7f060037
com.example.fragmentsleam:style/Theme.Material3.Dark.SideSheetDialog = 0x7f100274
com.example.fragmentsleam:color/m3_ref_palette_purple90 = 0x7f0501ae
com.example.fragmentsleam:attr/springDamping = 0x7f030449
com.example.fragmentsleam:color/m3_ref_palette_purple50 = 0x7f0501aa
com.example.fragmentsleam:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0c0144
com.example.fragmentsleam:dimen/design_navigation_item_icon_padding = 0x7f06007b
com.example.fragmentsleam:color/m3_ref_palette_purple40 = 0x7f0501a9
com.example.fragmentsleam:attr/indicatorDirectionCircular = 0x7f030280
com.example.fragmentsleam:attr/navigationIconTint = 0x7f0303ac
com.example.fragmentsleam:attr/customColorDrawableValue = 0x7f030180
com.example.fragmentsleam:color/m3_ref_palette_purple20 = 0x7f0501a7
com.example.fragmentsleam:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f0601d2
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f1002b3
com.example.fragmentsleam:dimen/design_snackbar_min_width = 0x7f060086
com.example.fragmentsleam:color/m3_ref_palette_purple100 = 0x7f0501a6
com.example.fragmentsleam:dimen/m3_carousel_extra_small_item_size = 0x7f0600f3
com.example.fragmentsleam:color/m3_ref_palette_purple0 = 0x7f0501a4
com.example.fragmentsleam:attr/materialCardViewStyle = 0x7f030340
com.example.fragmentsleam:color/m3_ref_palette_primary98 = 0x7f0501a2
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f10031f
com.example.fragmentsleam:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1000a7
com.example.fragmentsleam:color/m3_ref_palette_primary90 = 0x7f0501a0
com.example.fragmentsleam:color/m3_ref_palette_primary80 = 0x7f05019f
com.example.fragmentsleam:color/m3_ref_palette_primary60 = 0x7f05019d
com.example.fragmentsleam:id/actionDownUp = 0x7f080032
com.example.fragmentsleam:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f06031e
com.example.fragmentsleam:color/m3_ref_palette_primary40 = 0x7f05019b
com.example.fragmentsleam:attr/fontFamily = 0x7f030230
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f100096
com.example.fragmentsleam:color/m3_ref_palette_primary10 = 0x7f050197
com.example.fragmentsleam:attr/dividerInsetEnd = 0x7f03019d
com.example.fragmentsleam:color/m3_ref_palette_pink98 = 0x7f050195
com.example.fragmentsleam:drawable/ic_m3_chip_check = 0x7f070095
com.example.fragmentsleam:attr/methodName = 0x7f030369
com.example.fragmentsleam:color/m3_ref_palette_pink90 = 0x7f050193
com.example.fragmentsleam:color/m3_ref_palette_pink80 = 0x7f050192
com.example.fragmentsleam:dimen/m3_comp_split_button_xlarge_trailing_button_leading_space = 0x7f06022f
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f100133
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_hovered_label_text_color = 0x7f0c01d9
com.example.fragmentsleam:color/m3_ref_palette_pink60 = 0x7f050190
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar = 0x7f100486
com.example.fragmentsleam:macro/m3_comp_filter_chip_container_shape = 0x7f0c00b6
com.example.fragmentsleam:dimen/m3_comp_toolbar_floating_container_trailing_space = 0x7f060256
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_error = 0x7f05023e
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker = 0x7f1004bf
com.example.fragmentsleam:color/m3_ref_palette_pink30 = 0x7f05018d
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.TextView = 0x7f100122
com.example.fragmentsleam:dimen/notification_large_icon_height = 0x7f0603f4
com.example.fragmentsleam:attr/indeterminateAnimationTypeCircular = 0x7f03027b
com.example.fragmentsleam:styleable/ClockHandView = 0x7f110021
com.example.fragmentsleam:color/m3_ref_palette_pink10 = 0x7f05018a
com.example.fragmentsleam:attr/colorTertiaryContainer = 0x7f030137
com.example.fragmentsleam:dimen/design_navigation_elevation = 0x7f060077
com.example.fragmentsleam:color/m3_ref_palette_cyan100 = 0x7f0500bc
com.example.fragmentsleam:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f1001a6
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0c01bc
com.example.fragmentsleam:color/m3_ref_palette_pink0 = 0x7f050189
com.example.fragmentsleam:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1000f3
com.example.fragmentsleam:attr/dayTodayStyle = 0x7f03018d
com.example.fragmentsleam:color/m3_ref_palette_orange90 = 0x7f050186
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f10003b
com.example.fragmentsleam:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0c0067
com.example.fragmentsleam:color/m3_ref_palette_orange60 = 0x7f050183
com.example.fragmentsleam:color/m3_ref_palette_orange50 = 0x7f050182
com.example.fragmentsleam:attr/menuAlignmentMode = 0x7f030367
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f0602e0
com.example.fragmentsleam:attr/elevation = 0x7f0301bc
com.example.fragmentsleam:attr/targetId = 0x7f03049a
com.example.fragmentsleam:color/m3_ref_palette_orange100 = 0x7f05017e
com.example.fragmentsleam:attr/checkedState = 0x7f0300c0
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c0137
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant98 = 0x7f05017a
com.example.fragmentsleam:attr/itemPaddingBottom = 0x7f030299
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant90 = 0x7f050178
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant80 = 0x7f050177
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant50 = 0x7f050174
com.example.fragmentsleam:style/Widget.Material3.FloatingToolbar.Button.Vibrant = 0x7f1003ee
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant20 = 0x7f050171
com.example.fragmentsleam:style/Theme.Material3.Dark.Dialog = 0x7f10026f
com.example.fragmentsleam:id/mtrl_picker_title_text = 0x7f080141
com.example.fragmentsleam:attr/thumbTintMode = 0x7f0304fd
com.example.fragmentsleam:attr/animationMode = 0x7f030035
com.example.fragmentsleam:color/m3_ref_palette_purple98 = 0x7f0501b0
com.example.fragmentsleam:color/m3_ref_palette_grey60 = 0x7f050143
com.example.fragmentsleam:style/Base.Theme.Material3.Dark.Dialog = 0x7f10005b
com.example.fragmentsleam:dimen/compat_button_inset_vertical_material = 0x7f060057
com.example.fragmentsleam:color/m3_ref_palette_neutral99 = 0x7f05016d
com.example.fragmentsleam:string/material_timepicker_minute = 0x7f0f005a
com.example.fragmentsleam:color/m3_ref_palette_neutral95 = 0x7f05016a
com.example.fragmentsleam:drawable/notify_panel_notification_icon_bg = 0x7f0700e9
com.example.fragmentsleam:attr/closeIconVisible = 0x7f0300e9
com.example.fragmentsleam:color/m3_ref_palette_neutral80 = 0x7f050165
com.example.fragmentsleam:attr/materialCalendarHeaderToggleButton = 0x7f030337
com.example.fragmentsleam:attr/colorOnTertiaryContainer = 0x7f030119
com.example.fragmentsleam:dimen/m3_comp_slider_active_handle_width = 0x7f060201
com.example.fragmentsleam:attr/sizePercent = 0x7f03043e
com.example.fragmentsleam:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060163
com.example.fragmentsleam:color/m3_ref_palette_neutral70 = 0x7f050164
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_header_space_minimum = 0x7f0601b2
com.example.fragmentsleam:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f100304
com.example.fragmentsleam:attr/onCross = 0x7f0303b6
com.example.fragmentsleam:color/m3_ref_palette_neutral60 = 0x7f050163
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f10041c
com.example.fragmentsleam:attr/autoSizeStepGranularity = 0x7f030043
com.example.fragmentsleam:color/m3_ref_palette_neutral6 = 0x7f050162
com.example.fragmentsleam:color/m3_ref_palette_neutral50 = 0x7f050161
com.example.fragmentsleam:id/sharedValueUnset = 0x7f0801a7
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_error = 0x7f050222
com.example.fragmentsleam:layout/material_clock_period_toggle = 0x7f0b003c
com.example.fragmentsleam:id/accessibility_custom_action_30 = 0x7f080029
com.example.fragmentsleam:dimen/mtrl_alert_dialog_background_inset_top = 0x7f06032e
com.example.fragmentsleam:dimen/m3_comp_outlined_text_field_outline_width = 0x7f0601ce
com.example.fragmentsleam:attr/imageZoom = 0x7f030279
com.example.fragmentsleam:attr/motionDurationExtraLong4 = 0x7f030379
com.example.fragmentsleam:attr/itemPadding = 0x7f030298
com.example.fragmentsleam:color/m3_ref_palette_neutral40 = 0x7f050160
com.example.fragmentsleam:color/m3_ref_palette_neutral30 = 0x7f05015e
com.example.fragmentsleam:attr/liftOnScrollTargetViewId = 0x7f030304
com.example.fragmentsleam:dimen/m3_badge_with_text_size = 0x7f0600bd
com.example.fragmentsleam:attr/contentInsetLeft = 0x7f030158
com.example.fragmentsleam:color/material_harmonized_color_error_container = 0x7f050316
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary0 = 0x7f050105
com.example.fragmentsleam:color/m3_ref_palette_neutral22 = 0x7f05015c
com.example.fragmentsleam:color/m3_ref_palette_neutral12 = 0x7f050159
com.example.fragmentsleam:color/material_slider_inactive_track_color = 0x7f05035b
com.example.fragmentsleam:color/m3_ref_palette_neutral100 = 0x7f050158
com.example.fragmentsleam:dimen/highlight_alpha_material_dark = 0x7f060096
com.example.fragmentsleam:color/m3_ref_palette_neutral0 = 0x7f050156
com.example.fragmentsleam:color/m3_ref_palette_grey_variant90 = 0x7f050153
com.example.fragmentsleam:attr/itemTextAppearanceActive = 0x7f0302a7
com.example.fragmentsleam:color/m3_card_ripple_color = 0x7f05006c
com.example.fragmentsleam:color/m3_ref_palette_grey_variant80 = 0x7f050152
com.example.fragmentsleam:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.example.fragmentsleam:color/m3_ref_palette_grey_variant30 = 0x7f05014d
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1000c6
com.example.fragmentsleam:id/blocking = 0x7f080063
com.example.fragmentsleam:color/m3_ref_palette_grey90 = 0x7f050146
com.example.fragmentsleam:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1000d6
com.example.fragmentsleam:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f100271
com.example.fragmentsleam:color/m3_ref_palette_grey30 = 0x7f050140
com.example.fragmentsleam:color/m3_ref_palette_grey20 = 0x7f05013f
com.example.fragmentsleam:dimen/m3_comp_filter_chip_container_height = 0x7f060169
com.example.fragmentsleam:dimen/m3_comp_fab_primary_container_hovered_state_layer_opacity = 0x7f06015c
com.example.fragmentsleam:color/m3_ref_palette_grey10 = 0x7f05013d
com.example.fragmentsleam:dimen/mtrl_btn_letter_spacing = 0x7f060348
com.example.fragmentsleam:color/m3_ref_palette_grey0 = 0x7f05013c
com.example.fragmentsleam:style/Widget.MaterialComponents.BottomSheet = 0x7f100463
com.example.fragmentsleam:color/m3_ref_palette_green98 = 0x7f05013b
com.example.fragmentsleam:attr/constraints = 0x7f030142
com.example.fragmentsleam:color/m3_ref_palette_green95 = 0x7f05013a
com.example.fragmentsleam:color/m3_ref_palette_green80 = 0x7f050138
com.example.fragmentsleam:macro/m3_comp_button_outlined_hovered_state_layer_color = 0x7f0c0032
com.example.fragmentsleam:color/m3_ref_palette_green20 = 0x7f050132
com.example.fragmentsleam:drawable/ic_call_answer_low = 0x7f070089
com.example.fragmentsleam:id/path = 0x7f080177
com.example.fragmentsleam:color/m3_ref_palette_green0 = 0x7f05012f
com.example.fragmentsleam:attr/motionDurationLong4 = 0x7f03037d
com.example.fragmentsleam:color/m3_ref_palette_error95 = 0x7f05012c
com.example.fragmentsleam:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600a9
com.example.fragmentsleam:dimen/design_bottom_navigation_icon_size = 0x7f060066
com.example.fragmentsleam:id/SHOW_PATH = 0x7f080009
com.example.fragmentsleam:id/autoLimit = 0x7f08005a
com.example.fragmentsleam:drawable/$m3_avd_hide_password__1 = 0x7f070008
com.example.fragmentsleam:color/m3_sys_color_light_surface = 0x7f050282
com.example.fragmentsleam:macro/m3_comp_fab_large_container_shape = 0x7f0c009a
com.example.fragmentsleam:attr/itemShapeInsetEnd = 0x7f0302a0
com.example.fragmentsleam:color/m3_ref_palette_error90 = 0x7f05012b
com.example.fragmentsleam:dimen/m3_sys_elevation_level1 = 0x7f0602c0
com.example.fragmentsleam:color/m3_ref_palette_error80 = 0x7f05012a
com.example.fragmentsleam:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0603a1
com.example.fragmentsleam:attr/buttonStyleSmall = 0x7f03009a
com.example.fragmentsleam:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f060387
com.example.fragmentsleam:style/Widget.Material3.LoadingIndicator = 0x7f1003f7
com.example.fragmentsleam:color/m3_ref_palette_error70 = 0x7f050129
com.example.fragmentsleam:attr/actionModeShareDrawable = 0x7f03001c
com.example.fragmentsleam:color/m3_ref_palette_error60 = 0x7f050128
com.example.fragmentsleam:string/mtrl_picker_toggle_to_day_selection = 0x7f0f0091
com.example.fragmentsleam:color/m3_ref_palette_error50 = 0x7f050127
com.example.fragmentsleam:dimen/m3_comp_search_bar_container_height = 0x7f0601ee
com.example.fragmentsleam:color/m3_ref_palette_error40 = 0x7f050126
com.example.fragmentsleam:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f06024e
com.example.fragmentsleam:color/m3_ref_palette_error30 = 0x7f050125
com.example.fragmentsleam:color/m3_efab_ripple_color_selector = 0x7f050086
com.example.fragmentsleam:attr/motionEasingStandardAccelerateInterpolator = 0x7f03038f
com.example.fragmentsleam:color/m3_ref_palette_error0 = 0x7f050121
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary99 = 0x7f050120
com.example.fragmentsleam:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f0602db
com.example.fragmentsleam:attr/listPreferredItemPaddingLeft = 0x7f030315
com.example.fragmentsleam:id/snapMargins = 0x7f0801b3
com.example.fragmentsleam:attr/cornerSize = 0x7f03016f
com.example.fragmentsleam:style/Widget.Material3.LoadingIndicator.Contained = 0x7f1003f8
com.example.fragmentsleam:color/material_personalized__highlighted_text_inverse = 0x7f050324
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary95 = 0x7f05011e
com.example.fragmentsleam:id/material_timepicker_cancel_button = 0x7f08011c
com.example.fragmentsleam:id/uniform = 0x7f080201
com.example.fragmentsleam:dimen/m3_comp_button_small_trailing_space = 0x7f060126
com.example.fragmentsleam:attr/drawableLeftCompat = 0x7f0301aa
com.example.fragmentsleam:color/m3_sys_color_light_outline = 0x7f05027c
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary30 = 0x7f050117
com.example.fragmentsleam:attr/layout_constraintTop_creator = 0x7f0302e5
com.example.fragmentsleam:color/mtrl_tabs_icon_color_selector = 0x7f05038f
com.example.fragmentsleam:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0c0069
com.example.fragmentsleam:macro/m3_comp_button_elevated_container_color = 0x7f0c0014
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary100 = 0x7f050115
com.example.fragmentsleam:attr/listItemLayout = 0x7f03030d
com.example.fragmentsleam:color/material_personalized_color_primary_text_inverse = 0x7f05033d
com.example.fragmentsleam:macro/m3_comp_button_large_selected_container_shape_round = 0x7f0c0025
com.example.fragmentsleam:attr/materialClockStyle = 0x7f030342
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary99 = 0x7f050112
com.example.fragmentsleam:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070025
com.example.fragmentsleam:attr/fontProviderFetchStrategy = 0x7f030233
com.example.fragmentsleam:color/m3_dynamic_default_color_secondary_text = 0x7f050082
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary98 = 0x7f050111
com.example.fragmentsleam:color/m3_ref_palette_tertiary95 = 0x7f0501d7
com.example.fragmentsleam:style/Widget.Material3.FloatingToolbar.IconButton.Vibrant = 0x7f1003f0
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary90 = 0x7f05010f
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary100 = 0x7f050107
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f100495
com.example.fragmentsleam:id/search_edit_frame = 0x7f08019d
com.example.fragmentsleam:dimen/highlight_alpha_material_colored = 0x7f060095
com.example.fragmentsleam:string/abc_activitychooserview_choose_application = 0x7f0f0005
com.example.fragmentsleam:dimen/m3_comp_button_text_pressed_state_layer_opacity = 0x7f060129
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary10 = 0x7f050106
com.example.fragmentsleam:styleable/AppCompatImageView = 0x7f11000e
com.example.fragmentsleam:attr/cardUseCompatPadding = 0x7f0300a3
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary95 = 0x7f050102
com.example.fragmentsleam:attr/layout_constraintWidth_default = 0x7f0302ec
com.example.fragmentsleam:id/textTop = 0x7f0801e1
com.example.fragmentsleam:color/m3_ref_palette_neutral24 = 0x7f05015d
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary90 = 0x7f050101
com.example.fragmentsleam:drawable/m3_password_eye = 0x7f0700a2
com.example.fragmentsleam:color/m3_sys_color_light_on_secondary_container = 0x7f050277
com.example.fragmentsleam:color/m3_assist_chip_icon_tint_color = 0x7f050061
com.example.fragmentsleam:dimen/m3_comp_input_chip_container_elevation = 0x7f060196
com.example.fragmentsleam:styleable/ExtendedFloatingActionButton = 0x7f110032
com.example.fragmentsleam:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700c0
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary80 = 0x7f050100
com.example.fragmentsleam:color/material_dynamic_secondary80 = 0x7f0502fd
com.example.fragmentsleam:attr/staggered = 0x7f03044f
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_none = 0x7f0602fc
com.example.fragmentsleam:color/material_dynamic_secondary10 = 0x7f0502f5
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary50 = 0x7f0500fd
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0c0131
com.example.fragmentsleam:layout/abc_action_bar_title_item = 0x7f0b0000
com.example.fragmentsleam:attr/isLightTheme = 0x7f030288
com.example.fragmentsleam:dimen/m3_extended_fab_start_padding = 0x7f060265
com.example.fragmentsleam:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1001cf
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary40 = 0x7f0500fc
com.example.fragmentsleam:attr/clockNumberTextColor = 0x7f0300e2
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary0 = 0x7f0500f7
com.example.fragmentsleam:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0c01a4
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500f6
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0500f1
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0500ef
com.example.fragmentsleam:style/Widget.Material3.SideSheet.Modal = 0x7f100430
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Badge = 0x7f10023c
com.example.fragmentsleam:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0c006c
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500ee
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0500eb
com.example.fragmentsleam:layout/mtrl_search_view = 0x7f0b0066
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f0602d8
com.example.fragmentsleam:animator/m3_card_state_list_anim = 0x7f02000f
com.example.fragmentsleam:color/abc_tint_spinner = 0x7f050017
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500e9
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0500e8
com.example.fragmentsleam:macro/m3_comp_button_outlined_hovered_icon_color = 0x7f0c002f
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0500e2
com.example.fragmentsleam:xml/m3_button_group_child_size_change = 0x7f120002
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500e0
com.example.fragmentsleam:attr/itemShapeInsetBottom = 0x7f03029f
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500dc
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500db
com.example.fragmentsleam:dimen/m3_comp_extended_fab_large_icon_size = 0x7f06013e
com.example.fragmentsleam:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500da
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary80 = 0x7f05011c
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500d3
com.example.fragmentsleam:attr/statusBarForeground = 0x7f030463
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500d2
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0c0120
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500d1
com.example.fragmentsleam:attr/indicatorTrackGapSize = 0x7f030284
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Button.Xlarge.Container.Shape.Round = 0x7f100168
com.example.fragmentsleam:attr/showMarker = 0x7f03042f
com.example.fragmentsleam:attr/windowNoTitle = 0x7f030570
com.example.fragmentsleam:dimen/m3_comp_button_small_icon_size = 0x7f060123
com.example.fragmentsleam:id/decelerateAndComplete = 0x7f080092
com.example.fragmentsleam:attr/layout_constraintStart_toEndOf = 0x7f0302e2
com.example.fragmentsleam:attr/state_lifted = 0x7f030460
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500cc
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500ca
com.example.fragmentsleam:attr/thumbIconSize = 0x7f0304f5
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral10 = 0x7f0500c8
com.example.fragmentsleam:attr/labelStyle = 0x7f0302b3
com.example.fragmentsleam:color/m3_ref_palette_cyan98 = 0x7f0500c6
com.example.fragmentsleam:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f060370
com.example.fragmentsleam:attr/trackThickness = 0x7f03053f
com.example.fragmentsleam:color/m3_ref_palette_cyan90 = 0x7f0500c4
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant60 = 0x7f050175
com.example.fragmentsleam:color/m3_ref_palette_cyan80 = 0x7f0500c3
com.example.fragmentsleam:dimen/m3_searchview_elevation = 0x7f0602b1
com.example.fragmentsleam:color/m3_ref_palette_cyan70 = 0x7f0500c2
com.example.fragmentsleam:layout/design_navigation_menu_item = 0x7f0b002a
com.example.fragmentsleam:id/accessibility_custom_action_18 = 0x7f08001b
com.example.fragmentsleam:attr/backgroundColor = 0x7f030048
com.example.fragmentsleam:color/m3_ref_palette_cyan50 = 0x7f0500c0
com.example.fragmentsleam:attr/iconEndPadding = 0x7f03026a
com.example.fragmentsleam:integer/mtrl_view_gone = 0x7f090042
com.example.fragmentsleam:color/m3_ref_palette_cyan20 = 0x7f0500bd
com.example.fragmentsleam:color/m3_ref_palette_cyan0 = 0x7f0500ba
com.example.fragmentsleam:attr/textAppearanceLabelSmall = 0x7f0304bf
com.example.fragmentsleam:attr/buttonBarNeutralButtonStyle = 0x7f03008e
com.example.fragmentsleam:dimen/m3_searchbar_outlined_stroke_width = 0x7f0602ac
com.example.fragmentsleam:color/m3_ref_palette_blue_variant90 = 0x7f0500b7
com.example.fragmentsleam:attr/colorPrimaryFixed = 0x7f030121
com.example.fragmentsleam:dimen/design_snackbar_background_corner_radius = 0x7f060082
com.example.fragmentsleam:id/never = 0x7f08014e
com.example.fragmentsleam:color/m3_ref_palette_blue_variant60 = 0x7f0500b4
com.example.fragmentsleam:color/m3_ref_palette_blue_variant50 = 0x7f0500b3
com.example.fragmentsleam:color/m3_ref_palette_blue_variant30 = 0x7f0500b1
com.example.fragmentsleam:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f100052
com.example.fragmentsleam:attr/materialCalendarTheme = 0x7f03033b
com.example.fragmentsleam:attr/keylines = 0x7f0302ae
com.example.fragmentsleam:color/m3_ref_palette_tertiary0 = 0x7f0501cc
com.example.fragmentsleam:color/m3_ref_palette_blue_variant100 = 0x7f0500af
com.example.fragmentsleam:id/info = 0x7f0800f1
com.example.fragmentsleam:color/m3_sys_color_light_error = 0x7f05026c
com.example.fragmentsleam:color/material_personalized_color_on_surface_variant = 0x7f050334
com.example.fragmentsleam:attr/fontProviderPackage = 0x7f030235
com.example.fragmentsleam:attr/state_liftable = 0x7f03045f
com.example.fragmentsleam:color/m3_ref_palette_blue98 = 0x7f0500ac
com.example.fragmentsleam:color/m3_ref_palette_blue90 = 0x7f0500aa
com.example.fragmentsleam:color/m3_ref_palette_blue80 = 0x7f0500a9
com.example.fragmentsleam:color/m3_ref_palette_blue30 = 0x7f0500a4
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f05025e
com.example.fragmentsleam:layout/material_textinput_timepicker = 0x7f0b0041
com.example.fragmentsleam:color/m3_ref_palette_blue0 = 0x7f0500a0
com.example.fragmentsleam:attr/layout_constraintVertical_weight = 0x7f0302ea
com.example.fragmentsleam:color/m3_ref_palette_secondary40 = 0x7f0501c3
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0c01c8
com.example.fragmentsleam:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f100427
com.example.fragmentsleam:macro/m3_comp_icon_button_large_container_shape_square = 0x7f0c00ba
com.example.fragmentsleam:dimen/m3_comp_nav_bar_item_vertical_container_between_space = 0x7f0601a5
com.example.fragmentsleam:color/m3_primary_text_disable_only = 0x7f05009c
com.example.fragmentsleam:dimen/m3_comp_icon_button_large_default_trailing_space = 0x7f06016f
com.example.fragmentsleam:attr/motionSpringSlowSpatial = 0x7f0303a3
com.example.fragmentsleam:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f050099
com.example.fragmentsleam:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f050098
com.example.fragmentsleam:attr/constraintRotate = 0x7f03013c
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f100354
com.example.fragmentsleam:color/m3_navigation_item_text_color = 0x7f050097
com.example.fragmentsleam:color/mtrl_textinput_hovered_box_stroke_color = 0x7f050398
com.example.fragmentsleam:id/design_navigation_view = 0x7f08009b
com.example.fragmentsleam:dimen/m3_btn_translation_z_hovered = 0x7f0600e8
com.example.fragmentsleam:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.example.fragmentsleam:attr/backHandlingEnabled = 0x7f030046
com.example.fragmentsleam:color/mtrl_tabs_colored_ripple_color = 0x7f05038e
com.example.fragmentsleam:dimen/m3_comp_extended_fab_large_icon_label_space = 0x7f06013d
com.example.fragmentsleam:attr/motionEffect_start = 0x7f030395
com.example.fragmentsleam:attr/titleMarginStart = 0x7f030514
com.example.fragmentsleam:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020026
com.example.fragmentsleam:color/m3_highlighted_text = 0x7f05008e
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0c01ca
com.example.fragmentsleam:dimen/m3_comp_icon_button_small_wide_trailing_space = 0x7f060185
com.example.fragmentsleam:attr/itemBackground = 0x7f03028d
com.example.fragmentsleam:color/m3_floating_toolbar_vibrant_icon_button_text_color_selector = 0x7f05008d
com.example.fragmentsleam:color/m3_floating_toolbar_vibrant_icon_button_container_color_selector = 0x7f05008c
com.example.fragmentsleam:dimen/m3_large_fab_max_image_size = 0x7f060275
com.example.fragmentsleam:color/m3_filled_icon_button_container_color_selector = 0x7f05008b
com.example.fragmentsleam:color/m3_fab_ripple_color_selector = 0x7f05008a
com.example.fragmentsleam:styleable/SwitchCompat = 0x7f11008c
com.example.fragmentsleam:color/m3_fab_efab_foreground_color_selector = 0x7f050089
com.example.fragmentsleam:id/src_in = 0x7f0801be
com.example.fragmentsleam:dimen/m3_comp_button_xlarge_icon_label_space = 0x7f06012b
com.example.fragmentsleam:attr/textOutlineColor = 0x7f0304e6
com.example.fragmentsleam:dimen/m3_comp_button_xsmall_trailing_space = 0x7f060134
com.example.fragmentsleam:dimen/abc_text_size_subhead_material = 0x7f06004d
com.example.fragmentsleam:color/m3_dynamic_highlighted_text = 0x7f050083
com.example.fragmentsleam:attr/flow_firstVerticalStyle = 0x7f03021f
com.example.fragmentsleam:color/material_grey_100 = 0x7f05030e
com.example.fragmentsleam:dimen/m3_ripple_hovered_alpha = 0x7f0602a5
com.example.fragmentsleam:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0c0146
com.example.fragmentsleam:color/material_dynamic_neutral100 = 0x7f0502cf
com.example.fragmentsleam:style/Widget.MaterialComponents.TextView = 0x7f1004be
com.example.fragmentsleam:attr/textAppearanceHeadlineLarge = 0x7f0304b5
com.example.fragmentsleam:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f100479
com.example.fragmentsleam:dimen/mtrl_switch_track_width = 0x7f0603df
com.example.fragmentsleam:color/m3_dynamic_dark_hint_foreground = 0x7f05007f
com.example.fragmentsleam:attr/appBarLayoutStyle = 0x7f030036
com.example.fragmentsleam:color/m3_default_color_secondary_text = 0x7f05007b
com.example.fragmentsleam:string/mtrl_switch_track_path = 0x7f0f009d
com.example.fragmentsleam:color/m3_dark_hint_foreground = 0x7f050078
com.example.fragmentsleam:attr/tickMarkTintMode = 0x7f030505
com.example.fragmentsleam:styleable/Badge = 0x7f110013
com.example.fragmentsleam:id/compress = 0x7f080080
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f100159
com.example.fragmentsleam:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a5
com.example.fragmentsleam:attr/thumbHeight = 0x7f0304f3
com.example.fragmentsleam:color/m3_chip_stroke_color = 0x7f050073
com.example.fragmentsleam:macro/m3_comp_app_bar_medium_flexible_subtitle_font = 0x7f0c0005
com.example.fragmentsleam:color/m3_switch_track_tint = 0x7f0501f8
com.example.fragmentsleam:style/Theme.Material3.Dark.NoActionBar = 0x7f100273
com.example.fragmentsleam:style/Widget.MaterialComponents.ChipGroup = 0x7f100477
com.example.fragmentsleam:attr/actionBarDivider = 0x7f030002
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_active_pressed_state_layer_opacity = 0x7f0601af
com.example.fragmentsleam:attr/counterOverflowTextColor = 0x7f030177
com.example.fragmentsleam:attr/viewInflaterClass = 0x7f030553
com.example.fragmentsleam:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.example.fragmentsleam:color/m3_checkbox_button_tint = 0x7f05006f
com.example.fragmentsleam:styleable/GradientColorItem = 0x7f11003e
com.example.fragmentsleam:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f100094
com.example.fragmentsleam:attr/titleMaxLines = 0x7f030517
com.example.fragmentsleam:string/path_password_eye_mask_visible = 0x7f0f00a5
com.example.fragmentsleam:style/Widget.Compat.NotificationActionContainer = 0x7f10037b
com.example.fragmentsleam:color/m3_checkbox_button_icon_tint = 0x7f05006e
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0c007b
com.example.fragmentsleam:id/text_input_start_icon = 0x7f0801e5
com.example.fragmentsleam:id/jumpToEnd = 0x7f0800fa
com.example.fragmentsleam:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f020020
com.example.fragmentsleam:color/m3_ref_palette_black = 0x7f05009f
com.example.fragmentsleam:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f06033a
com.example.fragmentsleam:attr/colorOutline = 0x7f03011c
com.example.fragmentsleam:dimen/material_bottom_sheet_max_width = 0x7f060305
com.example.fragmentsleam:attr/shapeCornerSizeMedium = 0x7f030427
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f100210
com.example.fragmentsleam:attr/textAppearanceCaption = 0x7f0304a8
com.example.fragmentsleam:attr/path_percent = 0x7f0303d2
com.example.fragmentsleam:color/m3_calendar_item_stroke_color = 0x7f05006a
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0c0167
com.example.fragmentsleam:attr/containerHeight = 0x7f030144
com.example.fragmentsleam:attr/colorError = 0x7f030104
com.example.fragmentsleam:attr/drawableBottomCompat = 0x7f0301a8
com.example.fragmentsleam:color/m3_button_ripple_color_selector = 0x7f050068
com.example.fragmentsleam:attr/colorOnContainerUnchecked = 0x7f030109
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0c012c
com.example.fragmentsleam:id/action_container = 0x7f08003b
com.example.fragmentsleam:attr/trackIconInactiveStart = 0x7f030539
com.example.fragmentsleam:color/m3_appbar_overlay_color = 0x7f050060
com.example.fragmentsleam:color/m3_ref_palette_cyan10 = 0x7f0500bb
com.example.fragmentsleam:attr/layout_constraintTop_toBottomOf = 0x7f0302e6
com.example.fragmentsleam:color/material_personalized_color_primary = 0x7f050339
com.example.fragmentsleam:dimen/m3_btn_dialog_btn_min_width = 0x7f0600d0
com.example.fragmentsleam:color/highlighted_text_material_dark = 0x7f05005e
com.example.fragmentsleam:drawable/lee = 0x7f07009e
com.example.fragmentsleam:color/foreground_material_light = 0x7f05005d
com.example.fragmentsleam:style/TextAppearance.AppCompat.Menu = 0x7f1001cd
com.example.fragmentsleam:id/pressed = 0x7f08017f
com.example.fragmentsleam:attr/sideSheetDialogTheme = 0x7f030435
com.example.fragmentsleam:color/error_color_material_light = 0x7f05005b
com.example.fragmentsleam:dimen/m3_alert_dialog_action_top_padding = 0x7f0600a0
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary98 = 0x7f05011f
com.example.fragmentsleam:id/expand_activities_button = 0x7f0800bd
com.example.fragmentsleam:color/design_snackbar_background_color = 0x7f050055
com.example.fragmentsleam:color/material_personalized_color_surface_bright = 0x7f050343
com.example.fragmentsleam:style/Base.DialogWindowTitle.AppCompat = 0x7f100011
com.example.fragmentsleam:attr/activityChooserViewStyle = 0x7f030027
com.example.fragmentsleam:color/abc_tint_default = 0x7f050014
com.example.fragmentsleam:attr/listPreferredItemHeightLarge = 0x7f030312
com.example.fragmentsleam:color/m3_ref_palette_neutral94 = 0x7f050169
com.example.fragmentsleam:dimen/m3_extended_fab_disabled_elevation = 0x7f060260
com.example.fragmentsleam:color/design_fab_shadow_end_color = 0x7f05004d
com.example.fragmentsleam:attr/alphabeticModifiers = 0x7f03002f
com.example.fragmentsleam:dimen/m3_comp_nav_bar_item_active_indicator_icon_label_space = 0x7f0601a0
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_hovered_state_layer_color = 0x7f0c01fc
com.example.fragmentsleam:attr/dynamicColorThemeOverlay = 0x7f0301b8
com.example.fragmentsleam:color/design_default_color_secondary = 0x7f050049
com.example.fragmentsleam:dimen/m3_comp_slider_value_indicator_active_bottom_space = 0x7f060214
com.example.fragmentsleam:color/design_default_color_primary = 0x7f050046
com.example.fragmentsleam:color/material_on_primary_emphasis_high_type = 0x7f05031d
com.example.fragmentsleam:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1002bb
com.example.fragmentsleam:attr/boxStrokeWidthFocused = 0x7f03008a
com.example.fragmentsleam:color/dim_foreground_material_light = 0x7f050059
com.example.fragmentsleam:layout/notification_template_part_time = 0x7f0b006c
com.example.fragmentsleam:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0602c9
com.example.fragmentsleam:color/design_default_color_on_background = 0x7f050041
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary70 = 0x7f0500ff
com.example.fragmentsleam:color/m3_sys_color_light_surface_variant = 0x7f05028a
com.example.fragmentsleam:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f0601cb
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0c00f5
com.example.fragmentsleam:color/design_dark_default_color_secondary = 0x7f05003c
com.example.fragmentsleam:attr/circularflow_angles = 0x7f0300d8
com.example.fragmentsleam:dimen/material_cursor_width = 0x7f060315
com.example.fragmentsleam:attr/framePosition = 0x7f03023e
com.example.fragmentsleam:color/design_dark_default_color_primary_variant = 0x7f05003b
com.example.fragmentsleam:attr/addElevationShadow = 0x7f030028
com.example.fragmentsleam:color/mtrl_switch_thumb_icon_tint = 0x7f05038a
com.example.fragmentsleam:attr/materialTimePickerTitleStyle = 0x7f030359
com.example.fragmentsleam:integer/m3_sys_motion_duration_extra_long1 = 0x7f09000f
com.example.fragmentsleam:attr/colorContainer = 0x7f0300fe
com.example.fragmentsleam:color/design_dark_default_color_primary_dark = 0x7f05003a
com.example.fragmentsleam:color/design_dark_default_color_error = 0x7f050033
com.example.fragmentsleam:string/material_slider_range_start = 0x7f0f0055
com.example.fragmentsleam:dimen/m3_comp_extended_fab_primary_container_pressed_state_layer_opacity = 0x7f06014c
com.example.fragmentsleam:attr/showMotionSpec = 0x7f030430
com.example.fragmentsleam:attr/navigationMode = 0x7f0303ad
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f050242
com.example.fragmentsleam:attr/itemIconPadding = 0x7f030293
com.example.fragmentsleam:color/call_notification_answer_color = 0x7f05002a
com.example.fragmentsleam:color/button_material_light = 0x7f050029
com.example.fragmentsleam:style/Base.Theme.MaterialComponents = 0x7f100065
com.example.fragmentsleam:style/Widget.MaterialComponents.BottomNavigationView = 0x7f100460
com.example.fragmentsleam:dimen/m3_floatingtoolbar_min_height = 0x7f060273
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f1001a9
com.example.fragmentsleam:attr/ifTagNotSet = 0x7f030273
com.example.fragmentsleam:attr/flow_horizontalBias = 0x7f030221
com.example.fragmentsleam:dimen/design_bottom_navigation_active_text_size = 0x7f060063
com.example.fragmentsleam:color/m3_ref_palette_secondary10 = 0x7f0501bf
com.example.fragmentsleam:attr/expandedTitleMarginStart = 0x7f0301ea
com.example.fragmentsleam:attr/buttonIcon = 0x7f030093
com.example.fragmentsleam:color/abc_tint_edittext = 0x7f050015
com.example.fragmentsleam:id/text_input_error_icon = 0x7f0801e4
com.example.fragmentsleam:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f10027a
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Display1 = 0x7f10001b
com.example.fragmentsleam:attr/contentInsetEndWithActions = 0x7f030157
com.example.fragmentsleam:attr/flow_verticalStyle = 0x7f03022d
com.example.fragmentsleam:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1002ec
com.example.fragmentsleam:id/progress_circular = 0x7f080180
com.example.fragmentsleam:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f100045
com.example.fragmentsleam:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f10040e
com.example.fragmentsleam:macro/m3_comp_button_xlarge_container_shape_square = 0x7f0c005e
com.example.fragmentsleam:layout/m3_side_sheet_dialog = 0x7f0b0038
com.example.fragmentsleam:dimen/m3_comp_nav_rail_expanded_container_width_minimum = 0x7f0601ac
com.example.fragmentsleam:color/abc_primary_text_material_light = 0x7f05000c
com.example.fragmentsleam:attr/colorOnSurfaceVariant = 0x7f030117
com.example.fragmentsleam:drawable/m3_avd_show_password = 0x7f0700a0
com.example.fragmentsleam:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f100256
com.example.fragmentsleam:id/pin = 0x7f08017b
com.example.fragmentsleam:color/m3_ref_palette_red0 = 0x7f0501b1
com.example.fragmentsleam:attr/textAppearanceSubtitle2 = 0x7f0304cc
com.example.fragmentsleam:color/abc_hint_foreground_material_dark = 0x7f050007
com.example.fragmentsleam:id/navigation_bar_item_inner_content_container = 0x7f080148
com.example.fragmentsleam:color/abc_decor_view_status_guard_light = 0x7f050006
com.example.fragmentsleam:style/Base.TextAppearance.Material3.Search = 0x7f100043
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0c012e
com.example.fragmentsleam:attr/cornerSizeBottomRight = 0x7f030171
com.example.fragmentsleam:color/abc_color_highlight_material = 0x7f050004
com.example.fragmentsleam:id/graph_wrap = 0x7f0800d9
com.example.fragmentsleam:dimen/m3_comp_split_button_xlarge_leading_button_leading_space = 0x7f06022c
com.example.fragmentsleam:attr/drawerArrowStyle = 0x7f0301b1
com.example.fragmentsleam:attr/fontProviderQuery = 0x7f030236
com.example.fragmentsleam:color/cardview_shadow_start_color = 0x7f05002f
com.example.fragmentsleam:attr/shortcutMatchRequired = 0x7f030429
com.example.fragmentsleam:attr/queryHint = 0x7f0303ed
com.example.fragmentsleam:attr/trackIconActiveColor = 0x7f030534
com.example.fragmentsleam:attr/windowMinWidthMinor = 0x7f03056f
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Headline = 0x7f10001f
com.example.fragmentsleam:dimen/m3_bottom_nav_item_padding_top = 0x7f0600c5
com.example.fragmentsleam:id/view_tree_saved_state_registry_owner = 0x7f08020c
com.example.fragmentsleam:dimen/m3_extended_fab_icon_padding = 0x7f060263
com.example.fragmentsleam:attr/windowFixedWidthMajor = 0x7f03056c
com.example.fragmentsleam:attr/windowActionModeOverlay = 0x7f030569
com.example.fragmentsleam:attr/layout_constraintBottom_toBottomOf = 0x7f0302c9
com.example.fragmentsleam:styleable/ActionBar = 0x7f110000
com.example.fragmentsleam:attr/trackIconActiveEnd = 0x7f030535
com.example.fragmentsleam:drawable/abc_spinner_mtrl_am_alpha = 0x7f070066
com.example.fragmentsleam:attr/widthChange = 0x7f030566
com.example.fragmentsleam:attr/backgroundInsetEnd = 0x7f03004a
com.example.fragmentsleam:dimen/m3_timepicker_window_elevation = 0x7f060303
com.example.fragmentsleam:attr/wavelengthIndeterminate = 0x7f030565
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_error_container = 0x7f050245
com.example.fragmentsleam:id/mtrl_picker_header_title_and_selection = 0x7f08013c
com.example.fragmentsleam:attr/wavelengthDeterminate = 0x7f030564
com.example.fragmentsleam:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f0602d6
com.example.fragmentsleam:attr/wavePhase = 0x7f03055f
com.example.fragmentsleam:attr/alertDialogButtonGroupStyle = 0x7f030029
com.example.fragmentsleam:layout/mtrl_layout_snackbar = 0x7f0b0058
com.example.fragmentsleam:attr/textBackgroundRotate = 0x7f0304d6
com.example.fragmentsleam:attr/waveDecay = 0x7f03055c
com.example.fragmentsleam:attr/passwordToggleEnabled = 0x7f0303ce
com.example.fragmentsleam:attr/textureHeight = 0x7f0304ed
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1001de
com.example.fragmentsleam:color/material_personalized_color_on_primary_container = 0x7f05032f
com.example.fragmentsleam:attr/wavelength = 0x7f030563
com.example.fragmentsleam:style/Widget.Material3.PopupMenu.Overflow = 0x7f100426
com.example.fragmentsleam:id/activity_chooser_view_content = 0x7f080046
com.example.fragmentsleam:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f1003c8
com.example.fragmentsleam:attr/materialCalendarMonth = 0x7f030338
com.example.fragmentsleam:string/mtrl_picker_day_of_week_column_header = 0x7f0f0079
com.example.fragmentsleam:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f0602e6
com.example.fragmentsleam:style/Widget.Material3.MaterialDivider = 0x7f100413
com.example.fragmentsleam:id/split_action_bar = 0x7f0801b8
com.example.fragmentsleam:attr/editTextColor = 0x7f0301ba
com.example.fragmentsleam:attr/fastScrollEnabled = 0x7f030204
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f10034e
com.example.fragmentsleam:color/highlighted_text_material_light = 0x7f05005f
com.example.fragmentsleam:dimen/mtrl_badge_text_size = 0x7f060334
com.example.fragmentsleam:attr/simpleItemSelectedRippleColor = 0x7f030439
com.example.fragmentsleam:color/m3_ref_palette_neutral92 = 0x7f050168
com.example.fragmentsleam:attr/verticalOffset = 0x7f030551
com.example.fragmentsleam:color/m3_timepicker_display_ripple_color = 0x7f0502ac
com.example.fragmentsleam:attr/buttonCompat = 0x7f030091
com.example.fragmentsleam:attr/maxActionInlineWidth = 0x7f03035b
com.example.fragmentsleam:attr/colorAccent = 0x7f0300fb
com.example.fragmentsleam:color/m3_card_foreground_color = 0x7f05006b
com.example.fragmentsleam:attr/ttcIndex = 0x7f03054b
com.example.fragmentsleam:macro/m3_comp_button_outlined_focused_outline_color = 0x7f0c002d
com.example.fragmentsleam:attr/deltaPolarRadius = 0x7f030194
com.example.fragmentsleam:attr/triggerSlack = 0x7f03054a
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f10031a
com.example.fragmentsleam:attr/transitionShapeAppearance = 0x7f030547
com.example.fragmentsleam:attr/transitionEasing = 0x7f030544
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0c01b8
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500ec
com.example.fragmentsleam:attr/motionSpringFastEffects = 0x7f0303a0
com.example.fragmentsleam:attr/useCompatPadding = 0x7f03054d
com.example.fragmentsleam:id/item_touch_helper_previous_elevation = 0x7f0800f9
com.example.fragmentsleam:attr/motionEasingStandard = 0x7f03038e
com.example.fragmentsleam:attr/trackStopIndicatorSize = 0x7f03053e
com.example.fragmentsleam:dimen/tooltip_horizontal_padding = 0x7f060400
com.example.fragmentsleam:attr/subheaderTextAppearance = 0x7f03046c
com.example.fragmentsleam:attr/motionSpringDefaultSpatial = 0x7f03039f
com.example.fragmentsleam:dimen/m3_comp_toolbar_floating_horizontal_container_height = 0x7f060257
com.example.fragmentsleam:attr/displayOptions = 0x7f030199
com.example.fragmentsleam:attr/colorPrimaryFixedDim = 0x7f030122
com.example.fragmentsleam:attr/trackIconSize = 0x7f03053a
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1004b5
com.example.fragmentsleam:macro/m3_comp_time_picker_container_color = 0x7f0c01b4
com.example.fragmentsleam:attr/materialCalendarYearNavigationButton = 0x7f03033c
com.example.fragmentsleam:macro/m3_comp_extended_fab_secondary_container_container_color = 0x7f0c0091
com.example.fragmentsleam:dimen/design_bottom_navigation_active_item_max_width = 0x7f060061
com.example.fragmentsleam:attr/materialCalendarHeaderLayout = 0x7f030334
com.example.fragmentsleam:attr/materialSplitButtonIconFilledTonalStyle = 0x7f030351
com.example.fragmentsleam:attr/motionEasingLinearInterpolator = 0x7f03038d
com.example.fragmentsleam:attr/selectableItemBackgroundBorderless = 0x7f03040f
com.example.fragmentsleam:attr/trackIconInactiveEnd = 0x7f030538
com.example.fragmentsleam:attr/region_widthMoreThan = 0x7f0303fc
com.example.fragmentsleam:attr/trackIconActiveStart = 0x7f030536
com.example.fragmentsleam:layout/material_timepicker = 0x7f0b0044
com.example.fragmentsleam:dimen/compat_button_inset_horizontal_material = 0x7f060056
com.example.fragmentsleam:attr/linearProgressIndicatorStyle = 0x7f030308
com.example.fragmentsleam:attr/trackCornerRadius = 0x7f03052e
com.example.fragmentsleam:attr/trackColorInactive = 0x7f03052d
com.example.fragmentsleam:attr/touchRegionId = 0x7f030529
com.example.fragmentsleam:attr/thumbIconTint = 0x7f0304f6
com.example.fragmentsleam:style/Base.Widget.Material3.TabLayout = 0x7f100112
com.example.fragmentsleam:attr/tooltipStyle = 0x7f030524
com.example.fragmentsleam:color/design_dark_default_color_surface = 0x7f05003e
com.example.fragmentsleam:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f10007d
com.example.fragmentsleam:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.example.fragmentsleam:attr/carousel_backwardTransition = 0x7f0300a6
com.example.fragmentsleam:attr/toolbarSurfaceStyle = 0x7f030521
com.example.fragmentsleam:attr/floatingActionButtonSmallSurfaceStyle = 0x7f030215
com.example.fragmentsleam:attr/toolbarNavigationButtonStyle = 0x7f03051f
com.example.fragmentsleam:attr/listLayout = 0x7f03030e
com.example.fragmentsleam:style/Widget.Material3.Snackbar.FullWidth = 0x7f100437
com.example.fragmentsleam:attr/toolbarId = 0x7f03051e
com.example.fragmentsleam:attr/toggleCheckedStateOnClick = 0x7f03051d
com.example.fragmentsleam:attr/duration = 0x7f0301b7
com.example.fragmentsleam:attr/badgeShapeAppearance = 0x7f030056
com.example.fragmentsleam:attr/tabSecondaryStyle = 0x7f030493
com.example.fragmentsleam:drawable/abc_list_pressed_holo_dark = 0x7f070051
com.example.fragmentsleam:attr/contentInsetStart = 0x7f03015a
com.example.fragmentsleam:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f060246
com.example.fragmentsleam:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0c0145
com.example.fragmentsleam:attr/trackDecoration = 0x7f030530
com.example.fragmentsleam:color/m3_ref_palette_grey_variant50 = 0x7f05014f
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f100315
com.example.fragmentsleam:attr/titleMarginBottom = 0x7f030512
com.example.fragmentsleam:style/Base.Widget.AppCompat.RatingBar = 0x7f1000ed
com.example.fragmentsleam:color/abc_secondary_text_material_light = 0x7f050012
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f100292
com.example.fragmentsleam:attr/textAppearanceLabelLargeEmphasized = 0x7f0304bc
com.example.fragmentsleam:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f100445
com.example.fragmentsleam:attr/indicatorInset = 0x7f030282
com.example.fragmentsleam:attr/tickVisible = 0x7f030509
com.example.fragmentsleam:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a6
com.example.fragmentsleam:color/m3_vibrant_toolbar_icon_button_icon_color_selector = 0x7f0502b4
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_tertiary = 0x7f05025d
com.example.fragmentsleam:attr/horizontalOffsetWithText = 0x7f030267
com.example.fragmentsleam:attr/tickColorActive = 0x7f030501
com.example.fragmentsleam:attr/buttonBarNegativeButtonStyle = 0x7f03008d
com.example.fragmentsleam:attr/thumbTint = 0x7f0304fc
com.example.fragmentsleam:attr/badgeWithTextShapeAppearance = 0x7f030061
com.example.fragmentsleam:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0c01af
com.example.fragmentsleam:attr/thumbTextPadding = 0x7f0304fb
com.example.fragmentsleam:attr/thumbStrokeColor = 0x7f0304f9
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f100328
com.example.fragmentsleam:color/material_timepicker_button_background = 0x7f05035d
com.example.fragmentsleam:id/material_timepicker_container = 0x7f08011d
com.example.fragmentsleam:dimen/m3_comp_icon_button_xlarge_default_leading_space = 0x7f060186
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0c010b
com.example.fragmentsleam:attr/thumbElevation = 0x7f0304f2
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001b
com.example.fragmentsleam:attr/textCentered = 0x7f0304d8
com.example.fragmentsleam:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0603ae
com.example.fragmentsleam:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f100263
com.example.fragmentsleam:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f10007b
com.example.fragmentsleam:color/m3expressive_bottom_nav_item_icon_tint = 0x7f0502b6
com.example.fragmentsleam:attr/expandedTitleMarginTop = 0x7f0301eb
com.example.fragmentsleam:attr/theme = 0x7f0304ef
com.example.fragmentsleam:macro/m3_comp_icon_button_tonal_icon_color = 0x7f0c00c7
com.example.fragmentsleam:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060138
com.example.fragmentsleam:layout/notification_action_tombstone = 0x7f0b0068
com.example.fragmentsleam:attr/itemShapeInsetStart = 0x7f0302a1
com.example.fragmentsleam:color/m3_timepicker_time_input_stroke_color = 0x7f0502b0
com.example.fragmentsleam:color/m3_ref_palette_tertiary98 = 0x7f0501d8
com.example.fragmentsleam:attr/saturation = 0x7f030403
com.example.fragmentsleam:attr/thumbStrokeWidth = 0x7f0304fa
com.example.fragmentsleam:attr/layout_constraintBaseline_toTopOf = 0x7f0302c7
com.example.fragmentsleam:drawable/$m3_avd_show_password__2 = 0x7f07000c
com.example.fragmentsleam:attr/textureBlurFactor = 0x7f0304eb
com.example.fragmentsleam:attr/snackbarStyle = 0x7f030441
com.example.fragmentsleam:attr/textStartPadding = 0x7f0304ea
com.example.fragmentsleam:dimen/mtrl_card_spacing = 0x7f060384
com.example.fragmentsleam:attr/containerShapeDefault = 0x7f030150
com.example.fragmentsleam:color/m3_ref_palette_secondary70 = 0x7f0501c6
com.example.fragmentsleam:attr/font = 0x7f03022f
com.example.fragmentsleam:attr/trackDecorationTintMode = 0x7f030532
com.example.fragmentsleam:dimen/m3_comp_fab_container_height = 0x7f060152
com.example.fragmentsleam:string/abc_menu_delete_shortcut_label = 0x7f0f000a
com.example.fragmentsleam:attr/constraintSetEnd = 0x7f03013e
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0c0073
com.example.fragmentsleam:macro/m3_comp_app_bar_large_title_font = 0x7f0c0003
com.example.fragmentsleam:id/dimensions = 0x7f08009d
com.example.fragmentsleam:dimen/m3_navigation_drawer_layout_corner_size = 0x7f060280
com.example.fragmentsleam:color/call_notification_decline_color = 0x7f05002b
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary100 = 0x7f0500f9
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_extra_small = 0x7f0602f8
com.example.fragmentsleam:attr/textInputOutlinedStyle = 0x7f0304e3
com.example.fragmentsleam:bool/abc_action_bar_embed_tabs = 0x7f040000
com.example.fragmentsleam:macro/m3_comp_time_picker_container_shape = 0x7f0c01b5
com.example.fragmentsleam:attr/spinnerStyle = 0x7f030446
com.example.fragmentsleam:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f10039c
com.example.fragmentsleam:color/m3_chip_background_color = 0x7f050071
com.example.fragmentsleam:dimen/m3_comp_progress_indicator_linear_active_indicator_wave_amplitude = 0x7f0601dd
com.example.fragmentsleam:styleable/ImageFilterView = 0x7f110040
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f10048d
com.example.fragmentsleam:attr/motionEffect_viewTransition = 0x7f030399
com.example.fragmentsleam:attr/textColorSearchUrl = 0x7f0304da
com.example.fragmentsleam:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0c01ac
com.example.fragmentsleam:attr/colorOnBackground = 0x7f030106
com.example.fragmentsleam:attr/layout_constraintHorizontal_bias = 0x7f0302d9
com.example.fragmentsleam:dimen/abc_text_size_body_2_material = 0x7f060040
com.example.fragmentsleam:color/cardview_light_background = 0x7f05002d
com.example.fragmentsleam:drawable/abc_cab_background_top_mtrl_alpha = 0x7f07003a
com.example.fragmentsleam:attr/colorOnContainerChecked = 0x7f030108
com.example.fragmentsleam:attr/paddingLeftSystemWindowInsets = 0x7f0303c3
com.example.fragmentsleam:attr/textAppearanceTitleLargeEmphasized = 0x7f0304ce
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_pressed_label_text_color = 0x7f0c0043
com.example.fragmentsleam:dimen/m3_btn_max_width = 0x7f0600dd
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0c01c5
com.example.fragmentsleam:color/m3_timepicker_button_text_color = 0x7f0502a9
com.example.fragmentsleam:attr/textAppearancePopupMenuHeader = 0x7f0304c7
com.example.fragmentsleam:attr/colorSecondaryFixed = 0x7f030128
com.example.fragmentsleam:dimen/m3_comp_icon_button_xsmall_narrow_leading_space = 0x7f060191
com.example.fragmentsleam:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1002d6
com.example.fragmentsleam:attr/listChoiceIndicatorMultipleAnimated = 0x7f03030a
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_hovered_outline_color = 0x7f0c004c
com.example.fragmentsleam:attr/textAppearanceOverline = 0x7f0304c6
com.example.fragmentsleam:attr/state_collapsed = 0x7f03045a
com.example.fragmentsleam:attr/suggestionRowLayout = 0x7f030478
com.example.fragmentsleam:attr/stackFromEnd = 0x7f03044e
com.example.fragmentsleam:color/design_dark_default_color_secondary_variant = 0x7f05003d
com.example.fragmentsleam:dimen/m3_comp_icon_button_small_default_leading_space = 0x7f06017e
com.example.fragmentsleam:string/hide_bottom_view_on_scroll_behavior = 0x7f0f0034
com.example.fragmentsleam:dimen/mtrl_calendar_day_vertical_padding = 0x7f06035f
com.example.fragmentsleam:dimen/m3_btn_icon_only_default_size = 0x7f0600d9
com.example.fragmentsleam:attr/actionBarSize = 0x7f030005
com.example.fragmentsleam:attr/textAppearanceListItem = 0x7f0304c3
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f050257
com.example.fragmentsleam:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f0601fe
com.example.fragmentsleam:color/background_material_dark = 0x7f05001f
com.example.fragmentsleam:attr/wavePeriod = 0x7f03055e
com.example.fragmentsleam:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f100453
com.example.fragmentsleam:attr/textAppearanceLabelLarge = 0x7f0304bb
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary80 = 0x7f05010e
com.example.fragmentsleam:attr/spinBars = 0x7f030444
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant100 = 0x7f050170
com.example.fragmentsleam:attr/textAppearanceHeadlineLargeEmphasized = 0x7f0304b6
com.example.fragmentsleam:styleable/ViewPager2 = 0x7f11009c
com.example.fragmentsleam:color/mtrl_btn_text_btn_ripple_color = 0x7f050366
com.example.fragmentsleam:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.example.fragmentsleam:attr/toolbarStyle = 0x7f030520
com.example.fragmentsleam:attr/layout_goneMarginEnd = 0x7f0302f5
com.example.fragmentsleam:attr/behavior_autoShrink = 0x7f030069
com.example.fragmentsleam:color/m3_ref_palette_tertiary100 = 0x7f0501ce
com.example.fragmentsleam:attr/textAppearanceHeadline3 = 0x7f0304b1
com.example.fragmentsleam:macro/m3_comp_assist_chip_container_shape = 0x7f0c000e
com.example.fragmentsleam:attr/textAppearanceHeadline2 = 0x7f0304b0
com.example.fragmentsleam:string/abc_menu_space_shortcut_label = 0x7f0f000f
com.example.fragmentsleam:attr/textAppearanceDisplaySmallEmphasized = 0x7f0304ae
com.example.fragmentsleam:id/beginOnFirstDraw = 0x7f08005f
com.example.fragmentsleam:attr/containerPaddingStart = 0x7f03014d
com.example.fragmentsleam:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1002ea
com.example.fragmentsleam:attr/cornerFamily = 0x7f030169
com.example.fragmentsleam:attr/expandedTitleGravity = 0x7f0301e6
com.example.fragmentsleam:attr/textAppearanceDisplaySmall = 0x7f0304ad
com.example.fragmentsleam:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1002cb
com.example.fragmentsleam:dimen/m3_loading_indicator_container_size = 0x7f060278
com.example.fragmentsleam:color/material_dynamic_color_dark_error_container = 0x7f0502c6
com.example.fragmentsleam:dimen/m3_comp_button_small_icon_label_space = 0x7f060122
com.example.fragmentsleam:attr/tabIndicatorAnimationMode = 0x7f030484
com.example.fragmentsleam:attr/textAppearanceDisplayLarge = 0x7f0304a9
com.example.fragmentsleam:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1001cc
com.example.fragmentsleam:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020012
com.example.fragmentsleam:string/material_timepicker_hour = 0x7f0f0059
com.example.fragmentsleam:dimen/abc_floating_window_z = 0x7f06002f
com.example.fragmentsleam:attr/chipStandaloneStyle = 0x7f0300d0
com.example.fragmentsleam:dimen/m3_simple_item_color_selected_alpha = 0x7f0602b8
com.example.fragmentsleam:color/m3_sys_color_light_tertiary = 0x7f05028b
com.example.fragmentsleam:attr/guidelineUseRtl = 0x7f03024d
com.example.fragmentsleam:attr/textAppearanceBodyMediumEmphasized = 0x7f0304a4
com.example.fragmentsleam:color/material_dynamic_neutral_variant70 = 0x7f0502e2
com.example.fragmentsleam:dimen/m3_comp_button_tonal_container_elevation = 0x7f06012a
com.example.fragmentsleam:styleable/AppBarLayoutStates = 0x7f11000b
com.example.fragmentsleam:attr/textAppearanceBodyLargeEmphasized = 0x7f0304a2
com.example.fragmentsleam:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f06036a
com.example.fragmentsleam:attr/textAppearanceLargePopupMenu = 0x7f0304c1
com.example.fragmentsleam:color/material_harmonized_color_error = 0x7f050315
com.example.fragmentsleam:dimen/m3_navigation_item_icon_padding = 0x7f060284
com.example.fragmentsleam:attr/containerIconPadding = 0x7f030145
com.example.fragmentsleam:color/material_dynamic_tertiary60 = 0x7f050308
com.example.fragmentsleam:style/Base.Widget.Material3.DockedToolbar = 0x7f100104
com.example.fragmentsleam:attr/telltales_velocityMode = 0x7f03049d
com.example.fragmentsleam:id/src_atop = 0x7f0801bd
com.example.fragmentsleam:id/homeAsUp = 0x7f0800e2
com.example.fragmentsleam:attr/titleTextAppearance = 0x7f030519
com.example.fragmentsleam:attr/onHide = 0x7f0303b7
com.example.fragmentsleam:dimen/m3_btn_icon_btn_padding_right = 0x7f0600d7
com.example.fragmentsleam:color/m3_ref_palette_blue_variant40 = 0x7f0500b2
com.example.fragmentsleam:attr/chipMinTouchTargetSize = 0x7f0300cc
com.example.fragmentsleam:dimen/m3_extended_fab_disabled_translation_z = 0x7f060261
com.example.fragmentsleam:attr/flow_verticalBias = 0x7f03022b
com.example.fragmentsleam:anim/design_snackbar_in = 0x7f01001a
com.example.fragmentsleam:integer/config_tooltipAnimTime = 0x7f090005
com.example.fragmentsleam:attr/logoAdjustViewBounds = 0x7f03031a
com.example.fragmentsleam:drawable/mtrl_ic_arrow_drop_down = 0x7f0700c3
com.example.fragmentsleam:attr/touchAnchorId = 0x7f030527
com.example.fragmentsleam:attr/hintAnimationEnabled = 0x7f03025c
com.example.fragmentsleam:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f100361
com.example.fragmentsleam:attr/layout_wrapBehaviorInParent = 0x7f030301
com.example.fragmentsleam:attr/tabRippleColor = 0x7f030492
com.example.fragmentsleam:color/m3_ref_palette_pink20 = 0x7f05018c
com.example.fragmentsleam:attr/strokeWidth = 0x7f030467
com.example.fragmentsleam:attr/tabPadding = 0x7f03048d
com.example.fragmentsleam:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f06033c
com.example.fragmentsleam:attr/deltaPolarAngle = 0x7f030193
com.example.fragmentsleam:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.example.fragmentsleam:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f100458
com.example.fragmentsleam:id/view_tree_view_model_store_owner = 0x7f08020d
com.example.fragmentsleam:attr/yearTodayStyle = 0x7f030573
com.example.fragmentsleam:style/ThemeOverlay.Material3.Button = 0x7f1002cf
com.example.fragmentsleam:attr/floatingActionButtonSmallPrimaryStyle = 0x7f030212
com.example.fragmentsleam:color/m3_bottom_sheet_drag_handle_color = 0x7f050063
com.example.fragmentsleam:style/Theme.MaterialComponents.Dialog = 0x7f10029f
com.example.fragmentsleam:attr/listPreferredItemPaddingRight = 0x7f030316
com.example.fragmentsleam:id/filled = 0x7f0800c3
com.example.fragmentsleam:dimen/design_bottom_navigation_height = 0x7f060065
com.example.fragmentsleam:attr/chainUseRtl = 0x7f0300b2
com.example.fragmentsleam:attr/startIconTintMode = 0x7f030456
com.example.fragmentsleam:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f100148
com.example.fragmentsleam:style/Base.Widget.Material3.Snackbar = 0x7f100111
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_pressed_state_layer_color = 0x7f0c01df
com.example.fragmentsleam:attr/tabIndicatorGravity = 0x7f030487
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_horizontal_icon_label_space = 0x7f0601b6
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant40 = 0x7f050173
com.example.fragmentsleam:color/m3_tabs_text_color = 0x7f05029d
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f10011d
com.example.fragmentsleam:dimen/m3_navigation_rail_item_min_height = 0x7f060298
com.example.fragmentsleam:attr/flow_firstHorizontalStyle = 0x7f03021d
com.example.fragmentsleam:color/design_icon_tint = 0x7f050054
com.example.fragmentsleam:attr/extendedFloatingActionButtonSmallStyle = 0x7f0301f6
com.example.fragmentsleam:attr/tabIndicator = 0x7f030482
com.example.fragmentsleam:attr/tabIconTintMode = 0x7f030481
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1001e7
com.example.fragmentsleam:attr/tabGravity = 0x7f03047f
com.example.fragmentsleam:attr/tabMinWidth = 0x7f03048b
com.example.fragmentsleam:attr/tabContentStart = 0x7f03047e
com.example.fragmentsleam:attr/autoSizeTextType = 0x7f030044
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant70 = 0x7f050176
com.example.fragmentsleam:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.example.fragmentsleam:styleable/MaterialDivider = 0x7f11005b
com.example.fragmentsleam:attr/windowMinWidthMajor = 0x7f03056e
com.example.fragmentsleam:attr/switchPadding = 0x7f03047a
com.example.fragmentsleam:attr/layout_constrainedHeight = 0x7f0302c2
com.example.fragmentsleam:attr/chipGroupStyle = 0x7f0300c5
com.example.fragmentsleam:attr/carousel_nextState = 0x7f0300ab
com.example.fragmentsleam:attr/subtitleTextAppearance = 0x7f030472
com.example.fragmentsleam:attr/materialSplitButtonIconFilledStyle = 0x7f030350
com.example.fragmentsleam:attr/panelBackground = 0x7f0303c9
com.example.fragmentsleam:macro/m3_comp_loading_indicator_active_indicator_color = 0x7f0c00d1
com.example.fragmentsleam:id/textinput_counter = 0x7f0801e6
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_outline = 0x7f05024e
com.example.fragmentsleam:attr/submitBackground = 0x7f03046e
com.example.fragmentsleam:macro/m3_comp_snackbar_container_shape = 0x7f0c017f
com.example.fragmentsleam:attr/chipIconEnabled = 0x7f0300c7
com.example.fragmentsleam:attr/mock_label = 0x7f030370
com.example.fragmentsleam:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f100232
com.example.fragmentsleam:integer/m3_sys_shape_corner_full_corner_family = 0x7f090024
com.example.fragmentsleam:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f050266
com.example.fragmentsleam:attr/subheaderInsetEnd = 0x7f03046a
com.example.fragmentsleam:attr/tabIndicatorColor = 0x7f030485
com.example.fragmentsleam:dimen/m3_navigation_item_expanded_active_indicator_height_default = 0x7f060282
com.example.fragmentsleam:attr/subMenuArrow = 0x7f030468
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500cd
com.example.fragmentsleam:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0c019a
com.example.fragmentsleam:attr/statusBarScrim = 0x7f030464
com.example.fragmentsleam:attr/colorSurfaceVariant = 0x7f030134
com.example.fragmentsleam:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f06024c
com.example.fragmentsleam:styleable/PopupWindowBackgroundState = 0x7f110076
com.example.fragmentsleam:attr/state_with_icon = 0x7f030461
com.example.fragmentsleam:attr/chipEndPadding = 0x7f0300c4
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_hovered_state_layer_color = 0x7f0c01da
com.example.fragmentsleam:attr/motionDebug = 0x7f030375
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_vertical_label_text_font = 0x7f0c00f0
com.example.fragmentsleam:animator/m3_extended_fab_hide_motion_spec = 0x7f020014
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Small = 0x7f1003dc
com.example.fragmentsleam:styleable/CustomAttribute = 0x7f11002e
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_active_indicator_color = 0x7f0c00e7
com.example.fragmentsleam:attr/contentPaddingLeft = 0x7f030160
com.example.fragmentsleam:attr/startIconTint = 0x7f030455
com.example.fragmentsleam:macro/m3_comp_snackbar_supporting_text_type = 0x7f0c0181
com.example.fragmentsleam:attr/bottomSheetStyle = 0x7f03007f
com.example.fragmentsleam:attr/springStopThreshold = 0x7f03044c
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f10049c
com.example.fragmentsleam:attr/alertDialogCenterButtons = 0x7f03002a
com.example.fragmentsleam:attr/subtitleTextStyle = 0x7f030474
com.example.fragmentsleam:attr/springMass = 0x7f03044a
com.example.fragmentsleam:attr/titleMarginEnd = 0x7f030513
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f0602d1
com.example.fragmentsleam:attr/textEndPadding = 0x7f0304db
com.example.fragmentsleam:color/material_timepicker_button_stroke = 0x7f05035e
com.example.fragmentsleam:attr/drawerLayoutCornerSize = 0x7f0301b2
com.example.fragmentsleam:color/material_dynamic_primary70 = 0x7f0502ef
com.example.fragmentsleam:dimen/m3_badge_size = 0x7f0600b9
com.example.fragmentsleam:attr/contentScrim = 0x7f030164
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0c01be
com.example.fragmentsleam:attr/actionModeWebSearchDrawable = 0x7f030020
com.example.fragmentsleam:attr/sliderStyle = 0x7f03043f
com.example.fragmentsleam:attr/singleSelection = 0x7f03043d
com.example.fragmentsleam:string/call_notification_answer_video_action = 0x7f0f0024
com.example.fragmentsleam:attr/splitTrack = 0x7f030447
com.example.fragmentsleam:attr/singleLine = 0x7f03043c
com.example.fragmentsleam:attr/drawerLayoutStyle = 0x7f0301b3
com.example.fragmentsleam:attr/singleChoiceItemLayout = 0x7f03043b
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker.Display = 0x7f100419
com.example.fragmentsleam:plurals/mtrl_badge_content_description = 0x7f0e0000
com.example.fragmentsleam:anim/mtrl_card_lowers_interpolator = 0x7f01002b
com.example.fragmentsleam:attr/materialCalendarDay = 0x7f03032e
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant10 = 0x7f05016f
com.example.fragmentsleam:color/material_dynamic_tertiary95 = 0x7f05030c
com.example.fragmentsleam:attr/simpleItems = 0x7f03043a
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_pressed_outline_color = 0x7f0c0051
com.example.fragmentsleam:id/parentPanel = 0x7f080173
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary50 = 0x7f05010b
com.example.fragmentsleam:attr/constraint_referenced_tags = 0x7f030141
com.example.fragmentsleam:color/button_material_dark = 0x7f050028
com.example.fragmentsleam:attr/simpleItemLayout = 0x7f030437
com.example.fragmentsleam:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f1003c3
com.example.fragmentsleam:style/ThemeOverlay.Material3.DockedToolbar = 0x7f1002e0
com.example.fragmentsleam:attr/collapsedItemMinHeight = 0x7f0300ed
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f100353
com.example.fragmentsleam:color/material_slider_inactive_tick_marks_color = 0x7f05035a
com.example.fragmentsleam:color/material_dynamic_secondary90 = 0x7f0502fe
com.example.fragmentsleam:attr/showDelay = 0x7f03042d
com.example.fragmentsleam:attr/showAnimationBehavior = 0x7f03042b
com.example.fragmentsleam:attr/shouldRemoveExpandedCorners = 0x7f03042a
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.IconButton.Medium.Selected.Container.Shape.Square = 0x7f100171
com.example.fragmentsleam:attr/shapeCornerSizeSmall = 0x7f030428
com.example.fragmentsleam:attr/shapeCornerSizeLargeIncreased = 0x7f030426
com.example.fragmentsleam:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f0601f4
com.example.fragmentsleam:dimen/m3_comp_fab_small_container_height = 0x7f06015f
com.example.fragmentsleam:dimen/m3_comp_button_medium_icon_size = 0x7f06011b
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f100030
com.example.fragmentsleam:attr/shapeCornerSizeExtraLargeIncreased = 0x7f030423
com.example.fragmentsleam:macro/m3_comp_fab_medium_container_shape = 0x7f0c009b
com.example.fragmentsleam:attr/containerInsetRight = 0x7f030149
com.example.fragmentsleam:attr/coordinatorLayoutStyle = 0x7f030167
com.example.fragmentsleam:color/m3_ref_palette_blue_variant80 = 0x7f0500b6
com.example.fragmentsleam:attr/autoAdjustToWithinGrandparentBounds = 0x7f03003c
com.example.fragmentsleam:attr/shapeCornerSizeExtraLarge = 0x7f030422
com.example.fragmentsleam:dimen/mtrl_btn_padding_top = 0x7f06034d
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_inactive_focused_state_layer_color = 0x7f0c00eb
com.example.fragmentsleam:attr/textInputStyle = 0x7f0304e4
com.example.fragmentsleam:color/m3_ref_palette_blue_variant10 = 0x7f0500ae
com.example.fragmentsleam:drawable/abc_ratingbar_material = 0x7f07005c
com.example.fragmentsleam:dimen/m3_ripple_focused_alpha = 0x7f0602a4
com.example.fragmentsleam:attr/shapeCornerSizeExtraExtraLarge = 0x7f030421
com.example.fragmentsleam:style/TextAppearance.Material3.LabelLarge = 0x7f10022c
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f05022a
com.example.fragmentsleam:color/m3_ref_palette_pink70 = 0x7f050191
com.example.fragmentsleam:attr/useDrawerArrowDrawable = 0x7f03054e
com.example.fragmentsleam:attr/spanCount = 0x7f030443
com.example.fragmentsleam:attr/loadingIndicatorStyle = 0x7f030318
com.example.fragmentsleam:attr/shapeAppearanceCornerMedium = 0x7f03041a
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.DisplayLarge.Emphasized = 0x7f100201
com.example.fragmentsleam:id/indeterminate = 0x7f0800f0
com.example.fragmentsleam:attr/materialSwitchStyle = 0x7f030355
com.example.fragmentsleam:attr/shapeAppearanceCornerLargeIncreased = 0x7f030419
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f10006f
com.example.fragmentsleam:macro/m3_comp_icon_button_standard_icon_color = 0x7f0c00c3
com.example.fragmentsleam:attr/itemShapeAppearanceOverlay = 0x7f03029d
com.example.fragmentsleam:attr/shapeAppearanceCornerExtraSmall = 0x7f030417
com.example.fragmentsleam:dimen/m3_comp_icon_button_xsmall_narrow_trailing_space = 0x7f060192
com.example.fragmentsleam:dimen/m3_comp_toolbar_floating_vertical_container_width = 0x7f060258
com.example.fragmentsleam:macro/m3_comp_button_filled_selected_icon_color = 0x7f0c001d
com.example.fragmentsleam:id/showHome = 0x7f0801aa
com.example.fragmentsleam:attr/shapeAppearance = 0x7f030413
com.example.fragmentsleam:attr/height = 0x7f030252
com.example.fragmentsleam:macro/m3_comp_app_bar_small_subtitle_font = 0x7f0c0009
com.example.fragmentsleam:id/date_picker_actions = 0x7f080090
com.example.fragmentsleam:attr/selectorSize = 0x7f030411
com.example.fragmentsleam:dimen/m3_navigation_divider_bottom_margin = 0x7f06027e
com.example.fragmentsleam:id/textinput_error = 0x7f0801e7
com.example.fragmentsleam:dimen/m3_comp_icon_button_xsmall_outlined_outline_width = 0x7f060193
com.example.fragmentsleam:styleable/Toolbar = 0x7f110095
com.example.fragmentsleam:attr/badgeTextAppearance = 0x7f03005a
com.example.fragmentsleam:dimen/mtrl_btn_hovered_z = 0x7f060344
com.example.fragmentsleam:attr/paddingTopNoTitle = 0x7f0303c7
com.example.fragmentsleam:attr/selectionRequired = 0x7f030410
com.example.fragmentsleam:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1002fe
com.example.fragmentsleam:attr/chipIcon = 0x7f0300c6
com.example.fragmentsleam:attr/chipSpacingVertical = 0x7f0300cf
com.example.fragmentsleam:id/decor_content_parent = 0x7f080093
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_secondary = 0x7f050248
com.example.fragmentsleam:attr/floatingActionButtonStyle = 0x7f030217
com.example.fragmentsleam:attr/layout_constraintHeight_default = 0x7f0302d5
com.example.fragmentsleam:string/mtrl_checkbox_state_description_unchecked = 0x7f0f006b
com.example.fragmentsleam:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020027
com.example.fragmentsleam:macro/m3_comp_button_large_pressed_container_shape = 0x7f0c0024
com.example.fragmentsleam:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f100461
com.example.fragmentsleam:attr/searchViewStyle = 0x7f03040c
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0c011a
com.example.fragmentsleam:attr/materialSplitButtonLeadingFilledStyle = 0x7f030352
com.example.fragmentsleam:attr/prefixTextAppearance = 0x7f0303e3
com.example.fragmentsleam:attr/searchPrefixText = 0x7f03040b
com.example.fragmentsleam:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f100101
com.example.fragmentsleam:attr/searchIcon = 0x7f03040a
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_hovered_label_text_color = 0x7f0c01f2
com.example.fragmentsleam:attr/searchHintIcon = 0x7f030409
com.example.fragmentsleam:dimen/m3_comp_extended_fab_small_trailing_space = 0x7f060151
com.example.fragmentsleam:style/Widget.Material3.DockedToolbar.IconButton = 0x7f1003ce
com.example.fragmentsleam:style/TextAppearance.Design.Snackbar.Message = 0x7f1001f7
com.example.fragmentsleam:color/m3_ref_palette_purple80 = 0x7f0501ad
com.example.fragmentsleam:attr/scrimVisibleHeightTrigger = 0x7f030407
com.example.fragmentsleam:attr/grid_rowWeights = 0x7f030246
com.example.fragmentsleam:attr/flow_horizontalGap = 0x7f030222
com.example.fragmentsleam:dimen/mtrl_navigation_rail_icon_size = 0x7f0603b4
com.example.fragmentsleam:attr/scaleFromTextSize = 0x7f030404
com.example.fragmentsleam:attr/actionModeFindDrawable = 0x7f030018
com.example.fragmentsleam:attr/round = 0x7f030401
com.example.fragmentsleam:attr/listChoiceIndicatorSingleAnimated = 0x7f03030b
com.example.fragmentsleam:color/m3_button_outline_color_selector = 0x7f050066
com.example.fragmentsleam:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f060168
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f10017f
com.example.fragmentsleam:dimen/abc_list_item_height_small_material = 0x7f060032
com.example.fragmentsleam:macro/m3_comp_extended_fab_small_container_shape = 0x7f0c0093
com.example.fragmentsleam:color/material_dynamic_primary99 = 0x7f0502f3
com.example.fragmentsleam:id/constraint = 0x7f080082
com.example.fragmentsleam:attr/constraintSet = 0x7f03013d
com.example.fragmentsleam:attr/reverseLayout = 0x7f0303fe
com.example.fragmentsleam:style/Base.Widget.Material3.FloatingActionButton.Medium = 0x7f10010c
com.example.fragmentsleam:style/Widget.AppCompat.ActionBar = 0x7f100332
com.example.fragmentsleam:attr/touchAnchorSide = 0x7f030528
com.example.fragmentsleam:attr/fabCustomSize = 0x7f030202
com.example.fragmentsleam:dimen/m3_comp_app_bar_medium_flexible_container_height = 0x7f060103
com.example.fragmentsleam:attr/enforceTextAppearance = 0x7f0301cc
com.example.fragmentsleam:attr/queryBackground = 0x7f0303ec
com.example.fragmentsleam:dimen/abc_dialog_padding_top_material = 0x7f060025
com.example.fragmentsleam:attr/region_heightMoreThan = 0x7f0303fa
com.example.fragmentsleam:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1000e4
com.example.fragmentsleam:string/mtrl_picker_toggled_to_day_selection = 0x7f0f0094
com.example.fragmentsleam:color/m3_ref_palette_tertiary30 = 0x7f0501d0
com.example.fragmentsleam:attr/materialCalendarMonthNavigationButton = 0x7f030339
com.example.fragmentsleam:attr/region_heightLessThan = 0x7f0303f9
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Headline3 = 0x7f100244
com.example.fragmentsleam:color/m3_ref_palette_primary100 = 0x7f050198
com.example.fragmentsleam:id/bottom = 0x7f080064
com.example.fragmentsleam:attr/circularflow_defaultRadius = 0x7f0300da
com.example.fragmentsleam:styleable/ChipGroup = 0x7f11001e
com.example.fragmentsleam:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f10011a
com.example.fragmentsleam:attr/flow_lastVerticalStyle = 0x7f030227
com.example.fragmentsleam:attr/reactiveGuide_applyToConstraintSet = 0x7f0303f6
com.example.fragmentsleam:attr/reactiveGuide_animateChange = 0x7f0303f4
com.example.fragmentsleam:attr/helperTextTextColor = 0x7f030256
com.example.fragmentsleam:attr/itemIconSize = 0x7f030294
com.example.fragmentsleam:attr/layout_goneMarginLeft = 0x7f0302f6
com.example.fragmentsleam:attr/shapeAppearanceCornerSmall = 0x7f03041b
com.example.fragmentsleam:attr/radioButtonStyle = 0x7f0303ef
com.example.fragmentsleam:attr/queryPatterns = 0x7f0303ee
com.example.fragmentsleam:attr/motionEffect_strict = 0x7f030396
com.example.fragmentsleam:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f050292
com.example.fragmentsleam:attr/quantizeMotionSteps = 0x7f0303eb
com.example.fragmentsleam:id/META = 0x7f080005
com.example.fragmentsleam:attr/quantizeMotionInterpolator = 0x7f0303e9
com.example.fragmentsleam:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1000dd
com.example.fragmentsleam:attr/progressBarStyle = 0x7f0303e8
com.example.fragmentsleam:drawable/test_level_drawable = 0x7f0700ef
com.example.fragmentsleam:attr/collapsedSubtitleTextColor = 0x7f0300f0
com.example.fragmentsleam:attr/checkMarkCompat = 0x7f0300b3
com.example.fragmentsleam:attr/passwordToggleContentDescription = 0x7f0303cc
com.example.fragmentsleam:color/background_floating_material_dark = 0x7f05001d
com.example.fragmentsleam:attr/stateLabels = 0x7f030458
com.example.fragmentsleam:attr/polarRelativeTo = 0x7f0303dd
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_primary_container = 0x7f050251
com.example.fragmentsleam:style/Widget.Material3.CompoundButton.Switch = 0x7f1003ca
com.example.fragmentsleam:dimen/m3_comp_icon_button_xlarge_narrow_leading_space = 0x7f060189
com.example.fragmentsleam:attr/placeholder_emptyVisibility = 0x7f0303dc
com.example.fragmentsleam:attr/placeholderText = 0x7f0303d9
com.example.fragmentsleam:macro/m3_comp_search_bar_supporting_text_color = 0x7f0c0155
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f100403
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0c00f7
com.example.fragmentsleam:dimen/m3_comp_toolbar_floating_container_elevation = 0x7f060254
com.example.fragmentsleam:macro/m3_comp_extended_fab_large_label_text = 0x7f0c008c
com.example.fragmentsleam:attr/dockedToolbarStyle = 0x7f0301a2
com.example.fragmentsleam:attr/percentY = 0x7f0303d6
com.example.fragmentsleam:attr/layout_keyline = 0x7f0302fb
com.example.fragmentsleam:dimen/m3_comp_button_xlarge_outlined_outline_width = 0x7f06012e
com.example.fragmentsleam:attr/percentX = 0x7f0303d5
com.example.fragmentsleam:attr/useMaterialThemeColors = 0x7f03054f
com.example.fragmentsleam:dimen/m3_comp_nav_bar_item_horizontal_active_indicator_height = 0x7f0601a1
com.example.fragmentsleam:id/autoHide = 0x7f080059
com.example.fragmentsleam:attr/percentWidth = 0x7f0303d4
com.example.fragmentsleam:id/neverCompleteToEnd = 0x7f08014f
com.example.fragmentsleam:id/accessibility_custom_action_5 = 0x7f08002c
com.example.fragmentsleam:attr/textBackgroundPanX = 0x7f0304d4
com.example.fragmentsleam:attr/tooltipText = 0x7f030525
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Toolbar.Floating.Container.Shape = 0x7f100186
com.example.fragmentsleam:attr/imageRotate = 0x7f030278
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_horizontal_full_width_leading_space = 0x7f0601b4
com.example.fragmentsleam:attr/showPaths = 0x7f030431
com.example.fragmentsleam:dimen/abc_action_bar_default_height_material = 0x7f060002
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat = 0x7f100016
com.example.fragmentsleam:attr/grid_horizontalGaps = 0x7f030244
com.example.fragmentsleam:attr/textAppearanceTitleLarge = 0x7f0304cd
com.example.fragmentsleam:dimen/design_textinput_caption_translate_y = 0x7f06008f
com.example.fragmentsleam:attr/panelMenuListWidth = 0x7f0303cb
com.example.fragmentsleam:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1000df
com.example.fragmentsleam:color/bright_foreground_disabled_material_light = 0x7f050023
com.example.fragmentsleam:dimen/compat_notification_large_icon_max_width = 0x7f06005c
com.example.fragmentsleam:attr/contentDescription = 0x7f030155
com.example.fragmentsleam:color/mtrl_chip_surface_color = 0x7f050370
com.example.fragmentsleam:color/material_dynamic_tertiary10 = 0x7f050302
com.example.fragmentsleam:attr/expandedHintEnabled = 0x7f0301df
com.example.fragmentsleam:attr/paddingBottomSystemWindowInsets = 0x7f0303c1
com.example.fragmentsleam:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f06024b
com.example.fragmentsleam:macro/m3_comp_icon_button_filled_icon_color = 0x7f0c00b9
com.example.fragmentsleam:attr/hintEnabled = 0x7f03025d
com.example.fragmentsleam:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1000e6
com.example.fragmentsleam:attr/paddingBottomNoButtons = 0x7f0303c0
com.example.fragmentsleam:animator/m3_extended_fab_state_list_animator = 0x7f020016
com.example.fragmentsleam:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.example.fragmentsleam:attr/onTouchUp = 0x7f0303bc
com.example.fragmentsleam:attr/popupWindowStyle = 0x7f0303e1
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f10046f
com.example.fragmentsleam:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.example.fragmentsleam:style/TextAppearance.Material3.SearchView = 0x7f100234
com.example.fragmentsleam:macro/m3_comp_button_outlined_focused_icon_color = 0x7f0c002b
com.example.fragmentsleam:attr/fastScrollVerticalThumbDrawable = 0x7f030207
com.example.fragmentsleam:attr/number = 0x7f0303b3
com.example.fragmentsleam:attr/nestedScrollable = 0x7f0303b2
com.example.fragmentsleam:attr/nestedScrollViewStyle = 0x7f0303b1
com.example.fragmentsleam:string/exposed_dropdown_menu_content_description = 0x7f0f0030
com.example.fragmentsleam:color/design_bottom_navigation_shadow_color = 0x7f050030
com.example.fragmentsleam:attr/iconLabelHorizontalSpacing = 0x7f03026c
com.example.fragmentsleam:attr/nestedScrollFlags = 0x7f0303b0
com.example.fragmentsleam:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f10036b
com.example.fragmentsleam:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f0601d4
com.example.fragmentsleam:attr/expanded = 0x7f0301d9
com.example.fragmentsleam:color/m3_ref_palette_grey80 = 0x7f050145
com.example.fragmentsleam:attr/navigationIcon = 0x7f0303ab
com.example.fragmentsleam:dimen/m3_comp_button_xlarge_leading_space = 0x7f06012d
com.example.fragmentsleam:attr/bottomSheetDialogTheme = 0x7f03007d
com.example.fragmentsleam:layout/select_dialog_multichoice_material = 0x7f0b006f
com.example.fragmentsleam:attr/navigationContentDescription = 0x7f0303aa
com.example.fragmentsleam:attr/chipIconVisible = 0x7f0300ca
com.example.fragmentsleam:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f10008c
com.example.fragmentsleam:color/m3_ref_palette_error20 = 0x7f050124
com.example.fragmentsleam:attr/indeterminateAnimatorDurationScale = 0x7f03027c
com.example.fragmentsleam:attr/motionTarget = 0x7f0303a5
com.example.fragmentsleam:dimen/mtrl_calendar_month_horizontal_padding = 0x7f06036e
com.example.fragmentsleam:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0602cb
com.example.fragmentsleam:attr/overlay = 0x7f0303bf
com.example.fragmentsleam:attr/buttonTintMode = 0x7f03009c
com.example.fragmentsleam:attr/motionStagger = 0x7f0303a4
com.example.fragmentsleam:string/call_notification_incoming_text = 0x7f0f0027
com.example.fragmentsleam:drawable/$avd_show_password__0 = 0x7f070003
com.example.fragmentsleam:color/material_dynamic_neutral_variant95 = 0x7f0502e5
com.example.fragmentsleam:attr/motionSpringSlowEffects = 0x7f0303a2
com.example.fragmentsleam:color/m3_button_background_color_selector = 0x7f050064
com.example.fragmentsleam:attr/fastScrollVerticalTrackDrawable = 0x7f030208
com.example.fragmentsleam:attr/motionPathRotate = 0x7f03039c
com.example.fragmentsleam:id/accessibility_custom_action_6 = 0x7f08002d
com.example.fragmentsleam:attr/springBoundary = 0x7f030448
com.example.fragmentsleam:dimen/m3_comp_nav_rail_collapsed_narrow_container_width = 0x7f0601a9
com.example.fragmentsleam:attr/backgroundTintMode = 0x7f030051
com.example.fragmentsleam:id/textStart = 0x7f0801e0
com.example.fragmentsleam:attr/tickMarkTint = 0x7f030504
com.example.fragmentsleam:macro/m3_comp_app_bar_leading_icon_color = 0x7f0c0004
com.example.fragmentsleam:id/cancel_button = 0x7f08006c
com.example.fragmentsleam:dimen/mtrl_card_dragged_z = 0x7f060382
com.example.fragmentsleam:attr/colorOnSecondaryContainer = 0x7f030112
com.example.fragmentsleam:color/m3_simple_item_ripple_color = 0x7f0501e9
com.example.fragmentsleam:attr/voiceIcon = 0x7f030559
com.example.fragmentsleam:dimen/m3_carousel_small_item_size_min = 0x7f0600f7
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_on_surface = 0x7f050228
com.example.fragmentsleam:attr/layout_scrollInterpolator = 0x7f030300
com.example.fragmentsleam:attr/motionEffect_move = 0x7f030394
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f1004b7
com.example.fragmentsleam:attr/arrowShaftLength = 0x7f03003a
com.example.fragmentsleam:color/background_floating_material_light = 0x7f05001e
com.example.fragmentsleam:id/above = 0x7f08000e
com.example.fragmentsleam:attr/motionEasingStandardInterpolator = 0x7f030391
com.example.fragmentsleam:string/mtrl_chip_close_icon_content_description = 0x7f0f006c
com.example.fragmentsleam:attr/motionEasingStandardDecelerateInterpolator = 0x7f030390
com.example.fragmentsleam:id/transitionToStart = 0x7f0801f6
com.example.fragmentsleam:color/m3_dynamic_dark_default_color_primary_text = 0x7f05007c
com.example.fragmentsleam:attr/layout_constraintWidth_percent = 0x7f0302ef
com.example.fragmentsleam:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f060245
com.example.fragmentsleam:color/androidx_core_ripple_material_light = 0x7f05001b
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_pressed_icon_color = 0x7f0c0042
com.example.fragmentsleam:attr/state_collapsible = 0x7f03045b
com.example.fragmentsleam:string/mtrl_picker_text_input_date_hint = 0x7f0f0089
com.example.fragmentsleam:attr/motionEasingLinear = 0x7f03038c
com.example.fragmentsleam:color/m3_ref_palette_neutral10 = 0x7f050157
com.example.fragmentsleam:attr/grid_columnWeights = 0x7f030242
com.example.fragmentsleam:id/view_tree_disjoint_parent = 0x7f080209
com.example.fragmentsleam:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f060376
com.example.fragmentsleam:attr/materialSplitButtonLeadingFilledTonalStyle = 0x7f030353
com.example.fragmentsleam:attr/colorSurface = 0x7f03012b
com.example.fragmentsleam:attr/containerPaddingEnd = 0x7f03014c
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0c0080
com.example.fragmentsleam:color/design_fab_stroke_end_inner_color = 0x7f050050
com.example.fragmentsleam:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f03038a
com.example.fragmentsleam:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f10009b
com.example.fragmentsleam:dimen/m3_comp_app_bar_large_flexible_container_height = 0x7f060101
com.example.fragmentsleam:dimen/abc_text_size_small_material = 0x7f06004c
com.example.fragmentsleam:attr/ensureMinTouchTargetSize = 0x7f0301cd
com.example.fragmentsleam:attr/srcCompat = 0x7f03044d
com.example.fragmentsleam:id/textView = 0x7f0801e2
com.example.fragmentsleam:style/Widget.Material3.MaterialButtonGroup.Connected = 0x7f1003fa
com.example.fragmentsleam:attr/layoutDuringTransition = 0x7f0302bb
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary70 = 0x7f05010d
com.example.fragmentsleam:attr/containerShapeChecked = 0x7f03014f
com.example.fragmentsleam:color/m3_dynamic_dark_default_color_secondary_text = 0x7f05007d
com.example.fragmentsleam:attr/motionEasingEmphasized = 0x7f030388
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f100313
com.example.fragmentsleam:styleable/TabLayout = 0x7f11008f
com.example.fragmentsleam:attr/chipIconSize = 0x7f0300c8
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f05024d
com.example.fragmentsleam:macro/m3_comp_button_filled_unselected_container_color = 0x7f0c001e
com.example.fragmentsleam:attr/motionEasingAccelerated = 0x7f030386
com.example.fragmentsleam:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f06024f
com.example.fragmentsleam:attr/motionDurationShort3 = 0x7f030384
com.example.fragmentsleam:attr/motionDurationShort1 = 0x7f030382
com.example.fragmentsleam:color/m3_sys_color_light_on_background = 0x7f050271
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary40 = 0x7f05010a
com.example.fragmentsleam:attr/labelVisibilityMode = 0x7f0302b5
com.example.fragmentsleam:attr/circularflow_radiusInDP = 0x7f0300db
com.example.fragmentsleam:attr/layout_constraintEnd_toStartOf = 0x7f0302d0
com.example.fragmentsleam:color/mtrl_calendar_item_stroke_color = 0x7f05036a
com.example.fragmentsleam:attr/thumbIconTintMode = 0x7f0304f7
com.example.fragmentsleam:styleable/MotionEffect = 0x7f110069
com.example.fragmentsleam:style/Base.V22.Theme.AppCompat = 0x7f1000ab
com.example.fragmentsleam:attr/expandedSubtitleTextColor = 0x7f0301e5
com.example.fragmentsleam:attr/counterOverflowTextAppearance = 0x7f030176
com.example.fragmentsleam:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300f6
com.example.fragmentsleam:attr/motionDurationMedium1 = 0x7f03037e
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_hovered_label_text_color = 0x7f0c003f
com.example.fragmentsleam:dimen/m3_comp_scrim_container_opacity = 0x7f0601eb
com.example.fragmentsleam:color/m3_sys_color_dark_inverse_on_surface = 0x7f0501fc
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1003e4
com.example.fragmentsleam:attr/waveShape = 0x7f030560
com.example.fragmentsleam:attr/motionDurationExtraLong2 = 0x7f030377
com.example.fragmentsleam:macro/m3_comp_extended_fab_tertiary_container_container_color = 0x7f0c0097
com.example.fragmentsleam:dimen/m3_comp_icon_button_xlarge_outlined_outline_width = 0x7f06018b
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_secondary = 0x7f050230
com.example.fragmentsleam:attr/mock_labelColor = 0x7f030372
com.example.fragmentsleam:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f0601d1
com.example.fragmentsleam:color/material_dynamic_tertiary50 = 0x7f050307
com.example.fragmentsleam:color/m3_ref_palette_pink50 = 0x7f05018f
com.example.fragmentsleam:animator/mtrl_btn_unelevated_state_list_anim = 0x7f02001c
com.example.fragmentsleam:attr/mock_labelBackgroundColor = 0x7f030371
com.example.fragmentsleam:attr/minWidth = 0x7f03036e
com.example.fragmentsleam:dimen/m3_comp_button_text_focused_state_layer_opacity = 0x7f060127
com.example.fragmentsleam:color/m3_ref_palette_orange70 = 0x7f050184
com.example.fragmentsleam:attr/itemGravity = 0x7f03028f
com.example.fragmentsleam:color/bright_foreground_inverse_material_light = 0x7f050025
com.example.fragmentsleam:dimen/material_clock_display_padding = 0x7f060307
com.example.fragmentsleam:id/snap = 0x7f0801b2
com.example.fragmentsleam:color/m3_sys_color_dark_secondary_container = 0x7f05020f
com.example.fragmentsleam:attr/thumbWidth = 0x7f0304ff
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f100214
com.example.fragmentsleam:attr/ratingBarStyleSmall = 0x7f0303f3
com.example.fragmentsleam:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700be
com.example.fragmentsleam:style/ThemeOverlay.Material3.FloatingToolbar.Vibrant = 0x7f1002ee
com.example.fragmentsleam:attr/measureBottomPaddingFromLabelBaseline = 0x7f030364
com.example.fragmentsleam:attr/maxVelocity = 0x7f030362
com.example.fragmentsleam:attr/shapeAppearanceLargeComponent = 0x7f03041c
com.example.fragmentsleam:styleable/ColorStateListItem = 0x7f110024
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f1002a9
com.example.fragmentsleam:attr/tabIndicatorAnimationDuration = 0x7f030483
com.example.fragmentsleam:id/mtrl_view_tag_bottom_padding = 0x7f080142
com.example.fragmentsleam:attr/lineSpacing = 0x7f030307
com.example.fragmentsleam:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f06010d
com.example.fragmentsleam:attr/actionModeCopyDrawable = 0x7f030016
com.example.fragmentsleam:attr/maxAcceleration = 0x7f03035a
com.example.fragmentsleam:color/abc_btn_colored_text_material = 0x7f050003
com.example.fragmentsleam:attr/numericModifiers = 0x7f0303b4
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500e4
com.example.fragmentsleam:id/sawtooth = 0x7f080191
com.example.fragmentsleam:attr/switchTextAppearance = 0x7f03047c
com.example.fragmentsleam:attr/layout_goneMarginStart = 0x7f0302f8
com.example.fragmentsleam:color/mtrl_outlined_stroke_color = 0x7f050387
com.example.fragmentsleam:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0c015d
com.example.fragmentsleam:layout/mtrl_calendar_horizontal = 0x7f0b0051
com.example.fragmentsleam:attr/materialSearchViewToolbarStyle = 0x7f03034e
com.example.fragmentsleam:style/Animation.Material3.BottomSheetDialog = 0x7f100006
com.example.fragmentsleam:attr/checkedIconTint = 0x7f0300be
com.example.fragmentsleam:dimen/mtrl_btn_padding_bottom = 0x7f06034a
com.example.fragmentsleam:attr/materialSearchViewToolbarHeight = 0x7f03034d
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_primary = 0x7f05022e
com.example.fragmentsleam:attr/itemIconTint = 0x7f030295
com.example.fragmentsleam:attr/materialDividerHeavyStyle = 0x7f030344
com.example.fragmentsleam:style/Theme.Design.BottomSheetDialog = 0x7f100267
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_small = 0x7f0602fd
com.example.fragmentsleam:dimen/mtrl_extended_fab_min_width = 0x7f060393
com.example.fragmentsleam:attr/buttonIconTint = 0x7f030095
com.example.fragmentsleam:dimen/m3_chip_corner_size = 0x7f0600f9
com.example.fragmentsleam:attr/actionModeStyle = 0x7f03001e
com.example.fragmentsleam:attr/indicatorColor = 0x7f03027f
com.example.fragmentsleam:attr/materialCircleRadius = 0x7f030341
com.example.fragmentsleam:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
com.example.fragmentsleam:attr/materialCardViewOutlinedStyle = 0x7f03033f
com.example.fragmentsleam:attr/materialCardViewFilledStyle = 0x7f03033e
com.example.fragmentsleam:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f1003c2
com.example.fragmentsleam:id/month_navigation_bar = 0x7f080127
com.example.fragmentsleam:attr/paddingEnd = 0x7f0303c2
com.example.fragmentsleam:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600c9
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050220
com.example.fragmentsleam:color/m3_ref_palette_blue70 = 0x7f0500a8
com.example.fragmentsleam:style/TextAppearance.AppCompat.Inverse = 0x7f1001c4
com.example.fragmentsleam:drawable/abc_list_longpressed_holo = 0x7f070050
com.example.fragmentsleam:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f060241
com.example.fragmentsleam:color/m3_ref_palette_yellow70 = 0x7f0501e3
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0c01b9
com.example.fragmentsleam:attr/motionEffect_translationY = 0x7f030398
com.example.fragmentsleam:attr/horizontalOffset = 0x7f030266
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f100024
com.example.fragmentsleam:attr/materialButtonTonalStyle = 0x7f03032d
com.example.fragmentsleam:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f1002a4
com.example.fragmentsleam:color/m3_ref_palette_secondary99 = 0x7f0501cb
com.example.fragmentsleam:string/mtrl_picker_text_input_date_range_end_hint = 0x7f0f008a
com.example.fragmentsleam:dimen/abc_text_size_medium_material = 0x7f060049
com.example.fragmentsleam:attr/textBackground = 0x7f0304d3
com.example.fragmentsleam:attr/buttonBarPositiveButtonStyle = 0x7f03008f
com.example.fragmentsleam:attr/actionTextColorAlpha = 0x7f030024
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Headline5 = 0x7f100246
com.example.fragmentsleam:attr/materialButtonToggleGroupStyle = 0x7f03032c
com.example.fragmentsleam:attr/materialButtonOutlinedStyle = 0x7f03032a
com.example.fragmentsleam:attr/cornerSizeBottomLeft = 0x7f030170
com.example.fragmentsleam:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1002e8
com.example.fragmentsleam:attr/materialAlertDialogTitlePanelStyle = 0x7f030326
com.example.fragmentsleam:id/design_menu_item_action_area_stub = 0x7f080099
com.example.fragmentsleam:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f060300
com.example.fragmentsleam:attr/textAppearanceHeadline1 = 0x7f0304af
com.example.fragmentsleam:id/tag_unhandled_key_event_manager = 0x7f0801d8
com.example.fragmentsleam:attr/materialAlertDialogTitleIconStyle = 0x7f030325
com.example.fragmentsleam:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301f9
com.example.fragmentsleam:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1000d8
com.example.fragmentsleam:attr/selectableItemBackground = 0x7f03040e
com.example.fragmentsleam:attr/marginBottomSystemWindowInsets = 0x7f03031d
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500de
com.example.fragmentsleam:attr/layout_anchor = 0x7f0302bd
com.example.fragmentsleam:attr/shapeAppearanceCornerLarge = 0x7f030418
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_focused_icon_color = 0x7f0c01d5
com.example.fragmentsleam:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f050092
com.example.fragmentsleam:color/m3_sys_color_dark_surface_variant = 0x7f050218
com.example.fragmentsleam:attr/logo = 0x7f030319
com.example.fragmentsleam:color/m3_ref_palette_cyan60 = 0x7f0500c1
com.example.fragmentsleam:attr/textAppearanceHeadline5 = 0x7f0304b3
com.example.fragmentsleam:animator/mtrl_chip_state_list_anim = 0x7f02001e
com.example.fragmentsleam:attr/scrollingEnabled = 0x7f030408
com.example.fragmentsleam:color/mtrl_choice_chip_text_color = 0x7f050374
com.example.fragmentsleam:dimen/abc_text_size_display_3_material = 0x7f060045
com.example.fragmentsleam:style/Theme.Material3.Light.SideSheetDialog = 0x7f10028a
com.example.fragmentsleam:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1000c1
com.example.fragmentsleam:dimen/m3_appbar_size_medium = 0x7f0600ae
com.example.fragmentsleam:style/Widget.Material3.CheckedTextView = 0x7f1003b0
com.example.fragmentsleam:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1001d1
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f10006c
com.example.fragmentsleam:id/middle = 0x7f080124
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_background = 0x7f05021b
com.example.fragmentsleam:style/Theme.Material3.Light.NoActionBar = 0x7f100289
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0c0129
com.example.fragmentsleam:dimen/disabled_alpha_material_light = 0x7f060091
com.example.fragmentsleam:attr/reactiveGuide_valueId = 0x7f0303f7
com.example.fragmentsleam:attr/passwordToggleTintMode = 0x7f0303d0
com.example.fragmentsleam:id/all = 0x7f08004b
com.example.fragmentsleam:color/material_on_surface_emphasis_high_type = 0x7f050320
com.example.fragmentsleam:attr/minHeight = 0x7f03036a
com.example.fragmentsleam:id/clear_text = 0x7f08007a
com.example.fragmentsleam:attr/listPreferredItemPaddingEnd = 0x7f030314
com.example.fragmentsleam:styleable/Capability = 0x7f110019
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Bridge = 0x7f1002aa
com.example.fragmentsleam:string/mtrl_picker_end_date_description = 0x7f0f007a
com.example.fragmentsleam:id/SHIFT = 0x7f080007
com.example.fragmentsleam:attr/listPreferredItemHeightSmall = 0x7f030313
com.example.fragmentsleam:dimen/m3_comp_app_bar_medium_container_height = 0x7f060102
com.example.fragmentsleam:color/m3_ref_palette_purple60 = 0x7f0501ab
com.example.fragmentsleam:attr/behavior_skipCollapsed = 0x7f030074
com.example.fragmentsleam:attr/listPreferredItemHeight = 0x7f030311
com.example.fragmentsleam:dimen/m3_extended_fab_min_height = 0x7f060264
com.example.fragmentsleam:string/abc_capital_on = 0x7f0f0007
com.example.fragmentsleam:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.example.fragmentsleam:attr/itemShapeInsetTop = 0x7f0302a2
com.example.fragmentsleam:attr/listChoiceBackgroundIndicator = 0x7f030309
com.example.fragmentsleam:attr/motionDurationLong1 = 0x7f03037a
com.example.fragmentsleam:id/ALT = 0x7f080000
com.example.fragmentsleam:dimen/m3_btn_icon_only_default_padding = 0x7f0600d8
com.example.fragmentsleam:color/m3_ref_palette_tertiary50 = 0x7f0501d2
com.example.fragmentsleam:attr/lineHeight = 0x7f030306
com.example.fragmentsleam:attr/hideAnimationBehavior = 0x7f030257
com.example.fragmentsleam:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0c0162
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface = 0x7f050232
com.example.fragmentsleam:style/ThemeOverlay.Material3.TextInputEditText = 0x7f100303
com.example.fragmentsleam:attr/enforceMaterialTheme = 0x7f0301cb
com.example.fragmentsleam:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1002d3
com.example.fragmentsleam:attr/textAppearanceTitleMedium = 0x7f0304cf
com.example.fragmentsleam:macro/m3_comp_radio_button_selected_icon_color = 0x7f0c0143
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_horizontal_active_indicator_height = 0x7f0601b3
com.example.fragmentsleam:color/design_fab_stroke_top_outer_color = 0x7f050053
com.example.fragmentsleam:attr/boxCollapsedPaddingTop = 0x7f030082
com.example.fragmentsleam:attr/layout_scrollFlags = 0x7f0302ff
com.example.fragmentsleam:attr/colorOnTertiaryFixedVariant = 0x7f03011b
com.example.fragmentsleam:color/primary_material_light = 0x7f05039e
com.example.fragmentsleam:color/material_slider_active_tick_marks_color = 0x7f050357
com.example.fragmentsleam:macro/m3_comp_button_outlined_focused_state_layer_color = 0x7f0c002e
com.example.fragmentsleam:styleable/ConstraintLayout_ReactiveGuide = 0x7f110028
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f100034
com.example.fragmentsleam:attr/layout_marginBaseline = 0x7f0302fc
com.example.fragmentsleam:dimen/m3_comp_icon_button_xlarge_wide_leading_space = 0x7f06018c
com.example.fragmentsleam:color/m3_ref_palette_pink100 = 0x7f05018b
com.example.fragmentsleam:id/motion_base = 0x7f08012c
com.example.fragmentsleam:attr/layout_goneMarginTop = 0x7f0302f9
com.example.fragmentsleam:macro/m3_comp_app_bar_on_scroll_container_color = 0x7f0c0008
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070012
com.example.fragmentsleam:id/open_search_view_search_prefix = 0x7f080168
com.example.fragmentsleam:dimen/m3_searchbar_elevation = 0x7f0602a8
com.example.fragmentsleam:attr/textAppearanceSearchResultTitle = 0x7f0304c9
com.example.fragmentsleam:attr/autoTransition = 0x7f030045
com.example.fragmentsleam:color/design_dark_default_color_on_primary = 0x7f050036
com.example.fragmentsleam:attr/marginLeftSystemWindowInsets = 0x7f03031f
com.example.fragmentsleam:macro/m3_comp_search_view_container_color = 0x7f0c0158
com.example.fragmentsleam:macro/m3_comp_button_xlarge_pressed_container_shape = 0x7f0c0060
com.example.fragmentsleam:dimen/mtrl_extended_fab_min_height = 0x7f060392
com.example.fragmentsleam:attr/circularProgressIndicatorStyle = 0x7f0300d7
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0500f2
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.Dialog = 0x7f1002ad
com.example.fragmentsleam:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070059
com.example.fragmentsleam:id/disablePostScroll = 0x7f0800a1
com.example.fragmentsleam:attr/layout_constraintTag = 0x7f0302e4
com.example.fragmentsleam:style/Widget.MaterialComponents.TabLayout = 0x7f1004af
com.example.fragmentsleam:attr/colorTertiaryFixed = 0x7f030138
com.example.fragmentsleam:attr/titleEnabled = 0x7f030510
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.DisplayMedium.Emphasized = 0x7f100203
com.example.fragmentsleam:animator/mtrl_fab_hide_motion_spec = 0x7f020024
com.example.fragmentsleam:attr/layout_constraintStart_toStartOf = 0x7f0302e3
com.example.fragmentsleam:animator/chevron_checked_unchecked = 0x7f020000
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_surface = 0x7f05024a
com.example.fragmentsleam:attr/itemIconGravity = 0x7f030292
com.example.fragmentsleam:attr/layout_constraintRight_toRightOf = 0x7f0302e1
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f100180
com.example.fragmentsleam:dimen/m3_datepicker_elevation = 0x7f06025d
com.example.fragmentsleam:layout/mtrl_picker_dialog = 0x7f0b005c
com.example.fragmentsleam:attr/layout_constraintRight_creator = 0x7f0302df
com.example.fragmentsleam:string/mtrl_picker_text_input_year_abbr = 0x7f0f008e
com.example.fragmentsleam:color/design_default_color_on_secondary = 0x7f050044
com.example.fragmentsleam:attr/maxHeight = 0x7f03035e
com.example.fragmentsleam:dimen/mtrl_calendar_header_content_padding = 0x7f060363
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f050247
com.example.fragmentsleam:color/m3_ref_palette_red100 = 0x7f0501b3
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1001e3
com.example.fragmentsleam:attr/layout_constraintLeft_creator = 0x7f0302dc
com.example.fragmentsleam:attr/layout_constraintHorizontal_chainStyle = 0x7f0302da
com.example.fragmentsleam:attr/limitBoundsTo = 0x7f030305
com.example.fragmentsleam:attr/layout_constraintHeight_min = 0x7f0302d7
com.example.fragmentsleam:attr/materialButtonGroupStyle = 0x7f030329
com.example.fragmentsleam:dimen/tooltip_margin = 0x7f060401
com.example.fragmentsleam:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1002ef
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.NavRail.Expanded.Container.Shape = 0x7f10017b
com.example.fragmentsleam:dimen/m3_comp_toolbar_standard_disabled_icon_opacity = 0x7f060259
com.example.fragmentsleam:attr/layout_constraintHeight = 0x7f0302d4
com.example.fragmentsleam:string/mtrl_picker_announce_current_selection_none = 0x7f0f0073
com.example.fragmentsleam:attr/layout_constraintEnd_toEndOf = 0x7f0302cf
com.example.fragmentsleam:attr/drawableRightCompat = 0x7f0301ab
com.example.fragmentsleam:styleable/DrawerArrowToggle = 0x7f110030
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0c01c9
com.example.fragmentsleam:attr/customDimension = 0x7f030182
com.example.fragmentsleam:attr/layout_constraintCircleRadius = 0x7f0302cd
com.example.fragmentsleam:attr/layout_constraintHorizontal_weight = 0x7f0302db
com.example.fragmentsleam:attr/layout_constraintBottom_toTopOf = 0x7f0302ca
com.example.fragmentsleam:dimen/mtrl_high_ripple_default_alpha = 0x7f06039e
com.example.fragmentsleam:dimen/m3_comp_toolbar_vibrant_disabled_label_text_opacity = 0x7f06025c
com.example.fragmentsleam:attr/layout_constraintBottom_creator = 0x7f0302c8
com.example.fragmentsleam:anim/abc_tooltip_enter = 0x7f01000a
com.example.fragmentsleam:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f06027c
com.example.fragmentsleam:color/m3_slider_inactive_tick_marks_color = 0x7f0501ee
com.example.fragmentsleam:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f100287
com.example.fragmentsleam:id/escape = 0x7f0800bb
com.example.fragmentsleam:attr/materialSearchBarStyle = 0x7f03034a
com.example.fragmentsleam:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1000bb
com.example.fragmentsleam:attr/cornerFamilyTopRight = 0x7f03016d
com.example.fragmentsleam:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1002de
com.example.fragmentsleam:integer/mtrl_switch_track_viewport_height = 0x7f09003f
com.example.fragmentsleam:attr/closeIconEndPadding = 0x7f0300e5
com.example.fragmentsleam:attr/shapeAppearanceMediumComponent = 0x7f03041d
com.example.fragmentsleam:attr/badgeWithTextRadius = 0x7f030060
com.example.fragmentsleam:styleable/KeyFrame = 0x7f110044
com.example.fragmentsleam:id/checked = 0x7f080077
com.example.fragmentsleam:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f100446
com.example.fragmentsleam:attr/bottomSheetDragHandleStyle = 0x7f03007e
com.example.fragmentsleam:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600cb
com.example.fragmentsleam:color/material_personalized_color_on_tertiary = 0x7f050335
com.example.fragmentsleam:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f1004ae
com.example.fragmentsleam:attr/layout_collapseParallaxMultiplier = 0x7f0302c1
com.example.fragmentsleam:color/m3_ref_palette_neutral98 = 0x7f05016c
com.example.fragmentsleam:attr/layout = 0x7f0302b9
com.example.fragmentsleam:attr/lastBaselineToBottomHeight = 0x7f0302b7
com.example.fragmentsleam:attr/helperTextEnabled = 0x7f030254
com.example.fragmentsleam:attr/textBackgroundZoom = 0x7f0304d7
com.example.fragmentsleam:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.example.fragmentsleam:macro/m3_comp_icon_button_medium_container_shape_square = 0x7f0c00bd
com.example.fragmentsleam:attr/cardPreventCornerOverlap = 0x7f0300a2
com.example.fragmentsleam:style/Widget.Material3.SplitButton.LeadingButton.Filled = 0x7f10043b
com.example.fragmentsleam:attr/itemMinHeight = 0x7f030297
com.example.fragmentsleam:attr/largeFontVerticalOffsetAdjustment = 0x7f0302b6
com.example.fragmentsleam:drawable/mtrl_switch_thumb_checked = 0x7f0700cf
com.example.fragmentsleam:attr/actionModeCutDrawable = 0x7f030017
com.example.fragmentsleam:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0c01b0
com.example.fragmentsleam:attr/contentPaddingTop = 0x7f030163
com.example.fragmentsleam:attr/layoutManager = 0x7f0302bc
com.example.fragmentsleam:animator/mtrl_btn_state_list_anim = 0x7f02001b
com.example.fragmentsleam:attr/textAppearanceHeadline6 = 0x7f0304b4
com.example.fragmentsleam:attr/labelTextAppearance = 0x7f0302b4
com.example.fragmentsleam:color/m3_sys_color_light_surface_container_low = 0x7f050287
com.example.fragmentsleam:attr/placeholderTextColor = 0x7f0303db
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_focused_label_text_color = 0x7f0c01e2
com.example.fragmentsleam:attr/controlBackground = 0x7f030166
com.example.fragmentsleam:animator/mtrl_extended_fab_state_list_animator = 0x7f020023
com.example.fragmentsleam:attr/itemTextAppearance = 0x7f0302a6
com.example.fragmentsleam:attr/badgeRadius = 0x7f030055
com.example.fragmentsleam:attr/itemSpacing = 0x7f0302a3
com.example.fragmentsleam:id/material_clock_face = 0x7f080110
com.example.fragmentsleam:attr/expandedTitleSpacing = 0x7f0301ec
com.example.fragmentsleam:id/action_bar = 0x7f080034
com.example.fragmentsleam:attr/currentState = 0x7f03017b
com.example.fragmentsleam:attr/pathMotionArc = 0x7f0303d1
com.example.fragmentsleam:attr/thumbTrackGapSize = 0x7f0304fe
com.example.fragmentsleam:style/Widget.Design.TabLayout = 0x7f100385
com.example.fragmentsleam:attr/dividerPadding = 0x7f03019f
com.example.fragmentsleam:attr/forceDefaultNavigationOnClickListener = 0x7f03023c
com.example.fragmentsleam:attr/fontWeight = 0x7f03023a
com.example.fragmentsleam:style/Widget.Material3.BottomSheet.DragHandle = 0x7f100398
com.example.fragmentsleam:drawable/mtrl_switch_thumb_unchecked = 0x7f0700d5
com.example.fragmentsleam:attr/itemShapeFillColor = 0x7f03029e
com.example.fragmentsleam:dimen/mtrl_btn_max_width = 0x7f060349
com.example.fragmentsleam:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.example.fragmentsleam:color/m3_sys_color_light_tertiary_container = 0x7f05028c
com.example.fragmentsleam:color/m3_ref_palette_primary99 = 0x7f0501a3
com.example.fragmentsleam:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f100257
com.example.fragmentsleam:attr/layout_collapseMode = 0x7f0302c0
com.example.fragmentsleam:attr/itemHorizontalTranslationEnabled = 0x7f030291
com.example.fragmentsleam:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0c0111
com.example.fragmentsleam:attr/cardViewStyle = 0x7f0300a4
com.example.fragmentsleam:attr/chipIconTint = 0x7f0300c9
com.example.fragmentsleam:attr/itemFillColor = 0x7f03028e
com.example.fragmentsleam:animator/m3_elevated_chip_state_list_anim = 0x7f020011
com.example.fragmentsleam:dimen/notification_small_icon_background_padding = 0x7f0603fa
com.example.fragmentsleam:attr/indicatorSize = 0x7f030283
com.example.fragmentsleam:color/m3_sys_color_dark_tertiary_container = 0x7f05021a
com.example.fragmentsleam:attr/containerStrokeWidth = 0x7f030152
com.example.fragmentsleam:attr/layout_constraintBaseline_toBaselineOf = 0x7f0302c5
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500d9
com.example.fragmentsleam:dimen/abc_control_inset_material = 0x7f060019
com.example.fragmentsleam:color/m3_sys_color_dark_primary_container = 0x7f05020d
com.example.fragmentsleam:color/m3_fab_efab_background_color_selector = 0x7f050088
com.example.fragmentsleam:attr/actionModePopupWindowStyle = 0x7f03001a
com.example.fragmentsleam:attr/imagePanY = 0x7f030277
com.example.fragmentsleam:color/design_dark_default_color_on_background = 0x7f050034
com.example.fragmentsleam:style/TextAppearance.M3.Sys.Typescale.BodyMedium.Emphasized = 0x7f1001fd
com.example.fragmentsleam:anim/abc_fade_out = 0x7f010001
com.example.fragmentsleam:attr/imagePanX = 0x7f030276
com.example.fragmentsleam:color/m3_sys_color_primary_fixed_dim = 0x7f050294
com.example.fragmentsleam:attr/boxCornerRadiusTopEnd = 0x7f030085
com.example.fragmentsleam:animator/m3_split_button_chevron_reverse_rotation = 0x7f020019
com.example.fragmentsleam:color/design_dark_default_color_on_surface = 0x7f050038
com.example.fragmentsleam:color/m3_ref_palette_blue95 = 0x7f0500ab
com.example.fragmentsleam:attr/iconTintMode = 0x7f030271
com.example.fragmentsleam:attr/textAppearanceLabelMedium = 0x7f0304bd
com.example.fragmentsleam:attr/maxCharacterCount = 0x7f03035d
com.example.fragmentsleam:color/material_dynamic_secondary20 = 0x7f0502f7
com.example.fragmentsleam:attr/actionBarWidgetTheme = 0x7f03000c
com.example.fragmentsleam:attr/buttonStyle = 0x7f030099
com.example.fragmentsleam:attr/itemTextAppearanceInactive = 0x7f0302a9
com.example.fragmentsleam:dimen/abc_button_padding_vertical_material = 0x7f060015
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0c007a
com.example.fragmentsleam:attr/icon = 0x7f030269
com.example.fragmentsleam:drawable/material_ic_calendar_black_24dp = 0x7f0700ac
com.example.fragmentsleam:color/material_dynamic_tertiary99 = 0x7f05030d
com.example.fragmentsleam:style/ShapeAppearance.MaterialComponents = 0x7f1001a1
com.example.fragmentsleam:id/action_bar_root = 0x7f080037
com.example.fragmentsleam:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.example.fragmentsleam:dimen/mtrl_extended_fab_start_padding_icon = 0x7f060395
com.example.fragmentsleam:style/Widget.Material3.SearchView = 0x7f10042b
com.example.fragmentsleam:attr/actionButtonStyle = 0x7f03000d
com.example.fragmentsleam:attr/contentPadding = 0x7f03015d
com.example.fragmentsleam:dimen/tooltip_corner_radius = 0x7f0603ff
com.example.fragmentsleam:attr/carousel_alignment = 0x7f0300a5
com.example.fragmentsleam:attr/hideOnScroll = 0x7f03025b
com.example.fragmentsleam:attr/expandedTitleMarginEnd = 0x7f0301e9
com.example.fragmentsleam:attr/seekBarStyle = 0x7f03040d
com.example.fragmentsleam:attr/flow_lastHorizontalBias = 0x7f030224
com.example.fragmentsleam:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070081
com.example.fragmentsleam:attr/paddingRightSystemWindowInsets = 0x7f0303c4
com.example.fragmentsleam:attr/floatingToolbarVibrantStyle = 0x7f03021b
com.example.fragmentsleam:string/status_bar_notification_info_overflow = 0x7f0f00ad
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_vertical_leading_space = 0x7f0601bc
com.example.fragmentsleam:color/m3_ref_palette_green70 = 0x7f050137
com.example.fragmentsleam:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f0603ef
com.example.fragmentsleam:id/view_transition = 0x7f080208
com.example.fragmentsleam:attr/helperTextTextAppearance = 0x7f030255
com.example.fragmentsleam:attr/headerMarginBottom = 0x7f030251
com.example.fragmentsleam:attr/materialAlertDialogTheme = 0x7f030324
com.example.fragmentsleam:color/m3_ref_palette_orange95 = 0x7f050187
com.example.fragmentsleam:dimen/mtrl_extended_fab_disabled_elevation = 0x7f06038b
com.example.fragmentsleam:attr/floatingActionButtonSmallSecondaryStyle = 0x7f030213
com.example.fragmentsleam:dimen/material_textinput_min_width_with_label = 0x7f060328
com.example.fragmentsleam:attr/colorSurfaceContainerHigh = 0x7f03012e
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f10041e
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f10018f
com.example.fragmentsleam:dimen/m3_comp_extended_fab_medium_icon_size = 0x7f060143
com.example.fragmentsleam:color/mtrl_on_surface_ripple_color = 0x7f050385
com.example.fragmentsleam:attr/grid_verticalGaps = 0x7f03024c
com.example.fragmentsleam:attr/extendMotionSpec = 0x7f0301f0
com.example.fragmentsleam:attr/colorSwitchThumbNormal = 0x7f030135
com.example.fragmentsleam:attr/checkedIconSize = 0x7f0300bd
com.example.fragmentsleam:dimen/m3_navigation_rail_min_expanded_width = 0x7f06029f
com.example.fragmentsleam:attr/grid_validateInputs = 0x7f03024b
com.example.fragmentsleam:dimen/m3_comp_nav_rail_expanded_container_width_maximum = 0x7f0601ab
com.example.fragmentsleam:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0c0197
com.example.fragmentsleam:macro/m3_comp_extended_fab_primary_container_icon_color = 0x7f0c0090
com.example.fragmentsleam:attr/materialAlertDialogButtonSpacerVisibility = 0x7f030323
com.example.fragmentsleam:style/TextAppearance.Material3.BodySmall = 0x7f10021e
com.example.fragmentsleam:attr/grid_orientation = 0x7f030245
com.example.fragmentsleam:macro/m3_comp_icon_button_medium_selected_container_shape_round = 0x7f0c00bf
com.example.fragmentsleam:color/design_dark_default_color_primary = 0x7f050039
com.example.fragmentsleam:dimen/m3_large_fab_size = 0x7f060276
com.example.fragmentsleam:dimen/m3_comp_button_large_icon_label_space = 0x7f060115
com.example.fragmentsleam:attr/cornerSizeTopLeft = 0x7f030172
com.example.fragmentsleam:attr/backgroundTint = 0x7f030050
com.example.fragmentsleam:anim/abc_popup_enter = 0x7f010003
com.example.fragmentsleam:attr/boxStrokeWidth = 0x7f030089
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0c0074
com.example.fragmentsleam:attr/prefixTextColor = 0x7f0303e4
com.example.fragmentsleam:attr/layout_dodgeInsetEdges = 0x7f0302f0
com.example.fragmentsleam:attr/gapBetweenBars = 0x7f03023f
com.example.fragmentsleam:color/m3_sys_color_light_inverse_on_surface = 0x7f05026e
com.example.fragmentsleam:attr/expandActivityOverflowButtonDrawable = 0x7f0301d8
com.example.fragmentsleam:id/ignoreRequest = 0x7f0800ea
com.example.fragmentsleam:attr/menuGravity = 0x7f030368
com.example.fragmentsleam:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0602ae
com.example.fragmentsleam:color/m3_ref_palette_error10 = 0x7f050122
com.example.fragmentsleam:string/side_sheet_accessibility_pane_title = 0x7f0f00ab
com.example.fragmentsleam:id/baseline = 0x7f08005e
com.example.fragmentsleam:id/edit_text_id = 0x7f0800b3
com.example.fragmentsleam:drawable/notification_bg_normal_pressed = 0x7f0700e3
com.example.fragmentsleam:layout/design_navigation_item_header = 0x7f0b0026
com.example.fragmentsleam:attr/materialIconButtonFilledStyle = 0x7f030346
com.example.fragmentsleam:attr/logoScaleType = 0x7f03031c
com.example.fragmentsleam:attr/buttonIconDimen = 0x7f030094
com.example.fragmentsleam:attr/subheaderColor = 0x7f030469
com.example.fragmentsleam:attr/animateCircleAngleTo = 0x7f030031
com.example.fragmentsleam:attr/textPanX = 0x7f0304e8
com.example.fragmentsleam:attr/behavior_fitToContents = 0x7f03006d
com.example.fragmentsleam:attr/flow_padding = 0x7f030229
com.example.fragmentsleam:style/Widget.Material3.PopupMenu = 0x7f100423
com.example.fragmentsleam:color/m3_sys_color_dark_on_primary = 0x7f050202
com.example.fragmentsleam:drawable/mtrl_ic_check_mark = 0x7f0700c6
com.example.fragmentsleam:attr/colorSecondaryContainer = 0x7f030127
com.example.fragmentsleam:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f0601ef
com.example.fragmentsleam:styleable/AppCompatEmojiHelper = 0x7f11000d
com.example.fragmentsleam:id/clockwise = 0x7f08007d
com.example.fragmentsleam:dimen/mtrl_calendar_month_vertical_padding = 0x7f06036f
com.example.fragmentsleam:attr/flow_lastVerticalBias = 0x7f030226
com.example.fragmentsleam:dimen/m3_chip_disabled_translation_z = 0x7f0600fa
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_container_color = 0x7f0c01d2
com.example.fragmentsleam:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600c2
com.example.fragmentsleam:style/Widget.Material3.DrawerLayout = 0x7f1003d3
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0500e3
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f100139
com.example.fragmentsleam:style/Widget.AppCompat.SearchView = 0x7f10036f
com.example.fragmentsleam:anim/abc_popup_exit = 0x7f010004
com.example.fragmentsleam:drawable/thre = 0x7f0700f0
com.example.fragmentsleam:attr/rotationCenterId = 0x7f030400
com.example.fragmentsleam:attr/boxCornerRadiusBottomStart = 0x7f030084
com.example.fragmentsleam:attr/tabTextAppearance = 0x7f030497
com.example.fragmentsleam:attr/titleTextEllipsize = 0x7f03051b
com.example.fragmentsleam:attr/flow_horizontalStyle = 0x7f030223
com.example.fragmentsleam:color/m3_standard_toolbar_icon_button_ripple_color_selector = 0x7f0501f6
com.example.fragmentsleam:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060242
com.example.fragmentsleam:dimen/abc_dialog_title_divider_material = 0x7f060026
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0c011b
com.example.fragmentsleam:attr/paddingStart = 0x7f0303c5
com.example.fragmentsleam:attr/flow_horizontalAlign = 0x7f030220
com.example.fragmentsleam:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f1004c3
com.example.fragmentsleam:layout/design_layout_tab_text = 0x7f0b0023
com.example.fragmentsleam:attr/containerInsetLeft = 0x7f030148
com.example.fragmentsleam:attr/customNavigationLayout = 0x7f030185
com.example.fragmentsleam:attr/trackTintMode = 0x7f030541
com.example.fragmentsleam:dimen/m3_extended_fab_translation_z_hovered = 0x7f060269
com.example.fragmentsleam:attr/layout_constraintTop_toTopOf = 0x7f0302e7
com.example.fragmentsleam:id/open_search_bar_text_view = 0x7f08015d
com.example.fragmentsleam:string/call_notification_answer_action = 0x7f0f0023
com.example.fragmentsleam:attr/viewTransitionOnNegativeCross = 0x7f030556
com.example.fragmentsleam:attr/startIconScaleType = 0x7f030454
com.example.fragmentsleam:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.example.fragmentsleam:integer/mtrl_view_invisible = 0x7f090043
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f050239
com.example.fragmentsleam:dimen/m3_sys_elevation_level4 = 0x7f0602c3
com.example.fragmentsleam:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0c0112
com.example.fragmentsleam:attr/actionBarTabTextStyle = 0x7f03000a
com.example.fragmentsleam:layout/material_time_input = 0x7f0b0043
com.example.fragmentsleam:attr/actionModeSplitBackground = 0x7f03001d
com.example.fragmentsleam:attr/floatingActionButtonSmallStyle = 0x7f030214
com.example.fragmentsleam:macro/m3_comp_icon_button_tonal_container_color = 0x7f0c00c6
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0500e6
com.example.fragmentsleam:attr/floatingActionButtonSecondaryStyle = 0x7f030211
com.example.fragmentsleam:attr/floatingActionButtonPrimaryStyle = 0x7f030210
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_extra_extra_large = 0x7f0602f5
com.example.fragmentsleam:dimen/m3_comp_button_group_connected_small_between_space = 0x7f060113
com.example.fragmentsleam:attr/floatingActionButtonLargeStyle = 0x7f03020c
com.example.fragmentsleam:layout/mtrl_picker_header_fullscreen = 0x7f0b005f
com.example.fragmentsleam:attr/titleMargin = 0x7f030511
com.example.fragmentsleam:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f050091
com.example.fragmentsleam:style/Theme.MaterialComponents = 0x7f10028b
com.example.fragmentsleam:dimen/mtrl_btn_inset = 0x7f060347
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0c01cb
com.example.fragmentsleam:attr/floatingActionButtonLargeSecondaryStyle = 0x7f03020b
com.example.fragmentsleam:id/wrap_content_constrained = 0x7f080216
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface_dim = 0x7f05025b
com.example.fragmentsleam:style/Widget.Material3.FloatingToolbar.IconButton = 0x7f1003ef
com.example.fragmentsleam:color/material_dynamic_neutral0 = 0x7f0502cd
com.example.fragmentsleam:color/design_dark_default_color_on_error = 0x7f050035
com.example.fragmentsleam:color/material_slider_active_track_color = 0x7f050358
com.example.fragmentsleam:attr/fastScrollHorizontalTrackDrawable = 0x7f030206
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_extra_large = 0x7f0602f6
com.example.fragmentsleam:color/material_personalized_color_control_highlight = 0x7f050327
com.example.fragmentsleam:color/m3_ref_palette_grey_variant70 = 0x7f050151
com.example.fragmentsleam:style/Widget.MaterialComponents.Slider = 0x7f1004ab
com.example.fragmentsleam:attr/floatingActionButtonMediumStyle = 0x7f03020f
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1001db
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500ce
com.example.fragmentsleam:id/overshoot = 0x7f08016f
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1001da
com.example.fragmentsleam:dimen/mtrl_navigation_rail_icon_margin = 0x7f0603b3
com.example.fragmentsleam:attr/horizontalItemSpacing = 0x7f030263
com.example.fragmentsleam:attr/buttonSizeChange = 0x7f030098
com.example.fragmentsleam:attr/fabCradleMargin = 0x7f0301ff
com.example.fragmentsleam:layout/abc_expanded_menu_layout = 0x7f0b000d
com.example.fragmentsleam:string/mtrl_picker_text_input_month_abbr = 0x7f0f008d
com.example.fragmentsleam:dimen/m3_comp_split_button_large_trailing_button_leading_space = 0x7f060220
com.example.fragmentsleam:attr/horizontalItemTextAppearanceActive = 0x7f030264
com.example.fragmentsleam:style/Widget.AppCompat.Toolbar = 0x7f100379
com.example.fragmentsleam:string/material_timepicker_select_time = 0x7f0f005c
com.example.fragmentsleam:attr/motionSpringDefaultEffects = 0x7f03039e
com.example.fragmentsleam:styleable/LinearLayoutCompat = 0x7f11004b
com.example.fragmentsleam:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1003db
com.example.fragmentsleam:attr/thickness = 0x7f0304f0
com.example.fragmentsleam:id/visible_removing_fragment_view_tag = 0x7f08020f
com.example.fragmentsleam:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301f5
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Title.Text = 0x7f10012e
com.example.fragmentsleam:style/TextAppearance.Design.Counter = 0x7f1001f0
com.example.fragmentsleam:color/switch_thumb_normal_material_dark = 0x7f0503ad
com.example.fragmentsleam:attr/cornerRadius = 0x7f03016e
com.example.fragmentsleam:attr/textAppearanceHeadlineSmallEmphasized = 0x7f0304ba
com.example.fragmentsleam:attr/counterTextColor = 0x7f030179
com.example.fragmentsleam:dimen/m3_comp_button_xsmall_leading_space = 0x7f060132
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f050258
com.example.fragmentsleam:style/Widget.Material3.Button = 0x7f10039a
com.example.fragmentsleam:attr/layout_anchorGravity = 0x7f0302be
com.example.fragmentsleam:color/material_personalized_color_surface_container_highest = 0x7f050346
com.example.fragmentsleam:attr/checkedIcon = 0x7f0300b9
com.example.fragmentsleam:color/material_personalized_color_on_tertiary_container = 0x7f050336
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f05024c
com.example.fragmentsleam:attr/materialCalendarHeaderTitle = 0x7f030336
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0c013a
com.example.fragmentsleam:attr/minHideDelay = 0x7f03036b
com.example.fragmentsleam:macro/m3_comp_switch_selected_handle_color = 0x7f0c018f
com.example.fragmentsleam:attr/expandedMaxWidth = 0x7f0301e2
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0c0106
com.example.fragmentsleam:attr/chipStartPadding = 0x7f0300d1
com.example.fragmentsleam:attr/maxNumber = 0x7f030361
com.example.fragmentsleam:anim/abc_slide_out_bottom = 0x7f010008
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f10015c
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_vibrant_icon_color = 0x7f0c0202
com.example.fragmentsleam:attr/expandedMarginHorizontal = 0x7f0301e1
com.example.fragmentsleam:attr/expandedActiveIndicatorPaddingTop = 0x7f0301dd
com.example.fragmentsleam:attr/extraMultilineHeightEnabled = 0x7f0301fa
com.example.fragmentsleam:id/month_navigation_previous = 0x7f08012a
com.example.fragmentsleam:attr/backgroundSplit = 0x7f03004e
com.example.fragmentsleam:dimen/m3_appbar_size_medium_with_subtitle = 0x7f0600af
com.example.fragmentsleam:attr/dividerVertical = 0x7f0301a1
com.example.fragmentsleam:attr/errorContentDescription = 0x7f0301d0
com.example.fragmentsleam:id/material_clock_level = 0x7f080112
com.example.fragmentsleam:attr/flow_verticalGap = 0x7f03022c
com.example.fragmentsleam:color/material_dynamic_secondary99 = 0x7f050300
com.example.fragmentsleam:style/Theme.Design.Light.BottomSheetDialog = 0x7f100269
com.example.fragmentsleam:color/m3_sys_color_light_on_primary = 0x7f050274
com.example.fragmentsleam:attr/backgroundOverlayColorAlpha = 0x7f03004d
com.example.fragmentsleam:attr/endInsetScrimEnabled = 0x7f0301ca
com.example.fragmentsleam:attr/actionModeSelectAllDrawable = 0x7f03001b
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary0 = 0x7f050113
com.example.fragmentsleam:attr/motion_triggerOnCollision = 0x7f0303a7
com.example.fragmentsleam:attr/maxWidth = 0x7f030363
com.example.fragmentsleam:color/mtrl_navigation_bar_ripple_color = 0x7f050380
com.example.fragmentsleam:attr/damping = 0x7f030189
com.example.fragmentsleam:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060335
com.example.fragmentsleam:attr/endIconTint = 0x7f0301c8
com.example.fragmentsleam:attr/endIconMinSize = 0x7f0301c5
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_error_container = 0x7f05021d
com.example.fragmentsleam:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f10010b
com.example.fragmentsleam:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070072
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_on_error = 0x7f050244
com.example.fragmentsleam:attr/endIconCheckable = 0x7f0301c2
com.example.fragmentsleam:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0603c3
com.example.fragmentsleam:color/mtrl_tabs_legacy_text_color_selector = 0x7f050391
com.example.fragmentsleam:attr/customReference = 0x7f030187
com.example.fragmentsleam:styleable/CompoundButton = 0x7f110025
com.example.fragmentsleam:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700d3
com.example.fragmentsleam:color/m3_ref_palette_secondary98 = 0x7f0501ca
com.example.fragmentsleam:attr/colorOnSurfaceInverse = 0x7f030116
com.example.fragmentsleam:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b6
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f100038
com.example.fragmentsleam:dimen/design_snackbar_text_size = 0x7f06008a
com.example.fragmentsleam:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f1002b5
com.example.fragmentsleam:attr/elevationOverlayColor = 0x7f0301be
com.example.fragmentsleam:attr/ratingBarStyleIndicator = 0x7f0303f2
com.example.fragmentsleam:color/m3_ref_palette_orange30 = 0x7f050180
com.example.fragmentsleam:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1002ca
com.example.fragmentsleam:id/position = 0x7f08017d
com.example.fragmentsleam:attr/dropdownListPreferredItemHeight = 0x7f0301b6
com.example.fragmentsleam:color/m3_ref_palette_neutral96 = 0x7f05016b
com.example.fragmentsleam:string/error_icon_content_description = 0x7f0f002f
com.example.fragmentsleam:attr/behavior_autoHide = 0x7f030068
com.example.fragmentsleam:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.example.fragmentsleam:attr/layout_constrainedWidth = 0x7f0302c3
com.example.fragmentsleam:dimen/abc_config_prefDialogWidth = 0x7f060017
com.example.fragmentsleam:attr/drawableTint = 0x7f0301ae
com.example.fragmentsleam:style/TextAppearance.Material3.TitleLarge.Emphasized = 0x7f100237
com.example.fragmentsleam:attr/drawableSize = 0x7f0301ac
com.example.fragmentsleam:attr/sideSheetModalStyle = 0x7f030436
com.example.fragmentsleam:animator/fragment_close_exit = 0x7f020006
com.example.fragmentsleam:id/textSpacerNoTitle = 0x7f0801df
com.example.fragmentsleam:dimen/m3_comp_assist_chip_container_height = 0x7f060106
com.example.fragmentsleam:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f100442
com.example.fragmentsleam:attr/fabCradleVerticalOffset = 0x7f030201
com.example.fragmentsleam:attr/fabCradleRoundedCornerRadius = 0x7f030200
com.example.fragmentsleam:macro/m3_comp_fab_secondary_container_container_color = 0x7f0c00a1
com.example.fragmentsleam:dimen/m3_comp_icon_button_medium_default_leading_space = 0x7f060176
com.example.fragmentsleam:attr/drawPath = 0x7f0301a7
com.example.fragmentsleam:attr/minSeparation = 0x7f03036c
com.example.fragmentsleam:attr/viewTransitionOnPositiveCross = 0x7f030557
com.example.fragmentsleam:id/advance = 0x7f080048
com.example.fragmentsleam:dimen/material_clock_size = 0x7f060313
com.example.fragmentsleam:attr/badgeVerticalPadding = 0x7f03005c
com.example.fragmentsleam:attr/dragScale = 0x7f0301a5
com.example.fragmentsleam:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1003e9
com.example.fragmentsleam:attr/dragDirection = 0x7f0301a4
com.example.fragmentsleam:attr/dockedToolbarVibrantStyle = 0x7f0301a3
com.example.fragmentsleam:color/material_personalized_color_tertiary = 0x7f05034c
com.example.fragmentsleam:animator/m3_fab_state_list_animator = 0x7f020017
com.example.fragmentsleam:attr/badgeWithTextHeight = 0x7f03005f
com.example.fragmentsleam:attr/behavior_draggable = 0x7f03006a
com.example.fragmentsleam:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0c0188
com.example.fragmentsleam:color/design_fab_stroke_top_inner_color = 0x7f050052
com.example.fragmentsleam:attr/dialogTheme = 0x7f030198
com.example.fragmentsleam:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.example.fragmentsleam:attr/marginTopSystemWindowInsets = 0x7f030321
com.example.fragmentsleam:drawable/abc_btn_check_material_anim = 0x7f07002d
com.example.fragmentsleam:attr/behavior_significantVelocityThreshold = 0x7f030073
com.example.fragmentsleam:attr/contentInsetStartWithNavigation = 0x7f03015b
com.example.fragmentsleam:attr/checkMarkTint = 0x7f0300b4
com.example.fragmentsleam:attr/deriveConstraintsFrom = 0x7f030195
com.example.fragmentsleam:attr/textAppearanceLabelMediumEmphasized = 0x7f0304be
com.example.fragmentsleam:id/view_tree_lifecycle_owner = 0x7f08020a
com.example.fragmentsleam:dimen/mtrl_tooltip_minHeight = 0x7f0603ec
com.example.fragmentsleam:attr/expandedSubtitleTextAppearance = 0x7f0301e4
com.example.fragmentsleam:attr/dividerHorizontal = 0x7f03019c
com.example.fragmentsleam:style/ThemeOverlay.Material3.NavigationView = 0x7f1002fb
com.example.fragmentsleam:attr/buttonBarButtonStyle = 0x7f03008c
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500d7
com.example.fragmentsleam:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0c019b
com.example.fragmentsleam:attr/itemStrokeWidth = 0x7f0302a5
com.example.fragmentsleam:attr/shapeAppearanceCornerExtraLarge = 0x7f030415
com.example.fragmentsleam:macro/m3_comp_button_outlined_pressed_state_layer_color = 0x7f0c0038
com.example.fragmentsleam:attr/defaultState = 0x7f030192
com.example.fragmentsleam:attr/dayInvalidStyle = 0x7f03018a
com.example.fragmentsleam:dimen/m3_comp_slider_active_handle_height = 0x7f0601ff
com.example.fragmentsleam:attr/actionDropDownStyle = 0x7f03000e
com.example.fragmentsleam:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f0601c0
com.example.fragmentsleam:anim/abc_tooltip_exit = 0x7f01000b
com.example.fragmentsleam:dimen/mtrl_tooltip_cornerSize = 0x7f0603eb
com.example.fragmentsleam:color/m3_sys_color_dark_on_tertiary_container = 0x7f050209
com.example.fragmentsleam:animator/mtrl_extended_fab_show_motion_spec = 0x7f020022
com.example.fragmentsleam:color/background_material_light = 0x7f050020
com.example.fragmentsleam:style/Widget.Material3.Chip.Assist.Elevated = 0x7f1003b2
com.example.fragmentsleam:attr/brightness = 0x7f03008b
com.example.fragmentsleam:attr/textAppearanceLabelSmallEmphasized = 0x7f0304c0
com.example.fragmentsleam:color/m3_ref_palette_grey_variant98 = 0x7f050155
com.example.fragmentsleam:attr/dayStyle = 0x7f03018c
com.example.fragmentsleam:integer/mtrl_view_visible = 0x7f090044
com.example.fragmentsleam:attr/badgeWithTextShapeAppearanceOverlay = 0x7f030062
com.example.fragmentsleam:attr/layout_constraintWidth = 0x7f0302eb
com.example.fragmentsleam:dimen/abc_text_size_headline_material = 0x7f060047
com.example.fragmentsleam:color/m3_ref_palette_purple30 = 0x7f0501a8
com.example.fragmentsleam:attr/errorIconDrawable = 0x7f0301d2
com.example.fragmentsleam:attr/colorControlHighlight = 0x7f030102
com.example.fragmentsleam:dimen/m3_btn_padding_top = 0x7f0600e1
com.example.fragmentsleam:attr/textInputOutlinedDenseStyle = 0x7f0304e1
com.example.fragmentsleam:attr/windowFixedHeightMinor = 0x7f03056b
com.example.fragmentsleam:attr/textInputFilledStyle = 0x7f0304df
com.example.fragmentsleam:attr/customStringValue = 0x7f030188
com.example.fragmentsleam:attr/autoCompleteMode = 0x7f03003d
com.example.fragmentsleam:dimen/m3_comp_fab_primary_container_pressed_container_elevation = 0x7f06015d
com.example.fragmentsleam:macro/m3_comp_nav_bar_item_inactive_hovered_state_layer_color = 0x7f0c00de
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant30 = 0x7f050172
com.example.fragmentsleam:style/Widget.Material3.Chip.Input.Icon = 0x7f1003b7
com.example.fragmentsleam:attr/popupMenuBackground = 0x7f0303de
com.example.fragmentsleam:attr/tickVisibilityMode = 0x7f030508
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0c0136
com.example.fragmentsleam:dimen/mtrl_switch_track_height = 0x7f0603de
com.example.fragmentsleam:dimen/highlight_alpha_material_light = 0x7f060097
com.example.fragmentsleam:id/text_input_end_icon = 0x7f0801e3
com.example.fragmentsleam:attr/checkedIconGravity = 0x7f0300bb
com.example.fragmentsleam:id/hideable = 0x7f0800e0
com.example.fragmentsleam:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.example.fragmentsleam:id/horizontal_only = 0x7f0800e5
com.example.fragmentsleam:string/abc_shareactionprovider_share_with_application = 0x7f0f0019
com.example.fragmentsleam:attr/cursorErrorColor = 0x7f03017d
com.example.fragmentsleam:id/ifRoom = 0x7f0800e8
com.example.fragmentsleam:attr/colorSecondaryVariant = 0x7f03012a
com.example.fragmentsleam:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0c0191
com.example.fragmentsleam:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f06024a
com.example.fragmentsleam:attr/constraintSetStart = 0x7f03013f
com.example.fragmentsleam:dimen/design_snackbar_elevation = 0x7f060083
com.example.fragmentsleam:color/m3_sys_color_light_surface_container = 0x7f050284
com.example.fragmentsleam:attr/offsetAlignmentMode = 0x7f0303b5
com.example.fragmentsleam:drawable/$avd_show_password__2 = 0x7f070005
com.example.fragmentsleam:style/Widget.AppCompat.ActionMode = 0x7f10033a
com.example.fragmentsleam:styleable/SnackbarLayout = 0x7f110085
com.example.fragmentsleam:attr/materialSearchViewStyle = 0x7f03034c
com.example.fragmentsleam:attr/cornerFamilyBottomRight = 0x7f03016b
com.example.fragmentsleam:attr/cornerFamilyBottomLeft = 0x7f03016a
com.example.fragmentsleam:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.example.fragmentsleam:attr/coplanarSiblingViewId = 0x7f030168
com.example.fragmentsleam:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1002e2
com.example.fragmentsleam:string/mtrl_picker_cancel = 0x7f0f0074
com.example.fragmentsleam:dimen/notification_content_margin_start = 0x7f0603f3
com.example.fragmentsleam:attr/keyboardIcon = 0x7f0302ad
com.example.fragmentsleam:macro/m3_comp_dialog_container_color = 0x7f0c0082
com.example.fragmentsleam:color/m3expressive_nav_rail_item_ripple_tint = 0x7f0502bd
com.example.fragmentsleam:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0c0149
com.example.fragmentsleam:attr/labelMaxLines = 0x7f0302b2
com.example.fragmentsleam:dimen/mtrl_navigation_rail_active_text_size = 0x7f0603af
com.example.fragmentsleam:attr/contentPaddingStart = 0x7f030162
com.example.fragmentsleam:attr/bottomInsetScrimEnabled = 0x7f03007b
com.example.fragmentsleam:attr/textAppearanceButton = 0x7f0304a7
com.example.fragmentsleam:dimen/design_fab_translation_z_hovered_focused = 0x7f060075
com.example.fragmentsleam:attr/iconifiedByDefault = 0x7f030272
com.example.fragmentsleam:style/TextAppearance.Material3.LabelLarge.Emphasized = 0x7f10022d
com.example.fragmentsleam:color/m3_sys_color_dark_on_surface_variant = 0x7f050207
com.example.fragmentsleam:attr/values = 0x7f030550
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f100467
com.example.fragmentsleam:style/Animation.Material3.SideSheetDialog.Left = 0x7f100008
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0c0104
com.example.fragmentsleam:color/material_deep_teal_200 = 0x7f0502c2
com.example.fragmentsleam:attr/counterEnabled = 0x7f030174
com.example.fragmentsleam:attr/colorTertiary = 0x7f030136
com.example.fragmentsleam:dimen/m3_comp_split_button_medium_trailing_button_icon_size = 0x7f060224
com.example.fragmentsleam:drawable/abc_spinner_textfield_background_material = 0x7f070067
com.example.fragmentsleam:color/m3_ref_palette_purple70 = 0x7f0501ac
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f100418
com.example.fragmentsleam:attr/textFillColor = 0x7f0304dc
com.example.fragmentsleam:attr/spinnerDropDownItemStyle = 0x7f030445
com.example.fragmentsleam:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f1003a6
com.example.fragmentsleam:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f0601c5
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f10029c
com.example.fragmentsleam:id/disableHome = 0x7f08009f
com.example.fragmentsleam:attr/errorEnabled = 0x7f0301d1
com.example.fragmentsleam:attr/backgroundInsetTop = 0x7f03004c
com.example.fragmentsleam:dimen/m3_searchbar_padding_start = 0x7f0602ad
com.example.fragmentsleam:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0c0113
com.example.fragmentsleam:attr/windowFixedWidthMinor = 0x7f03056d
com.example.fragmentsleam:attr/flow_wrapMode = 0x7f03022e
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f100321
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070015
com.example.fragmentsleam:dimen/m3_navigation_rail_item_padding_bottom = 0x7f060299
com.example.fragmentsleam:attr/keyPositionType = 0x7f0302ac
com.example.fragmentsleam:style/Widget.Material3.MaterialTimePicker.Button = 0x7f100417
com.example.fragmentsleam:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f100191
com.example.fragmentsleam:attr/containerPaddingBottom = 0x7f03014b
com.example.fragmentsleam:color/bright_foreground_material_dark = 0x7f050026
com.example.fragmentsleam:animator/m3_chip_state_list_anim = 0x7f020010
com.example.fragmentsleam:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f100368
com.example.fragmentsleam:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1002dc
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f050241
com.example.fragmentsleam:attr/containerInsetTop = 0x7f03014a
com.example.fragmentsleam:color/material_dynamic_neutral_variant80 = 0x7f0502e3
com.example.fragmentsleam:attr/background = 0x7f030047
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500f3
com.example.fragmentsleam:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.example.fragmentsleam:color/design_dark_default_color_background = 0x7f050032
com.example.fragmentsleam:attr/rangeFillColor = 0x7f0303f0
com.example.fragmentsleam:dimen/m3_slider_thumb_elevation = 0x7f0602b9
com.example.fragmentsleam:attr/iconGravity = 0x7f03026b
com.example.fragmentsleam:drawable/mf = 0x7f0700b5
com.example.fragmentsleam:dimen/m3_comp_button_large_icon_size = 0x7f060116
com.example.fragmentsleam:attr/commitIcon = 0x7f03013a
com.example.fragmentsleam:color/m3_sys_color_dynamic_light_surface = 0x7f050254
com.example.fragmentsleam:attr/startIconCheckable = 0x7f030450
com.example.fragmentsleam:attr/grid_spans = 0x7f030249
com.example.fragmentsleam:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f10032f
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f100359
com.example.fragmentsleam:attr/SharedValueId = 0x7f030001
com.example.fragmentsleam:attr/customColorValue = 0x7f030181
com.example.fragmentsleam:macro/m3_comp_button_outlined_unselected_pressed_label_text_color = 0x7f0c0050
com.example.fragmentsleam:drawable/abc_control_background_material = 0x7f07003b
com.example.fragmentsleam:dimen/m3_comp_fab_medium_container_height = 0x7f060156
com.example.fragmentsleam:dimen/m3_carousel_debug_keyline_width = 0x7f0600f2
com.example.fragmentsleam:drawable/notification_oversize_large_icon_bg = 0x7f0700e5
com.example.fragmentsleam:attr/passwordToggleTint = 0x7f0303cf
com.example.fragmentsleam:attr/thumbRadius = 0x7f0304f8
com.example.fragmentsleam:macro/m3_comp_toolbar_standard_selected_button_container_color = 0x7f0c01e0
com.example.fragmentsleam:attr/textColorAlertDialogListItem = 0x7f0304d9
com.example.fragmentsleam:attr/listPopupWindowStyle = 0x7f030310
com.example.fragmentsleam:attr/stiffness = 0x7f030465
com.example.fragmentsleam:attr/waveVariesBy = 0x7f030562
com.example.fragmentsleam:attr/textAppearanceDisplayMedium = 0x7f0304ab
com.example.fragmentsleam:attr/isMaterial3DynamicColorApplied = 0x7f030289
com.example.fragmentsleam:attr/colorSurfaceInverse = 0x7f030133
com.example.fragmentsleam:color/m3_ref_palette_neutral87 = 0x7f050166
com.example.fragmentsleam:attr/colorOnPrimary = 0x7f03010c
com.example.fragmentsleam:attr/layout_constraintGuide_begin = 0x7f0302d1
com.example.fragmentsleam:macro/m3_comp_fab_secondary_icon_color = 0x7f0c00a3
com.example.fragmentsleam:color/m3_sys_color_dynamic_secondary_fixed = 0x7f050267
com.example.fragmentsleam:drawable/abc_cab_background_top_material = 0x7f070039
com.example.fragmentsleam:attr/errorTextAppearance = 0x7f0301d6
com.example.fragmentsleam:dimen/abc_switch_padding = 0x7f06003e
com.example.fragmentsleam:style/Theme.AppCompat.CompactMenu = 0x7f100251
com.example.fragmentsleam:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1002c8
com.example.fragmentsleam:color/accent_material_light = 0x7f05001a
com.example.fragmentsleam:styleable/RangeSlider = 0x7f110079
com.example.fragmentsleam:attr/materialCalendarStyle = 0x7f03033a
com.example.fragmentsleam:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.example.fragmentsleam:id/open_search_view_toolbar_container = 0x7f08016c
com.example.fragmentsleam:attr/textAppearanceTitleMediumEmphasized = 0x7f0304d0
com.example.fragmentsleam:attr/colorOnPrimaryFixedVariant = 0x7f03010f
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f100488
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_surface_container = 0x7f050234
com.example.fragmentsleam:style/TextAppearance.Material3.DisplayLarge.Emphasized = 0x7f100221
com.example.fragmentsleam:attr/colorOnPrimaryFixed = 0x7f03010e
com.example.fragmentsleam:attr/foregroundInsidePadding = 0x7f03023d
com.example.fragmentsleam:macro/m3_comp_button_filled_container_color = 0x7f0c001a
com.example.fragmentsleam:attr/drawableTintMode = 0x7f0301af
com.example.fragmentsleam:color/m3_sys_color_light_outline_variant = 0x7f05027d
com.example.fragmentsleam:attr/closeIconEnabled = 0x7f0300e4
com.example.fragmentsleam:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f0602dc
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_focused_state_layer_color = 0x7f0c003d
com.example.fragmentsleam:attr/circularflow_viewCenter = 0x7f0300dc
com.example.fragmentsleam:attr/colorOnPrimaryContainer = 0x7f03010d
com.example.fragmentsleam:dimen/design_tab_text_size_2line = 0x7f06008e
com.example.fragmentsleam:attr/textPanY = 0x7f0304e9
com.example.fragmentsleam:attr/trackIconInactiveColor = 0x7f030537
com.example.fragmentsleam:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060084
com.example.fragmentsleam:attr/materialAlertDialogTitleTextStyle = 0x7f030327
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_selected_hovered_icon_color = 0x7f0c01fa
com.example.fragmentsleam:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0c01ae
com.example.fragmentsleam:attr/cardElevation = 0x7f03009f
com.example.fragmentsleam:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f0603e4
com.example.fragmentsleam:color/m3_ref_palette_blue20 = 0x7f0500a3
com.example.fragmentsleam:id/horizontal = 0x7f0800e4
com.example.fragmentsleam:color/m3_sys_color_light_on_surface = 0x7f050278
com.example.fragmentsleam:dimen/mtrl_btn_text_size = 0x7f060354
com.example.fragmentsleam:attr/colorControlNormal = 0x7f030103
com.example.fragmentsleam:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f0602cd
com.example.fragmentsleam:color/m3_ref_palette_yellow10 = 0x7f0501dc
com.example.fragmentsleam:styleable/ScrimInsetsFrameLayout = 0x7f11007c
com.example.fragmentsleam:attr/actionBarPopupTheme = 0x7f030004
com.example.fragmentsleam:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
com.example.fragmentsleam:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0c013b
com.example.fragmentsleam:attr/pivotAnchor = 0x7f0303d8
com.example.fragmentsleam:color/m3_ref_palette_tertiary99 = 0x7f0501d9
com.example.fragmentsleam:attr/colorPrimaryVariant = 0x7f030125
com.example.fragmentsleam:style/Widget.AppCompat.Button.Small = 0x7f100342
com.example.fragmentsleam:attr/colorContainerUnchecked = 0x7f030100
com.example.fragmentsleam:attr/customIntegerValue = 0x7f030184
com.example.fragmentsleam:dimen/m3_searchbar_text_size = 0x7f0602af
com.example.fragmentsleam:style/Widget.Design.AppBarLayout = 0x7f10037d
com.example.fragmentsleam:styleable/MockView = 0x7f110067
com.example.fragmentsleam:attr/bottomNavigationStyle = 0x7f03007c
com.example.fragmentsleam:attr/centered = 0x7f0300b1
com.example.fragmentsleam:macro/m3_comp_nav_rail_item_horizontal_label_text_font = 0x7f0c00ea
com.example.fragmentsleam:attr/collapsingToolbarLayoutMediumSize = 0x7f0300f7
com.example.fragmentsleam:attr/clockIcon = 0x7f0300e1
com.example.fragmentsleam:color/m3_ref_palette_primary70 = 0x7f05019e
com.example.fragmentsleam:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f10006d
com.example.fragmentsleam:attr/errorAccessibilityLabel = 0x7f0301ce
com.example.fragmentsleam:color/m3_ref_palette_dynamic_primary30 = 0x7f0500fb
com.example.fragmentsleam:attr/blendSrc = 0x7f030075
com.example.fragmentsleam:styleable/TextInputLayout = 0x7f110093
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.Button = 0x7f1001a7
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f100137
com.example.fragmentsleam:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f0601bf
com.example.fragmentsleam:attr/layout_goneMarginBottom = 0x7f0302f4
com.example.fragmentsleam:attr/actionLayout = 0x7f03000f
com.example.fragmentsleam:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f100424
com.example.fragmentsleam:macro/m3_comp_icon_button_xsmall_selected_container_shape_round = 0x7f0c00cd
com.example.fragmentsleam:attr/subheaderInsetStart = 0x7f03046b
com.example.fragmentsleam:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1004b8
com.example.fragmentsleam:dimen/mtrl_slider_tick_min_spacing = 0x7f0603cf
com.example.fragmentsleam:dimen/m3_comp_button_elevated_container_elevation = 0x7f06010f
com.example.fragmentsleam:attr/colorSecondaryFixedDim = 0x7f030129
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0c007d
com.example.fragmentsleam:dimen/m3_comp_split_button_xlarge_trailing_button_trailing_space = 0x7f060230
com.example.fragmentsleam:style/Base.V26.Theme.AppCompat = 0x7f1000b3
com.example.fragmentsleam:string/abc_searchview_description_search = 0x7f0f0015
com.example.fragmentsleam:attr/collapseIcon = 0x7f0300ec
com.example.fragmentsleam:attr/waveAmplitude = 0x7f03055b
com.example.fragmentsleam:attr/collapseContentDescription = 0x7f0300eb
com.example.fragmentsleam:color/material_personalized_primary_text_disable_only = 0x7f050356
com.example.fragmentsleam:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f06016d
com.example.fragmentsleam:color/m3_sys_color_dark_on_primary_container = 0x7f050203
com.example.fragmentsleam:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f100114
com.example.fragmentsleam:color/accent_material_dark = 0x7f050019
com.example.fragmentsleam:drawable/btn_checkbox_unchecked_mtrl = 0x7f07007c
com.example.fragmentsleam:attr/clearsTag = 0x7f0300dd
com.example.fragmentsleam:string/call_notification_hang_up_action = 0x7f0f0026
com.example.fragmentsleam:id/tag_unhandled_key_listeners = 0x7f0801d9
com.example.fragmentsleam:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f060238
com.example.fragmentsleam:id/mtrl_calendar_days_of_week = 0x7f08012e
com.example.fragmentsleam:attr/labelFontScalingEnabled = 0x7f0302b1
com.example.fragmentsleam:color/design_default_color_background = 0x7f05003f
com.example.fragmentsleam:string/mtrl_picker_start_date_description = 0x7f0f0088
com.example.fragmentsleam:style/Motion.Material3.Spring.Standard.Slow.Spatial = 0x7f10013f
com.example.fragmentsleam:attr/floatingToolbarStyle = 0x7f03021a
com.example.fragmentsleam:attr/itemTextAppearanceActiveBoldEnabled = 0x7f0302a8
com.example.fragmentsleam:attr/textAppearanceDisplayMediumEmphasized = 0x7f0304ac
com.example.fragmentsleam:attr/buttonTint = 0x7f03009b
com.example.fragmentsleam:attr/onShow = 0x7f0303ba
com.example.fragmentsleam:string/abc_shareactionprovider_share_with = 0x7f0f0018
com.example.fragmentsleam:attr/colorOnError = 0x7f03010a
com.example.fragmentsleam:attr/contentPaddingRight = 0x7f030161
com.example.fragmentsleam:attr/iconPadding = 0x7f03026d
com.example.fragmentsleam:attr/collapsedTitleGravity = 0x7f0300f1
com.example.fragmentsleam:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0c01c1
com.example.fragmentsleam:attr/boxBackgroundColor = 0x7f030080
com.example.fragmentsleam:color/m3_ref_palette_neutral17 = 0x7f05015a
com.example.fragmentsleam:id/autoCompleteToEnd = 0x7f080057
com.example.fragmentsleam:attr/circleRadius = 0x7f0300d6
com.example.fragmentsleam:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f060295
com.example.fragmentsleam:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f100478
com.example.fragmentsleam:style/Widget.Material3.NavigationRailView = 0x7f10041f
com.example.fragmentsleam:dimen/abc_action_button_min_width_material = 0x7f06000e
com.example.fragmentsleam:styleable/ActivityChooserView = 0x7f110005
com.example.fragmentsleam:attr/colorOnSurface = 0x7f030115
com.example.fragmentsleam:dimen/design_bottom_navigation_margin = 0x7f06006a
com.example.fragmentsleam:attr/chipStrokeColor = 0x7f0300d2
com.example.fragmentsleam:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f100308
com.example.fragmentsleam:styleable/MaterialButtonGroup = 0x7f110054
com.example.fragmentsleam:attr/popupTheme = 0x7f0303e0
com.example.fragmentsleam:integer/m3_card_anim_delay_ms = 0x7f09000c
com.example.fragmentsleam:color/material_dynamic_primary50 = 0x7f0502ed
com.example.fragmentsleam:attr/carousel_infinite = 0x7f0300aa
com.example.fragmentsleam:attr/textBackgroundPanY = 0x7f0304d5
com.example.fragmentsleam:color/material_dynamic_neutral_variant30 = 0x7f0502de
com.example.fragmentsleam:attr/initialActivityCount = 0x7f030285
com.example.fragmentsleam:dimen/m3_comp_filled_card_icon_size = 0x7f060166
com.example.fragmentsleam:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f100378
com.example.fragmentsleam:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0c0189
com.example.fragmentsleam:attr/badgeText = 0x7f030059
com.example.fragmentsleam:style/TextAppearance.Material3.TitleSmall = 0x7f10023a
com.example.fragmentsleam:dimen/mtrl_card_checked_icon_size = 0x7f060380
com.example.fragmentsleam:color/material_slider_halo_color = 0x7f050359
com.example.fragmentsleam:attr/backgroundInsetBottom = 0x7f030049
com.example.fragmentsleam:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0c00b2
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070011
com.example.fragmentsleam:attr/itemStrokeColor = 0x7f0302a4
com.example.fragmentsleam:attr/iconStartPadding = 0x7f03026f
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary70 = 0x7f05011b
com.example.fragmentsleam:color/material_dynamic_tertiary90 = 0x7f05030b
com.example.fragmentsleam:animator/design_fab_hide_motion_spec = 0x7f020003
com.example.fragmentsleam:attr/checkedTextViewStyle = 0x7f0300c1
com.example.fragmentsleam:color/material_dynamic_primary80 = 0x7f0502f0
com.example.fragmentsleam:attr/shapeCornerFamily = 0x7f030420
com.example.fragmentsleam:attr/drawableEndCompat = 0x7f0301a9
com.example.fragmentsleam:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1003c7
com.example.fragmentsleam:layout/abc_activity_chooser_view = 0x7f0b0006
com.example.fragmentsleam:styleable/FloatingToolbar = 0x7f110036
com.example.fragmentsleam:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0603c1
com.example.fragmentsleam:attr/motionPath = 0x7f03039b
com.example.fragmentsleam:styleable/SideSheetBehavior_Layout = 0x7f110082
com.example.fragmentsleam:attr/fabSize = 0x7f030203
com.example.fragmentsleam:dimen/m3_comp_icon_button_small_outlined_outline_width = 0x7f060183
com.example.fragmentsleam:attr/tooltipForegroundColor = 0x7f030522
com.example.fragmentsleam:anim/abc_slide_out_top = 0x7f010009
com.example.fragmentsleam:attr/borderlessButtonStyle = 0x7f030079
com.example.fragmentsleam:attr/checkedIconEnabled = 0x7f0300ba
com.example.fragmentsleam:attr/shapeAppearanceCornerExtraLargeIncreased = 0x7f030416
com.example.fragmentsleam:dimen/mtrl_progress_circular_inset = 0x7f0603b8
com.example.fragmentsleam:color/m3_sys_color_dark_on_secondary = 0x7f050204
com.example.fragmentsleam:id/tag_window_insets_animation_callback = 0x7f0801da
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary90 = 0x7f05011d
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f100293
com.example.fragmentsleam:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f100138
com.example.fragmentsleam:attr/layout_constraintLeft_toRightOf = 0x7f0302de
com.example.fragmentsleam:style/Platform.V21.AppCompat = 0x7f100149
com.example.fragmentsleam:attr/layout_constraintCircleAngle = 0x7f0302cc
com.example.fragmentsleam:id/dragRight = 0x7f0800a9
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500e1
com.example.fragmentsleam:style/Widget.Material3.ActionMode = 0x7f100389
com.example.fragmentsleam:style/Theme.MaterialComponents.CompactMenu = 0x7f10028e
com.example.fragmentsleam:attr/autoSizePresetSizes = 0x7f030042
com.example.fragmentsleam:attr/chipSpacing = 0x7f0300cd
com.example.fragmentsleam:attr/checkedChip = 0x7f0300b8
com.example.fragmentsleam:attr/windowFixedHeightMajor = 0x7f03056a
com.example.fragmentsleam:dimen/m3_extended_fab_end_padding = 0x7f060262
com.example.fragmentsleam:attr/actionBarTabBarStyle = 0x7f030008
com.example.fragmentsleam:attr/checkMarkTintMode = 0x7f0300b5
com.example.fragmentsleam:attr/altSrc = 0x7f030030
com.example.fragmentsleam:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1001df
com.example.fragmentsleam:attr/materialCalendarHeaderDivider = 0x7f030333
com.example.fragmentsleam:color/m3_sys_color_dark_error_container = 0x7f0501fb
com.example.fragmentsleam:attr/centerIfNoTextEnabled = 0x7f0300b0
com.example.fragmentsleam:drawable/abc_seekbar_track_material = 0x7f070065
com.example.fragmentsleam:dimen/m3_comp_fab_primary_container_container_elevation = 0x7f060158
com.example.fragmentsleam:macro/m3_comp_icon_button_xsmall_pressed_container_shape = 0x7f0c00cc
com.example.fragmentsleam:macro/m3_comp_dialog_container_shape = 0x7f0c0083
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_hovered_icon_color = 0x7f0c003e
com.example.fragmentsleam:color/design_default_color_surface = 0x7f05004b
com.example.fragmentsleam:attr/colorPrimaryDark = 0x7f030120
com.example.fragmentsleam:attr/carousel_firstView = 0x7f0300a8
com.example.fragmentsleam:dimen/m3_comp_slider_medium_active_handle_height = 0x7f06020c
com.example.fragmentsleam:dimen/m3_comp_extended_fab_small_leading_space = 0x7f060150
com.example.fragmentsleam:attr/moveWhenScrollAtTop = 0x7f0303a8
com.example.fragmentsleam:color/m3_ref_palette_dynamic_secondary20 = 0x7f050108
com.example.fragmentsleam:animator/fragment_fade_exit = 0x7f020008
com.example.fragmentsleam:attr/contentPaddingEnd = 0x7f03015f
com.example.fragmentsleam:attr/cardBackgroundColor = 0x7f03009d
com.example.fragmentsleam:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1003ff
com.example.fragmentsleam:id/startToEnd = 0x7f0801c3
com.example.fragmentsleam:dimen/mtrl_calendar_header_height_fullscreen = 0x7f060367
com.example.fragmentsleam:attr/popupMenuStyle = 0x7f0303df
com.example.fragmentsleam:dimen/m3_comp_app_bar_large_container_height = 0x7f060100
com.example.fragmentsleam:styleable/MaterialCheckBoxStates = 0x7f11005a
com.example.fragmentsleam:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f100307
com.example.fragmentsleam:attr/helperText = 0x7f030253
com.example.fragmentsleam:attr/layout_constraintGuide_end = 0x7f0302d2
com.example.fragmentsleam:dimen/m3_navigation_rail_icon_size = 0x7f060294
com.example.fragmentsleam:attr/textAppearanceBodySmallEmphasized = 0x7f0304a6
com.example.fragmentsleam:color/mtrl_filled_background_color = 0x7f050379
com.example.fragmentsleam:attr/clockHandColor = 0x7f0300e0
com.example.fragmentsleam:id/accessibility_custom_action_17 = 0x7f08001a
com.example.fragmentsleam:drawable/mtrl_ic_indeterminate = 0x7f0700ca
com.example.fragmentsleam:attr/closeIconTint = 0x7f0300e8
com.example.fragmentsleam:macro/m3_comp_button_outlined_selected_icon_color = 0x7f0c0041
com.example.fragmentsleam:attr/startIconContentDescription = 0x7f030451
com.example.fragmentsleam:attr/contentMarginTop = 0x7f03015c
com.example.fragmentsleam:attr/behavior_expandedOffset = 0x7f03006c
com.example.fragmentsleam:id/toggle = 0x7f0801f0
com.example.fragmentsleam:attr/editTextBackground = 0x7f0301b9
com.example.fragmentsleam:animator/m3_card_elevated_state_list_anim = 0x7f02000e
com.example.fragmentsleam:id/open_search_view_divider = 0x7f080162
com.example.fragmentsleam:id/below = 0x7f080061
com.example.fragmentsleam:color/m3_ref_palette_error98 = 0x7f05012d
com.example.fragmentsleam:attr/actionMenuTextAppearance = 0x7f030010
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f1001ae
com.example.fragmentsleam:attr/strokeColor = 0x7f030466
com.example.fragmentsleam:dimen/m3_comp_icon_button_xsmall_default_leading_space = 0x7f06018e
com.example.fragmentsleam:attr/borderRound = 0x7f030076
com.example.fragmentsleam:id/startHorizontal = 0x7f0801c2
com.example.fragmentsleam:dimen/m3_toolbar_text_size_title = 0x7f060304
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f10003d
com.example.fragmentsleam:attr/behavior_overlapTop = 0x7f030070
com.example.fragmentsleam:string/mtrl_picker_invalid_range = 0x7f0f007e
com.example.fragmentsleam:dimen/m3_comp_button_small_outlined_outline_width = 0x7f060125
com.example.fragmentsleam:attr/colorSurfaceContainerHighest = 0x7f03012f
com.example.fragmentsleam:color/m3_ref_palette_cyan30 = 0x7f0500be
com.example.fragmentsleam:string/abc_menu_alt_shortcut_label = 0x7f0f0008
com.example.fragmentsleam:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0c0153
com.example.fragmentsleam:attr/containerInsetBottom = 0x7f030147
com.example.fragmentsleam:attr/textAppearanceBodyMedium = 0x7f0304a3
com.example.fragmentsleam:dimen/m3_comp_loading_indicator_container_width = 0x7f06019c
com.example.fragmentsleam:color/abc_tint_seek_thumb = 0x7f050016
com.example.fragmentsleam:attr/behavior_saveFlags = 0x7f030072
com.example.fragmentsleam:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f10012b
com.example.fragmentsleam:id/material_minute_text_input = 0x7f080119
com.example.fragmentsleam:color/m3_sys_color_dark_secondary = 0x7f05020e
com.example.fragmentsleam:attr/titleCollapseMode = 0x7f03050f
com.example.fragmentsleam:dimen/mtrl_textinput_start_icon_margin_end = 0x7f0603e8
com.example.fragmentsleam:styleable/BottomSheetBehavior_Layout = 0x7f110017
com.example.fragmentsleam:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0c0124
com.example.fragmentsleam:attr/cardForegroundColor = 0x7f0300a0
com.example.fragmentsleam:attr/colorPrimarySurface = 0x7f030124
com.example.fragmentsleam:dimen/material_emphasis_disabled = 0x7f060317
com.example.fragmentsleam:dimen/m3_bottomappbar_height = 0x7f0600ce
com.example.fragmentsleam:string/appbar_scrolling_view_behavior = 0x7f0f001d
com.example.fragmentsleam:color/material_personalized_color_error_container = 0x7f05032a
com.example.fragmentsleam:dimen/m3_badge_horizontal_offset = 0x7f0600b7
com.example.fragmentsleam:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f0601c7
com.example.fragmentsleam:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f10008e
com.example.fragmentsleam:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0c0139
com.example.fragmentsleam:attr/customFloatValue = 0x7f030183
com.example.fragmentsleam:color/mtrl_btn_bg_color_selector = 0x7f050362
com.example.fragmentsleam:dimen/m3_comp_icon_button_xsmall_wide_leading_space = 0x7f060194
com.example.fragmentsleam:color/m3_ref_palette_orange0 = 0x7f05017c
com.example.fragmentsleam:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.example.fragmentsleam:dimen/m3_fab_translation_z_base = 0x7f06026f
com.example.fragmentsleam:id/line3 = 0x7f080102
com.example.fragmentsleam:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f0601e8
com.example.fragmentsleam:attr/textAppearanceSubtitle1 = 0x7f0304cb
com.example.fragmentsleam:attr/colorSecondary = 0x7f030126
com.example.fragmentsleam:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1001d3
com.example.fragmentsleam:attr/drawableStartCompat = 0x7f0301ad
com.example.fragmentsleam:attr/buttonGravity = 0x7f030092
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f100494
com.example.fragmentsleam:attr/badgeWithTextWidth = 0x7f030063
com.example.fragmentsleam:dimen/m3_comp_app_bar_on_scroll_container_elevation = 0x7f060104
com.example.fragmentsleam:attr/motionInterpolator = 0x7f03039a
com.example.fragmentsleam:attr/animateMenuItems = 0x7f030032
com.example.fragmentsleam:attr/motionDurationMedium3 = 0x7f030380
com.example.fragmentsleam:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f10029b
com.example.fragmentsleam:attr/ratingBarStyle = 0x7f0303f1
com.example.fragmentsleam:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.example.fragmentsleam:dimen/mtrl_calendar_navigation_top_padding = 0x7f060372
com.example.fragmentsleam:dimen/m3_btn_padding_left = 0x7f0600df
com.example.fragmentsleam:attr/isMaterialTheme = 0x7f03028b
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0500f4
com.example.fragmentsleam:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1000ca
com.example.fragmentsleam:attr/motionDurationShort2 = 0x7f030383
com.example.fragmentsleam:attr/badgeTextColor = 0x7f03005b
com.example.fragmentsleam:color/m3_slider_active_track_color_legacy = 0x7f0501ec
com.example.fragmentsleam:attr/navigationRailStyle = 0x7f0303ae
com.example.fragmentsleam:color/material_personalized_color_secondary_container = 0x7f05033f
com.example.fragmentsleam:color/m3_sys_color_dark_on_background = 0x7f0501ff
com.example.fragmentsleam:attr/isMaterial3Theme = 0x7f03028a
com.example.fragmentsleam:id/add = 0x7f080047
com.example.fragmentsleam:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f0601e3
com.example.fragmentsleam:string/mtrl_button_collapsed_content_description = 0x7f0f005f
com.example.fragmentsleam:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0c00f9
com.example.fragmentsleam:dimen/material_emphasis_medium = 0x7f06031a
com.example.fragmentsleam:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060204
com.example.fragmentsleam:attr/containerWidth = 0x7f030153
com.example.fragmentsleam:id/actionUp = 0x7f080033
com.example.fragmentsleam:attr/barLength = 0x7f030064
com.example.fragmentsleam:dimen/m3_comp_slider_large_active_handle_height = 0x7f060208
com.example.fragmentsleam:dimen/m3_sys_shape_corner_value_large_increased = 0x7f0602fa
com.example.fragmentsleam:id/visible = 0x7f08020e
com.example.fragmentsleam:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f060398
com.example.fragmentsleam:attr/itemTextColor = 0x7f0302aa
com.example.fragmentsleam:id/callMeasure = 0x7f08006b
com.example.fragmentsleam:attr/attributeName = 0x7f03003b
com.example.fragmentsleam:attr/arcMode = 0x7f030038
com.example.fragmentsleam:id/easeIn = 0x7f0800ad
com.example.fragmentsleam:attr/closeItemLayout = 0x7f0300ea
com.example.fragmentsleam:animator/m3_split_button_chevron_rotation = 0x7f02001a
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.Toolbar.Docked.Container.Shape = 0x7f100185
com.example.fragmentsleam:attr/textAppearanceSearchResultSubtitle = 0x7f0304c8
com.example.fragmentsleam:attr/maxButtonHeight = 0x7f03035c
com.example.fragmentsleam:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f10016d
com.example.fragmentsleam:id/x_right = 0x7f080218
com.example.fragmentsleam:style/Widget.Material3.MaterialButtonGroup = 0x7f1003f9
com.example.fragmentsleam:dimen/m3_comp_fab_primary_container_hovered_container_elevation = 0x7f06015b
com.example.fragmentsleam:anim/m3_motion_fade_exit = 0x7f010024
com.example.fragmentsleam:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
com.example.fragmentsleam:attr/itemActiveIndicatorStyle = 0x7f03028c
com.example.fragmentsleam:animator/mtrl_card_state_list_anim = 0x7f02001d
com.example.fragmentsleam:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f100106
com.example.fragmentsleam:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1000cc
com.example.fragmentsleam:attr/alpha = 0x7f03002e
com.example.fragmentsleam:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f090041
com.example.fragmentsleam:color/m3_sys_color_dark_surface = 0x7f050210
com.example.fragmentsleam:attr/actionViewClass = 0x7f030025
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0c007f
com.example.fragmentsleam:color/m3_ref_palette_orange40 = 0x7f050181
com.example.fragmentsleam:attr/actionModeCloseContentDescription = 0x7f030014
com.example.fragmentsleam:color/abc_secondary_text_material_dark = 0x7f050011
com.example.fragmentsleam:attr/layout_constraintWidth_max = 0x7f0302ed
com.example.fragmentsleam:integer/m3_sys_motion_duration_medium3 = 0x7f090019
com.example.fragmentsleam:attr/badgeWidth = 0x7f03005e
com.example.fragmentsleam:id/tag_accessibility_clickable_spans = 0x7f0801cf
com.example.fragmentsleam:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1001d5
com.example.fragmentsleam:dimen/m3_bottom_nav_min_height = 0x7f0600c6
com.example.fragmentsleam:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.example.fragmentsleam:style/Widget.MaterialComponents.Chip.Choice = 0x7f100474
com.example.fragmentsleam:attr/checkedButton = 0x7f0300b7
com.example.fragmentsleam:color/m3_ref_palette_error100 = 0x7f050123
com.example.fragmentsleam:attr/textAppearanceBodyLarge = 0x7f0304a1
com.example.fragmentsleam:attr/dropDownBackgroundTint = 0x7f0301b4
com.example.fragmentsleam:color/m3_sys_color_tertiary_fixed = 0x7f050297
com.example.fragmentsleam:animator/fragment_close_enter = 0x7f020005
com.example.fragmentsleam:style/ShapeAppearance.MaterialComponents.Badge = 0x7f1001a2
com.example.fragmentsleam:color/m3_sys_color_dynamic_dark_primary_container = 0x7f05022f
com.example.fragmentsleam:attr/actionModeCloseDrawable = 0x7f030015
com.example.fragmentsleam:style/Base.V24.Theme.Material3.Dark = 0x7f1000af
com.example.fragmentsleam:attr/triggerId = 0x7f030548
com.example.fragmentsleam:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.example.fragmentsleam:attr/tabSelectedTextAppearance = 0x7f030494
com.example.fragmentsleam:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f07001a
com.example.fragmentsleam:color/m3_card_stroke_color = 0x7f05006d
com.example.fragmentsleam:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f060203
com.example.fragmentsleam:string/hello_blank_fragment = 0x7f0f0033
com.example.fragmentsleam:dimen/m3_navigation_item_horizontal_padding = 0x7f060283
com.example.fragmentsleam:color/material_grey_850 = 0x7f050313
com.example.fragmentsleam:dimen/m3_comp_bottom_app_bar_container_height = 0x7f06010e
com.example.fragmentsleam:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f0601c2
com.example.fragmentsleam:attr/actionBarTabStyle = 0x7f030009
com.example.fragmentsleam:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1000e9
com.example.fragmentsleam:attr/textInputFilledExposedDropdownMenuStyle = 0x7f0304de
com.example.fragmentsleam:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0c00ab
com.example.fragmentsleam:color/m3_ref_palette_secondary20 = 0x7f0501c1
com.example.fragmentsleam:attr/backgroundStacked = 0x7f03004f
com.example.fragmentsleam:string/mtrl_timepicker_confirm = 0x7f0f009f
com.example.fragmentsleam:style/Base.V7.Theme.AppCompat.Light = 0x7f1000ba
com.example.fragmentsleam:animator/chevron_unchecked_checked = 0x7f020001
com.example.fragmentsleam:anim/m3_side_sheet_exit_to_right = 0x7f010028
com.example.fragmentsleam:attr/layout_constraintHeight_max = 0x7f0302d6
com.example.fragmentsleam:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0c01cf
com.example.fragmentsleam:color/m3_sys_color_light_background = 0x7f05026b
com.example.fragmentsleam:string/abc_menu_shift_shortcut_label = 0x7f0f000e
com.example.fragmentsleam:styleable/ClockFaceView = 0x7f110020
com.example.fragmentsleam:style/Widget.AppCompat.ActionBar.TabText = 0x7f100335
com.example.fragmentsleam:attr/tickColor = 0x7f030500
com.example.fragmentsleam:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f100153
com.example.fragmentsleam:attr/expandedTitleMargin = 0x7f0301e7
com.example.fragmentsleam:dimen/m3_alert_dialog_icon_margin = 0x7f0600a3
com.example.fragmentsleam:style/Widget.MaterialComponents.Button.TextButton = 0x7f100469
com.example.fragmentsleam:animator/design_appbar_state_list_animator = 0x7f020002
com.example.fragmentsleam:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1000bf
com.example.fragmentsleam:dimen/m3_navigation_subheader_horizontal_padding = 0x7f0602a0
com.example.fragmentsleam:style/Widget.Material3.Button.TextButton = 0x7f1003a4
com.example.fragmentsleam:attr/materialTimePickerStyle = 0x7f030357
com.example.fragmentsleam:attr/lastItemDecorated = 0x7f0302b8
com.example.fragmentsleam:macro/m3_comp_date_picker_modal_container_shape = 0x7f0c006e
com.example.fragmentsleam:color/m3_ref_palette_cyan40 = 0x7f0500bf
com.example.fragmentsleam:attr/titleTextColor = 0x7f03051a
com.example.fragmentsleam:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.example.fragmentsleam:attr/flow_lastHorizontalStyle = 0x7f030225
com.example.fragmentsleam:color/material_on_primary_emphasis_medium = 0x7f05031e
com.example.fragmentsleam:attr/alertDialogStyle = 0x7f03002b
com.example.fragmentsleam:attr/state_above_anchor = 0x7f030459
com.example.fragmentsleam:attr/itemVerticalPadding = 0x7f0302ab
com.example.fragmentsleam:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000c
com.example.fragmentsleam:attr/motionEffect_end = 0x7f030393
com.example.fragmentsleam:dimen/m3_comp_button_outlined_pressed_state_layer_opacity = 0x7f060121
com.example.fragmentsleam:attr/barrierDirection = 0x7f030066
com.example.fragmentsleam:attr/tickRadiusInactive = 0x7f030507
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_short_container_height = 0x7f0601b8
com.example.fragmentsleam:animator/fragment_open_enter = 0x7f020009
com.example.fragmentsleam:drawable/m3_tabs_transparent_background = 0x7f0700aa
com.example.fragmentsleam:attr/colorOnTertiaryFixed = 0x7f03011a
com.example.fragmentsleam:dimen/mtrl_btn_elevation = 0x7f060342
com.example.fragmentsleam:dimen/m3_comp_slider_xlarge_active_track_height = 0x7f060216
com.example.fragmentsleam:style/Widget.Material3.NavigationView = 0x7f100422
com.example.fragmentsleam:dimen/mtrl_chip_text_size = 0x7f060386
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f100356
com.example.fragmentsleam:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f100352
com.example.fragmentsleam:attr/alertDialogTheme = 0x7f03002c
com.example.fragmentsleam:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f0601f6
com.example.fragmentsleam:attr/materialButtonElevatedStyle = 0x7f030328
com.example.fragmentsleam:id/scrollable = 0x7f080198
com.example.fragmentsleam:attr/errorAccessibilityLiveRegion = 0x7f0301cf
com.example.fragmentsleam:attr/transitionDisable = 0x7f030543
com.example.fragmentsleam:anim/design_snackbar_out = 0x7f01001b
com.example.fragmentsleam:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0500e5
com.example.fragmentsleam:attr/layout_constraintRight_toLeftOf = 0x7f0302e0
com.example.fragmentsleam:id/anticipate = 0x7f080051
com.example.fragmentsleam:attr/subtitle = 0x7f03046f
com.example.fragmentsleam:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f060250
com.example.fragmentsleam:color/m3_standard_toolbar_button_text_color_selector = 0x7f0501f3
com.example.fragmentsleam:color/design_default_color_on_error = 0x7f050042
com.example.fragmentsleam:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f100498
com.example.fragmentsleam:attr/cornerSizeTopRight = 0x7f030173
com.example.fragmentsleam:dimen/m3_comp_nav_rail_item_icon_size = 0x7f0601b7
com.example.fragmentsleam:color/material_personalized_color_secondary_text_inverse = 0x7f050341
com.example.fragmentsleam:color/design_fab_shadow_mid_color = 0x7f05004e
com.example.fragmentsleam:color/secondary_text_default_material_light = 0x7f0503a6
com.example.fragmentsleam:attr/elevationOverlayAccentColor = 0x7f0301bd
com.example.fragmentsleam:anim/m3_motion_fade_enter = 0x7f010023
com.example.fragmentsleam:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f0601e9
com.example.fragmentsleam:attr/expandedItemMinHeight = 0x7f0301e0
com.example.fragmentsleam:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0603d6
com.example.fragmentsleam:attr/shapeAppearanceOverlay = 0x7f03041e
com.example.fragmentsleam:attr/labelBehavior = 0x7f0302b0
com.example.fragmentsleam:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1001b8
com.example.fragmentsleam:style/TextAppearance.MaterialComponents.Body1 = 0x7f10023d
com.example.fragmentsleam:attr/materialAlertDialogBodyTextStyle = 0x7f030322
com.example.fragmentsleam:id/parent = 0x7f080172
com.example.fragmentsleam:attr/gestureInsetBottomIgnored = 0x7f030240
com.example.fragmentsleam:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f1001ad
com.example.fragmentsleam:attr/errorTextColor = 0x7f0301d7
com.example.fragmentsleam:attr/haloRadius = 0x7f03024f
com.example.fragmentsleam:attr/layout_scrollEffect = 0x7f0302fe
com.example.fragmentsleam:color/m3_ref_palette_neutral_variant95 = 0x7f050179
com.example.fragmentsleam:style/Animation.Design.BottomSheetDialog = 0x7f100005
com.example.fragmentsleam:attr/behavior_draggableOnNestedScroll = 0x7f03006b
com.example.fragmentsleam:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0c01a8
com.example.fragmentsleam:attr/chipStyle = 0x7f0300d4
com.example.fragmentsleam:color/dim_foreground_disabled_material_light = 0x7f050057
com.example.fragmentsleam:dimen/m3_comp_icon_button_xlarge_default_trailing_space = 0x7f060187
com.example.fragmentsleam:color/m3_ref_palette_dynamic_tertiary20 = 0x7f050116
com.example.fragmentsleam:dimen/m3_comp_icon_button_medium_icon_size = 0x7f060178
com.example.fragmentsleam:color/material_blue_grey_800 = 0x7f0502be
com.example.fragmentsleam:style/TextAppearance.AppCompat.Title = 0x7f1001d4
com.example.fragmentsleam:dimen/mtrl_progress_track_thickness = 0x7f0603c5
com.example.fragmentsleam:anim/m3_bottom_sheet_slide_out = 0x7f010022
com.example.fragmentsleam:style/ThemeOverlay.Material3.Light = 0x7f1002f1
com.example.fragmentsleam:attr/carousel_touchUp_velocityThreshold = 0x7f0300af
com.example.fragmentsleam:id/open_search_view_status_bar_spacer = 0x7f080169
com.example.fragmentsleam:attr/layout_constraintBaseline_toBottomOf = 0x7f0302c6
com.example.fragmentsleam:attr/state_indeterminate = 0x7f03045e
com.example.fragmentsleam:dimen/m3_carousel_gone_size = 0x7f0600f4
com.example.fragmentsleam:dimen/m3_btn_disabled_elevation = 0x7f0600d2
com.example.fragmentsleam:string/abc_capital_off = 0x7f0f0006
com.example.fragmentsleam:macro/m3_comp_toolbar_vibrant_vibrant_selected_label_text_color = 0x7f0c0206
com.example.fragmentsleam:attr/subtitleTextColor = 0x7f030473
com.example.fragmentsleam:attr/tabIconTint = 0x7f030480
com.example.fragmentsleam:attr/errorShown = 0x7f0301d5
com.example.fragmentsleam:id/ratio = 0x7f080183
com.example.fragmentsleam:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.example.fragmentsleam:color/m3_ref_palette_secondary90 = 0x7f0501c8
com.example.fragmentsleam:attr/backgroundInsetStart = 0x7f03004b
com.example.fragmentsleam:id/bounceBoth = 0x7f080066
com.example.fragmentsleam:color/mtrl_fab_bg_color_selector = 0x7f050376
com.example.fragmentsleam:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f100037
com.example.fragmentsleam:attr/colorControlActivated = 0x7f030101
