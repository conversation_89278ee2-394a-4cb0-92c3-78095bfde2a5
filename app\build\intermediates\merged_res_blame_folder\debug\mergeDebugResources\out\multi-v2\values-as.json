{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,9774", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,9852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3487,3590,3698,3803,3907,4007,10088", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3482,3585,3693,3798,3902,4002,4131,10184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,593,695,818,897,957,1022,1111,1176,1235,1312,1398,1462,1526,1589,1659,1723,1787,1855,1911,1965,2070,2128,2190,2244,2316,2433,2520,2596,2688,2770,2853,2993,3070,3151,3278,3369,3446,3500,3551,3617,3687,3764,3835,3910,3981,4058,4127,4196,4303,4394,4466,4555,4644,4718,4790,4876,4926,5005,5071,5151,5235,5297,5361,5424,5493,5593,5681,5773,5858,5949,6037,6095,6150,6231,6312,6387", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,81,77,76,83,101,122,78,59,64,88,64,58,76,85,63,63,62,69,63,63,67,55,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,87,91,84,90,87,57,54,80,80,74,74", "endOffsets": "267,349,427,504,588,690,813,892,952,1017,1106,1171,1230,1307,1393,1457,1521,1584,1654,1718,1782,1850,1906,1960,2065,2123,2185,2239,2311,2428,2515,2591,2683,2765,2848,2988,3065,3146,3273,3364,3441,3495,3546,3612,3682,3759,3830,3905,3976,4053,4122,4191,4298,4389,4461,4550,4639,4713,4785,4871,4921,5000,5066,5146,5230,5292,5356,5419,5488,5588,5676,5768,5853,5944,6032,6090,6145,6226,6307,6382,6457"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3065,3147,3225,3302,4136,4238,4361,4440,4500,4565,4654,4719,4778,4855,4941,5005,5069,5132,5202,5266,5330,5398,5454,5508,5613,5671,5733,5787,5859,5976,6063,6139,6231,6313,6396,6536,6613,6694,6821,6912,6989,7043,7094,7160,7230,7307,7378,7453,7524,7601,7670,7739,7846,7937,8009,8098,8187,8261,8333,8419,8469,8548,8614,8694,8778,8840,8904,8967,9036,9136,9224,9316,9401,9492,9580,9638,9693,9857,9938,10013", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,81,77,76,83,101,122,78,59,64,88,64,58,76,85,63,63,62,69,63,63,67,55,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,87,91,84,90,87,57,54,80,80,74,74", "endOffsets": "317,3142,3220,3297,3381,4233,4356,4435,4495,4560,4649,4714,4773,4850,4936,5000,5064,5127,5197,5261,5325,5393,5449,5503,5608,5666,5728,5782,5854,5971,6058,6134,6226,6308,6391,6531,6608,6689,6816,6907,6984,7038,7089,7155,7225,7302,7373,7448,7519,7596,7665,7734,7841,7932,8004,8093,8182,8256,8328,8414,8464,8543,8609,8689,8773,8835,8899,8962,9031,9131,9219,9311,9396,9487,9575,9633,9688,9769,9933,10008,10083"}}]}]}