{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3380,3482,3584,3684,3784,3891,3995,10264", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3477,3579,3679,3779,3886,3990,4109,10360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,9950", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,10028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,608,705,827,908,968,1032,1121,1200,1263,1338,1431,1493,1559,1617,1690,1754,1821,1890,1947,2003,2125,2182,2244,2300,2376,2510,2595,2674,2772,2858,2944,3082,3163,3242,3366,3456,3533,3590,3641,3707,3785,3868,3939,4015,4090,4169,4242,4313,4422,4516,4594,4683,4773,4847,4928,5017,5070,5149,5216,5297,5381,5443,5507,5570,5641,5749,5847,5949,6046,6148,6247,6308,6363,6444,6527,6603", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,85,81,76,93,96,121,80,59,63,88,78,62,74,92,61,65,57,72,63,66,68,56,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,88,52,78,66,80,83,61,63,62,70,107,97,101,96,101,98,60,54,80,82,75,71", "endOffsets": "264,350,432,509,603,700,822,903,963,1027,1116,1195,1258,1333,1426,1488,1554,1612,1685,1749,1816,1885,1942,1998,2120,2177,2239,2295,2371,2505,2590,2669,2767,2853,2939,3077,3158,3237,3361,3451,3528,3585,3636,3702,3780,3863,3934,4010,4085,4164,4237,4308,4417,4511,4589,4678,4768,4842,4923,5012,5065,5144,5211,5292,5376,5438,5502,5565,5636,5744,5842,5944,6041,6143,6242,6303,6358,6439,6522,6598,6670"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,4114,4211,4333,4414,4474,4538,4627,4706,4769,4844,4937,4999,5065,5123,5196,5260,5327,5396,5453,5509,5631,5688,5750,5806,5882,6016,6101,6180,6278,6364,6450,6588,6669,6748,6872,6962,7039,7096,7147,7213,7291,7374,7445,7521,7596,7675,7748,7819,7928,8022,8100,8189,8279,8353,8434,8523,8576,8655,8722,8803,8887,8949,9013,9076,9147,9255,9353,9455,9552,9654,9753,9814,9869,10033,10116,10192", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,85,81,76,93,96,121,80,59,63,88,78,62,74,92,61,65,57,72,63,66,68,56,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,88,52,78,66,80,83,61,63,62,70,107,97,101,96,101,98,60,54,80,82,75,71", "endOffsets": "314,3122,3204,3281,3375,4206,4328,4409,4469,4533,4622,4701,4764,4839,4932,4994,5060,5118,5191,5255,5322,5391,5448,5504,5626,5683,5745,5801,5877,6011,6096,6175,6273,6359,6445,6583,6664,6743,6867,6957,7034,7091,7142,7208,7286,7369,7440,7516,7591,7670,7743,7814,7923,8017,8095,8184,8274,8348,8429,8518,8571,8650,8717,8798,8882,8944,9008,9071,9142,9250,9348,9450,9547,9649,9748,9809,9864,9945,10111,10187,10259"}}]}]}