{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "38,39,40,41,42,43,44,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3442,3540,3642,3740,3844,3948,4050,10347", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3535,3637,3735,3839,3943,4045,4162,10443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2520,2625,2739,2842,3011,10021", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2515,2620,2734,2837,3006,3102,10103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,662,758,884,965,1027,1093,1185,1262,1325,1405,1513,1573,1639,1695,1766,1826,1893,1965,2026,2080,2199,2256,2318,2372,2447,2571,2659,2736,2830,2914,2997,3142,3227,3313,3446,3534,3612,3666,3720,3786,3860,3938,4009,4091,4163,4240,4313,4383,4492,4585,4657,4749,4845,4919,4995,5091,5144,5226,5293,5380,5467,5529,5593,5656,5725,5833,5930,6031,6126,6224,6320,6378,6436,6516,6602,6678", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,78,79,81,93,95,125,80,61,65,91,76,62,79,107,59,65,55,70,59,66,71,60,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,96,100,94,97,95,57,57,79,85,75,76", "endOffsets": "322,401,481,563,657,753,879,960,1022,1088,1180,1257,1320,1400,1508,1568,1634,1690,1761,1821,1888,1960,2021,2075,2194,2251,2313,2367,2442,2566,2654,2731,2825,2909,2992,3137,3222,3308,3441,3529,3607,3661,3715,3781,3855,3933,4004,4086,4158,4235,4308,4378,4487,4580,4652,4744,4840,4914,4990,5086,5139,5221,5288,5375,5462,5524,5588,5651,5720,5828,5925,6026,6121,6219,6315,6373,6431,6511,6597,6673,6750"}, "to": {"startLines": "2,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3107,3186,3266,3348,4167,4263,4389,4470,4532,4598,4690,4767,4830,4910,5018,5078,5144,5200,5271,5331,5398,5470,5531,5585,5704,5761,5823,5877,5952,6076,6164,6241,6335,6419,6502,6647,6732,6818,6951,7039,7117,7171,7225,7291,7365,7443,7514,7596,7668,7745,7818,7888,7997,8090,8162,8254,8350,8424,8500,8596,8649,8731,8798,8885,8972,9034,9098,9161,9230,9338,9435,9536,9631,9729,9825,9883,9941,10108,10194,10270", "endLines": "6,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "endColumns": "12,78,79,81,93,95,125,80,61,65,91,76,62,79,107,59,65,55,70,59,66,71,60,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,96,100,94,97,95,57,57,79,85,75,76", "endOffsets": "372,3181,3261,3343,3437,4258,4384,4465,4527,4593,4685,4762,4825,4905,5013,5073,5139,5195,5266,5326,5393,5465,5526,5580,5699,5756,5818,5872,5947,6071,6159,6236,6330,6414,6497,6642,6727,6813,6946,7034,7112,7166,7220,7286,7360,7438,7509,7591,7663,7740,7813,7883,7992,8085,8157,8249,8345,8419,8495,8591,8644,8726,8793,8880,8967,9029,9093,9156,9225,9333,9430,9531,9626,9724,9820,9878,9936,10016,10189,10265,10342"}}]}]}