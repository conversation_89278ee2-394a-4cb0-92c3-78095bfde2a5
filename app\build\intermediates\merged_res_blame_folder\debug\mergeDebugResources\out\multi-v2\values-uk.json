{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1135,1217,1308,1401,1496,1590,1690,1783,1878,1973,2064,2155,2254,2360,2466,2564,2671,2778,2883,3053,10068", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1130,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,3148,10145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,709,805,921,1004,1076,1143,1234,1300,1363,1445,1533,1595,1662,1720,1791,1850,1921,1995,2054,2108,2222,2282,2345,2399,2472,2591,2677,2753,2844,2925,3008,3147,3232,3319,3453,3541,3619,3676,3727,3793,3865,3941,4012,4095,4168,4245,4327,4401,4510,4600,4679,4770,4866,4940,5021,5116,5170,5252,5318,5405,5491,5553,5617,5680,5753,5860,5962,6060,6158,6263,6364,6425,6480,6562,6647,6723", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,77,77,87,90,95,115,82,71,66,90,65,62,81,87,61,66,57,70,58,70,73,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,133,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,101,97,97,104,100,60,54,81,84,75,76", "endOffsets": "369,447,525,613,704,800,916,999,1071,1138,1229,1295,1358,1440,1528,1590,1657,1715,1786,1845,1916,1990,2049,2103,2217,2277,2340,2394,2467,2586,2672,2748,2839,2920,3003,3142,3227,3314,3448,3536,3614,3671,3722,3788,3860,3936,4007,4090,4163,4240,4322,4396,4505,4595,4674,4765,4861,4935,5016,5111,5165,5247,5313,5400,5486,5548,5612,5675,5748,5855,5957,6055,6153,6258,6359,6420,6475,6557,6642,6718,6795"}, "to": {"startLines": "2,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3153,3231,3309,3397,4215,4311,4427,4510,4582,4649,4740,4806,4869,4951,5039,5101,5168,5226,5297,5356,5427,5501,5560,5614,5728,5788,5851,5905,5978,6097,6183,6259,6350,6431,6514,6653,6738,6825,6959,7047,7125,7182,7233,7299,7371,7447,7518,7601,7674,7751,7833,7907,8016,8106,8185,8276,8372,8446,8527,8622,8676,8758,8824,8911,8997,9059,9123,9186,9259,9366,9468,9566,9664,9769,9870,9931,9986,10150,10235,10311", "endLines": "7,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "endColumns": "12,77,77,87,90,95,115,82,71,66,90,65,62,81,87,61,66,57,70,58,70,73,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,133,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,101,97,97,104,100,60,54,81,84,75,76", "endOffsets": "419,3226,3304,3392,3483,4306,4422,4505,4577,4644,4735,4801,4864,4946,5034,5096,5163,5221,5292,5351,5422,5496,5555,5609,5723,5783,5846,5900,5973,6092,6178,6254,6345,6426,6509,6648,6733,6820,6954,7042,7120,7177,7228,7294,7366,7442,7513,7596,7669,7746,7828,7902,8011,8101,8180,8271,8367,8441,8522,8617,8671,8753,8819,8906,8992,9054,9118,9181,9254,9361,9463,9561,9659,9764,9865,9926,9981,10063,10230,10306,10383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3588,3690,3791,3892,3997,4102,10388", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3583,3685,3786,3887,3992,4097,4210,10484"}}]}]}