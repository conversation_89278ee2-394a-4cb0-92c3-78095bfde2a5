{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,529,636,762,840,916,1007,1100,1195,1289,1389,1482,1577,1671,1762,1853,1935,2051,2161,2260,2373,2478,2592,2756,2856", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,524,631,757,835,911,1002,1095,1190,1284,1384,1477,1572,1666,1757,1848,1930,2046,2156,2255,2368,2473,2587,2751,2851,2934"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "327,441,553,666,751,858,984,1062,1138,1229,1322,1417,1511,1611,1704,1799,1893,1984,2075,2157,2273,2383,2482,2595,2700,2814,2978,10053", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "436,548,661,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,3073,10131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3422,3520,3623,3724,3830,3931,4039,10381", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3515,3618,3719,3825,3926,4034,4162,10477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,277,361,444,526,621,728,841,926,983,1046,1140,1206,1268,1352,1455,1521,1592,1651,1727,1792,1860,1932,1988,2042,2155,2213,2274,2328,2407,2523,2609,2692,2787,2873,2964,3106,3185,3264,3395,3483,3567,3624,3676,3742,3822,3912,3983,4062,4139,4216,4293,4362,4479,4578,4655,4748,4843,4917,4998,5094,5149,5233,5301,5387,5475,5538,5603,5666,5734,5839,5929,6024,6114,6212,6308,6369,6425,6507,6599,6678", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,83,82,81,94,106,112,84,56,62,93,65,61,83,102,65,70,58,75,64,67,71,55,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,130,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,54,83,67,85,87,62,64,62,67,104,89,94,89,97,95,60,55,81,91,78,73", "endOffsets": "272,356,439,521,616,723,836,921,978,1041,1135,1201,1263,1347,1450,1516,1587,1646,1722,1787,1855,1927,1983,2037,2150,2208,2269,2323,2402,2518,2604,2687,2782,2868,2959,3101,3180,3259,3390,3478,3562,3619,3671,3737,3817,3907,3978,4057,4134,4211,4288,4357,4474,4573,4650,4743,4838,4912,4993,5089,5144,5228,5296,5382,5470,5533,5598,5661,5729,5834,5924,6019,6109,6207,6303,6364,6420,6502,6594,6673,6747"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3078,3162,3245,3327,4167,4274,4387,4472,4529,4592,4686,4752,4814,4898,5001,5067,5138,5197,5273,5338,5406,5478,5534,5588,5701,5759,5820,5874,5953,6069,6155,6238,6333,6419,6510,6652,6731,6810,6941,7029,7113,7170,7222,7288,7368,7458,7529,7608,7685,7762,7839,7908,8025,8124,8201,8294,8389,8463,8544,8640,8695,8779,8847,8933,9021,9084,9149,9212,9280,9385,9475,9570,9660,9758,9854,9915,9971,10136,10228,10307", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,83,82,81,94,106,112,84,56,62,93,65,61,83,102,65,70,58,75,64,67,71,55,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,130,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,54,83,67,85,87,62,64,62,67,104,89,94,89,97,95,60,55,81,91,78,73", "endOffsets": "322,3157,3240,3322,3417,4269,4382,4467,4524,4587,4681,4747,4809,4893,4996,5062,5133,5192,5268,5333,5401,5473,5529,5583,5696,5754,5815,5869,5948,6064,6150,6233,6328,6414,6505,6647,6726,6805,6936,7024,7108,7165,7217,7283,7363,7453,7524,7603,7680,7757,7834,7903,8020,8119,8196,8289,8384,8458,8539,8635,8690,8774,8842,8928,9016,9079,9144,9207,9275,9380,9470,9565,9655,9753,9849,9910,9966,10048,10223,10302,10376"}}]}]}