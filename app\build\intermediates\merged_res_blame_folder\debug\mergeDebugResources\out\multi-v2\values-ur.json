{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,9855", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,9936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,582,682,801,884,940,1005,1099,1169,1228,1311,1401,1465,1534,1592,1661,1721,1790,1861,1915,1979,2091,2150,2209,2264,2339,2462,2542,2625,2719,2806,2890,3023,3105,3186,3317,3404,3486,3544,3600,3666,3741,3821,3892,3971,4038,4113,4190,4254,4361,4455,4525,4614,4707,4781,4856,4946,5002,5081,5148,5232,5316,5378,5442,5505,5571,5671,5763,5857,5947,6052,6155,6217,6277,6357,6442,6523", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,77,77,77,88,99,118,82,55,64,93,69,58,82,89,63,68,57,68,59,68,70,53,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,91,93,89,104,102,61,59,79,84,80,73", "endOffsets": "254,332,410,488,577,677,796,879,935,1000,1094,1164,1223,1306,1396,1460,1529,1587,1656,1716,1785,1856,1910,1974,2086,2145,2204,2259,2334,2457,2537,2620,2714,2801,2885,3018,3100,3181,3312,3399,3481,3539,3595,3661,3736,3816,3887,3966,4033,4108,4185,4249,4356,4450,4520,4609,4702,4776,4851,4941,4997,5076,5143,5227,5311,5373,5437,5500,5566,5666,5758,5852,5942,6047,6150,6212,6272,6352,6437,6518,6592"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3114,3192,3270,4080,4180,4299,4382,4438,4503,4597,4667,4726,4809,4899,4963,5032,5090,5159,5219,5288,5359,5413,5477,5589,5648,5707,5762,5837,5960,6040,6123,6217,6304,6388,6521,6603,6684,6815,6902,6984,7042,7098,7164,7239,7319,7390,7469,7536,7611,7688,7752,7859,7953,8023,8112,8205,8279,8354,8444,8500,8579,8646,8730,8814,8876,8940,9003,9069,9169,9261,9355,9445,9550,9653,9715,9775,9941,10026,10107", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,77,77,77,88,99,118,82,55,64,93,69,58,82,89,63,68,57,68,59,68,70,53,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,91,93,89,104,102,61,59,79,84,80,73", "endOffsets": "304,3109,3187,3265,3354,4175,4294,4377,4433,4498,4592,4662,4721,4804,4894,4958,5027,5085,5154,5214,5283,5354,5408,5472,5584,5643,5702,5757,5832,5955,6035,6118,6212,6299,6383,6516,6598,6679,6810,6897,6979,7037,7093,7159,7234,7314,7385,7464,7531,7606,7683,7747,7854,7948,8018,8107,8200,8274,8349,8439,8495,8574,8641,8725,8809,8871,8935,8998,9064,9164,9256,9350,9440,9545,9648,9710,9770,9850,10021,10102,10176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3359,3457,3559,3661,3765,3868,3966,10181", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "3452,3554,3656,3760,3863,3961,4075,10277"}}]}]}