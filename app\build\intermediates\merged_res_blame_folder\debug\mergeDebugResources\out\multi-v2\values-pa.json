{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3301,3399,3501,3604,3705,3807,3905,10066", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3394,3496,3599,3700,3802,3900,4029,10162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,589,697,809,892,948,1012,1104,1173,1232,1321,1406,1469,1531,1589,1653,1714,1779,1845,1900,1954,2068,2126,2186,2240,2310,2437,2518,2608,2707,2804,2883,3018,3094,3171,3300,3384,3465,3520,3575,3641,3710,3787,3858,3937,4005,4081,4151,4216,4318,4413,4486,4580,4673,4747,4816,4910,4966,5049,5116,5200,5288,5350,5414,5477,5544,5641,5734,5825,5916,6015,6112,6171,6230,6307,6392,6468", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,76,78,80,88,107,111,82,55,63,91,68,58,88,84,62,61,57,63,60,64,65,54,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,80,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,92,90,90,98,96,58,58,76,84,75,72", "endOffsets": "258,335,414,495,584,692,804,887,943,1007,1099,1168,1227,1316,1401,1464,1526,1584,1648,1709,1774,1840,1895,1949,2063,2121,2181,2235,2305,2432,2513,2603,2702,2799,2878,3013,3089,3166,3295,3379,3460,3515,3570,3636,3705,3782,3853,3932,4000,4076,4146,4211,4313,4408,4481,4575,4668,4742,4811,4905,4961,5044,5111,5195,5283,5345,5409,5472,5539,5636,5729,5820,5911,6010,6107,6166,6225,6302,6387,6463,6536"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2975,3052,3131,3212,4034,4142,4254,4337,4393,4457,4549,4618,4677,4766,4851,4914,4976,5034,5098,5159,5224,5290,5345,5399,5513,5571,5631,5685,5755,5882,5963,6053,6152,6249,6328,6463,6539,6616,6745,6829,6910,6965,7020,7086,7155,7232,7303,7382,7450,7526,7596,7661,7763,7858,7931,8025,8118,8192,8261,8355,8411,8494,8561,8645,8733,8795,8859,8922,8989,9086,9179,9270,9361,9460,9557,9616,9675,9832,9917,9993", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,76,78,80,88,107,111,82,55,63,91,68,58,88,84,62,61,57,63,60,64,65,54,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,80,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,92,90,90,98,96,58,58,76,84,75,72", "endOffsets": "308,3047,3126,3207,3296,4137,4249,4332,4388,4452,4544,4613,4672,4761,4846,4909,4971,5029,5093,5154,5219,5285,5340,5394,5508,5566,5626,5680,5750,5877,5958,6048,6147,6244,6323,6458,6534,6611,6740,6824,6905,6960,7015,7081,7150,7227,7298,7377,7445,7521,7591,7656,7758,7853,7926,8020,8113,8187,8256,8350,8406,8489,8556,8640,8728,8790,8854,8917,8984,9081,9174,9265,9356,9455,9552,9611,9670,9747,9912,9988,10061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,9752", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,9827"}}]}]}