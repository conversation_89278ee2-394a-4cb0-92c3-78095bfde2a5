{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,9820", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,9896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,594,693,813,897,954,1017,1108,1175,1234,1322,1412,1475,1540,1604,1673,1735,1804,1873,1929,1983,2098,2156,2217,2271,2344,2471,2557,2639,2738,2823,2907,3040,3115,3191,3324,3410,3491,3545,3597,3663,3736,3816,3887,3967,4038,4114,4193,4262,4369,4465,4543,4638,4732,4806,4881,4975,5026,5108,5175,5262,5352,5414,5478,5541,5608,5710,5801,5898,5990,6085,6177,6235,6291,6369,6455,6530", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,73,71,81,97,98,119,83,56,62,90,66,58,87,89,62,64,63,68,61,68,68,55,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,93,73,74,93,50,81,66,86,89,61,63,62,66,101,90,96,91,94,91,57,55,77,85,74,72", "endOffsets": "263,337,409,491,589,688,808,892,949,1012,1103,1170,1229,1317,1407,1470,1535,1599,1668,1730,1799,1868,1924,1978,2093,2151,2212,2266,2339,2466,2552,2634,2733,2818,2902,3035,3110,3186,3319,3405,3486,3540,3592,3658,3731,3811,3882,3962,4033,4109,4188,4257,4364,4460,4538,4633,4727,4801,4876,4970,5021,5103,5170,5257,5347,5409,5473,5536,5603,5705,5796,5893,5985,6080,6172,6230,6286,6364,6450,6525,6598"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3001,3075,3147,3229,4045,4144,4264,4348,4405,4468,4559,4626,4685,4773,4863,4926,4991,5055,5124,5186,5255,5324,5380,5434,5549,5607,5668,5722,5795,5922,6008,6090,6189,6274,6358,6491,6566,6642,6775,6861,6942,6996,7048,7114,7187,7267,7338,7418,7489,7565,7644,7713,7820,7916,7994,8089,8183,8257,8332,8426,8477,8559,8626,8713,8803,8865,8929,8992,9059,9161,9252,9349,9441,9536,9628,9686,9742,9901,9987,10062", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,73,71,81,97,98,119,83,56,62,90,66,58,87,89,62,64,63,68,61,68,68,55,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,93,73,74,93,50,81,66,86,89,61,63,62,66,101,90,96,91,94,91,57,55,77,85,74,72", "endOffsets": "313,3070,3142,3224,3322,4139,4259,4343,4400,4463,4554,4621,4680,4768,4858,4921,4986,5050,5119,5181,5250,5319,5375,5429,5544,5602,5663,5717,5790,5917,6003,6085,6184,6269,6353,6486,6561,6637,6770,6856,6937,6991,7043,7109,7182,7262,7333,7413,7484,7560,7639,7708,7815,7911,7989,8084,8178,8252,8327,8421,8472,8554,8621,8708,8798,8860,8924,8987,9054,9156,9247,9344,9436,9531,9623,9681,9737,9815,9982,10057,10130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3327,3421,3524,3621,3723,3825,3923,10135", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3416,3519,3616,3718,3820,3918,4040,10231"}}]}]}