{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3388,3487,3589,3689,3787,3894,4000,10388", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3482,3584,3684,3782,3889,3995,4115,10484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,622,724,852,933,995,1060,1155,1225,1288,1366,1459,1523,1595,1658,1732,1796,1870,1947,2003,2059,2177,2235,2297,2353,2433,2567,2656,2732,2830,2911,2992,3133,3214,3294,3445,3535,3612,3668,3724,3790,3869,3951,4022,4111,4184,4261,4331,4408,4514,4603,4677,4771,4873,4945,5026,5130,5183,5268,5335,5428,5517,5579,5643,5706,5774,5885,5982,6084,6174,6274,6368,6428,6488,6571,6654,6730", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,81,79,85,95,101,127,80,61,64,94,69,62,77,92,63,71,62,73,63,73,76,55,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,96,101,89,99,93,59,59,82,82,75,76", "endOffsets": "273,355,435,521,617,719,847,928,990,1055,1150,1220,1283,1361,1454,1518,1590,1653,1727,1791,1865,1942,1998,2054,2172,2230,2292,2348,2428,2562,2651,2727,2825,2906,2987,3128,3209,3289,3440,3530,3607,3663,3719,3785,3864,3946,4017,4106,4179,4256,4326,4403,4509,4598,4672,4766,4868,4940,5021,5125,5178,5263,5330,5423,5512,5574,5638,5701,5769,5880,5977,6079,6169,6269,6363,6423,6483,6566,6649,6725,6802"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3044,3126,3206,3292,4120,4222,4350,4431,4493,4558,4653,4723,4786,4864,4957,5021,5093,5156,5230,5294,5368,5445,5501,5557,5675,5733,5795,5851,5931,6065,6154,6230,6328,6409,6490,6631,6712,6792,6943,7033,7110,7166,7222,7288,7367,7449,7520,7609,7682,7759,7829,7906,8012,8101,8175,8269,8371,8443,8524,8628,8681,8766,8833,8926,9015,9077,9141,9204,9272,9383,9480,9582,9672,9772,9866,9926,9986,10152,10235,10311", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,81,79,85,95,101,127,80,61,64,94,69,62,77,92,63,71,62,73,63,73,76,55,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,96,101,89,99,93,59,59,82,82,75,76", "endOffsets": "323,3121,3201,3287,3383,4217,4345,4426,4488,4553,4648,4718,4781,4859,4952,5016,5088,5151,5225,5289,5363,5440,5496,5552,5670,5728,5790,5846,5926,6060,6149,6225,6323,6404,6485,6626,6707,6787,6938,7028,7105,7161,7217,7283,7362,7444,7515,7604,7677,7754,7824,7901,8007,8096,8170,8264,8366,8438,8519,8623,8676,8761,8828,8921,9010,9072,9136,9199,9267,9378,9475,9577,9667,9767,9861,9921,9981,10064,10230,10306,10383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,448,557,665,750,852,968,1053,1133,1224,1317,1412,1506,1605,1698,1797,1893,1984,2075,2157,2264,2363,2462,2570,2678,2785,2944,10069", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "443,552,660,745,847,963,1048,1128,1219,1312,1407,1501,1600,1693,1792,1888,1979,2070,2152,2259,2358,2457,2565,2673,2780,2939,3039,10147"}}]}]}