{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3357,3452,3554,3651,3761,3867,3985,10183", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3447,3549,3646,3756,3862,3980,4095,10279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,612,715,831,914,976,1041,1134,1199,1258,1337,1424,1486,1548,1608,1674,1736,1807,1879,1935,1989,2097,2154,2215,2270,2341,2461,2552,2629,2726,2811,2897,3045,3131,3217,3345,3433,3511,3564,3615,3681,3752,3830,3901,3980,4053,4129,4202,4273,4380,4472,4545,4635,4728,4802,4873,4964,5016,5096,5164,5248,5333,5395,5459,5522,5594,5698,5794,5890,5984,6081,6176,6233,6288,6374,6459,6537", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,79,78,86,86,102,115,82,61,64,92,64,58,78,86,61,61,59,65,61,70,71,55,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,95,95,93,96,94,56,54,85,84,77,76", "endOffsets": "274,354,433,520,607,710,826,909,971,1036,1129,1194,1253,1332,1419,1481,1543,1603,1669,1731,1802,1874,1930,1984,2092,2149,2210,2265,2336,2456,2547,2624,2721,2806,2892,3040,3126,3212,3340,3428,3506,3559,3610,3676,3747,3825,3896,3975,4048,4124,4197,4268,4375,4467,4540,4630,4723,4797,4868,4959,5011,5091,5159,5243,5328,5390,5454,5517,5589,5693,5789,5885,5979,6076,6171,6228,6283,6369,6454,6532,6609"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3104,3183,3270,4100,4203,4319,4402,4464,4529,4622,4687,4746,4825,4912,4974,5036,5096,5162,5224,5295,5367,5423,5477,5585,5642,5703,5758,5829,5949,6040,6117,6214,6299,6385,6533,6619,6705,6833,6921,6999,7052,7103,7169,7240,7318,7389,7468,7541,7617,7690,7761,7868,7960,8033,8123,8216,8290,8361,8452,8504,8584,8652,8736,8821,8883,8947,9010,9082,9186,9282,9378,9472,9569,9664,9721,9776,9943,10028,10106", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,79,78,86,86,102,115,82,61,64,92,64,58,78,86,61,61,59,65,61,70,71,55,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,95,95,93,96,94,56,54,85,84,77,76", "endOffsets": "324,3099,3178,3265,3352,4198,4314,4397,4459,4524,4617,4682,4741,4820,4907,4969,5031,5091,5157,5219,5290,5362,5418,5472,5580,5637,5698,5753,5824,5944,6035,6112,6209,6294,6380,6528,6614,6700,6828,6916,6994,7047,7098,7164,7235,7313,7384,7463,7536,7612,7685,7756,7863,7955,8028,8118,8211,8285,8356,8447,8499,8579,8647,8731,8816,8878,8942,9005,9077,9181,9277,9373,9467,9564,9659,9716,9771,9857,10023,10101,10178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,9862", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,9938"}}]}]}