{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,588,694,810,893,957,1022,1116,1181,1240,1316,1403,1465,1527,1587,1653,1715,1783,1852,1908,1962,2074,2131,2192,2246,2318,2444,2530,2608,2701,2787,2871,3010,3091,3172,3303,3393,3475,3528,3580,3646,3718,3802,3873,3953,4028,4104,4177,4252,4350,4435,4510,4602,4696,4770,4843,4937,4989,5071,5140,5225,5312,5374,5438,5501,5573,5676,5769,5864,5955,6049,6141,6198,6254,6334,6415,6493", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,78,75,78,84,105,115,82,63,64,93,64,58,75,86,61,61,59,65,61,67,68,55,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,130,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,92,94,90,93,91,56,55,79,80,77,77", "endOffsets": "264,343,419,498,583,689,805,888,952,1017,1111,1176,1235,1311,1398,1460,1522,1582,1648,1710,1778,1847,1903,1957,2069,2126,2187,2241,2313,2439,2525,2603,2696,2782,2866,3005,3086,3167,3298,3388,3470,3523,3575,3641,3713,3797,3868,3948,4023,4099,4172,4247,4345,4430,4505,4597,4691,4765,4838,4932,4984,5066,5135,5220,5307,5369,5433,5496,5568,5671,5764,5859,5950,6044,6136,6193,6249,6329,6410,6488,6566"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3105,3181,3260,4075,4181,4297,4380,4444,4509,4603,4668,4727,4803,4890,4952,5014,5074,5140,5202,5270,5339,5395,5449,5561,5618,5679,5733,5805,5931,6017,6095,6188,6274,6358,6497,6578,6659,6790,6880,6962,7015,7067,7133,7205,7289,7360,7440,7515,7591,7664,7739,7837,7922,7997,8089,8183,8257,8330,8424,8476,8558,8627,8712,8799,8861,8925,8988,9060,9163,9256,9351,9442,9536,9628,9685,9741,9906,9987,10065", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,78,75,78,84,105,115,82,63,64,93,64,58,75,86,61,61,59,65,61,67,68,55,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,130,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,92,94,90,93,91,56,55,79,80,77,77", "endOffsets": "314,3100,3176,3255,3340,4176,4292,4375,4439,4504,4598,4663,4722,4798,4885,4947,5009,5069,5135,5197,5265,5334,5390,5444,5556,5613,5674,5728,5800,5926,6012,6090,6183,6269,6353,6492,6573,6654,6785,6875,6957,7010,7062,7128,7200,7284,7355,7435,7510,7586,7659,7734,7832,7917,7992,8084,8178,8252,8325,8419,8471,8553,8622,8707,8794,8856,8920,8983,9055,9158,9251,9346,9437,9531,9623,9680,9736,9816,9982,10060,10138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3345,3440,3542,3639,3736,3842,3960,10143", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3435,3537,3634,3731,3837,3955,4070,10239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,646,733,837,953,1036,1114,1205,1298,1393,1487,1587,1680,1775,1869,1960,2051,2137,2240,2345,2446,2550,2659,2767,2927,9821", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,641,728,832,948,1031,1109,1200,1293,1388,1482,1582,1675,1770,1864,1955,2046,2132,2235,2340,2441,2545,2654,2762,2922,3021,9901"}}]}]}