[{"merged": "com.example.fragmentsleam.app-debug-35:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_mf.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/mf.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/layout_fragment_red.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/layout/fragment_red.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/layout_fragment_blue.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/layout/fragment_blue.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/xml_data_extraction_rules.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/xml/data_extraction_rules.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/layout_activity_main.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/layout/activity_main.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_s1.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/s1.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/layout_recycler_single_item.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/layout/recycler_single_item.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_s3.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/s3.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_nasuse.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/nasuse.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_lee.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/lee.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_thre.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/thre.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/xml_backup_rules.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/xml/backup_rules.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_ic_launcher_background.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_s5.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/s5.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_s2.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/s2.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_s4.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/s4.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/drawable_thresh.png.flat", "source": "com.example.fragmentsleam.app-main-37:/drawable/thresh.png"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/layout_fragment_pink.xml.flat", "source": "com.example.fragmentsleam.app-main-37:/layout/fragment_pink.xml"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.fragmentsleam.app-debug-35:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.fragmentsleam.app-main-37:/mipmap-xxxhdpi/ic_launcher_round.webp"}]