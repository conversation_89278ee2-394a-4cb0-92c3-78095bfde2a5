{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,603,699,823,910,973,1039,1130,1200,1264,1344,1447,1510,1575,1635,1703,1766,1836,1911,1970,2025,2153,2210,2272,2327,2402,2542,2629,2708,2801,2887,2970,3103,3185,3270,3416,3503,3580,3634,3689,3755,3828,3904,3975,4053,4126,4202,4277,4347,4456,4544,4619,4711,4811,4885,4959,5066,5119,5201,5268,5351,5438,5500,5564,5627,5697,5811,5907,6009,6100,6199,6295,6353,6412,6497,6586,6670", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,80,79,81,88,95,123,86,62,65,90,69,63,79,102,62,64,59,67,62,69,74,58,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,99,73,73,106,52,81,66,82,86,61,63,62,69,113,95,101,90,98,95,57,58,84,88,83,78", "endOffsets": "266,347,427,509,598,694,818,905,968,1034,1125,1195,1259,1339,1442,1505,1570,1630,1698,1761,1831,1906,1965,2020,2148,2205,2267,2322,2397,2537,2624,2703,2796,2882,2965,3098,3180,3265,3411,3498,3575,3629,3684,3750,3823,3899,3970,4048,4121,4197,4272,4342,4451,4539,4614,4706,4806,4880,4954,5061,5114,5196,5263,5346,5433,5495,5559,5622,5692,5806,5902,6004,6095,6194,6290,6348,6407,6492,6581,6665,6744"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3117,3197,3279,4087,4183,4307,4394,4457,4523,4614,4684,4748,4828,4931,4994,5059,5119,5187,5250,5320,5395,5454,5509,5637,5694,5756,5811,5886,6026,6113,6192,6285,6371,6454,6587,6669,6754,6900,6987,7064,7118,7173,7239,7312,7388,7459,7537,7610,7686,7761,7831,7940,8028,8103,8195,8295,8369,8443,8550,8603,8685,8752,8835,8922,8984,9048,9111,9181,9295,9391,9493,9584,9683,9779,9837,9896,10069,10158,10242", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,80,79,81,88,95,123,86,62,65,90,69,63,79,102,62,64,59,67,62,69,74,58,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,99,73,73,106,52,81,66,82,86,61,63,62,69,113,95,101,90,98,95,57,58,84,88,83,78", "endOffsets": "316,3112,3192,3274,3363,4178,4302,4389,4452,4518,4609,4679,4743,4823,4926,4989,5054,5114,5182,5245,5315,5390,5449,5504,5632,5689,5751,5806,5881,6021,6108,6187,6280,6366,6449,6582,6664,6749,6895,6982,7059,7113,7168,7234,7307,7383,7454,7532,7605,7681,7756,7826,7935,8023,8098,8190,8290,8364,8438,8545,8598,8680,8747,8830,8917,8979,9043,9106,9176,9290,9386,9488,9579,9678,9774,9832,9891,9976,10153,10237,10316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3368,3466,3568,3665,3763,3868,3971,10321", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3461,3563,3660,3758,3863,3966,4082,10417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2903"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,533,641,727,835,954,1038,1119,1210,1303,1399,1493,1593,1686,1781,1877,1968,2059,2146,2252,2358,2459,2566,2678,2782,2938,9981", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "424,528,636,722,830,949,1033,1114,1205,1298,1394,1488,1588,1681,1776,1872,1963,2054,2141,2247,2353,2454,2561,2673,2777,2933,3031,10064"}}]}]}