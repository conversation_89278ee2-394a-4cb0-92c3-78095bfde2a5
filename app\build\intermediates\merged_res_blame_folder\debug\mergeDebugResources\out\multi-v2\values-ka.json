{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,9934", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,10011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3356,3452,3554,3653,3752,3858,3962,10262", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3447,3549,3648,3747,3853,3957,4075,10358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,603,702,827,915,978,1045,1142,1211,1274,1353,1440,1504,1570,1630,1699,1760,1835,1912,1974,2028,2143,2202,2262,2316,2388,2518,2606,2685,2783,2871,2955,3093,3171,3247,3386,3480,3560,3616,3670,3736,3809,3887,3958,4042,4115,4193,4266,4341,4451,4541,4616,4710,4808,4882,4959,5059,5112,5196,5264,5353,5442,5504,5569,5632,5702,5809,5898,5998,6083,6173,6259,6319,6377,6457,6547,6622", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,77,73,83,93,98,124,87,62,66,96,68,62,78,86,63,65,59,68,60,74,76,61,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,88,99,84,89,85,59,57,79,89,74,80", "endOffsets": "268,346,420,504,598,697,822,910,973,1040,1137,1206,1269,1348,1435,1499,1565,1625,1694,1755,1830,1907,1969,2023,2138,2197,2257,2311,2383,2513,2601,2680,2778,2866,2950,3088,3166,3242,3381,3475,3555,3611,3665,3731,3804,3882,3953,4037,4110,4188,4261,4336,4446,4536,4611,4705,4803,4877,4954,5054,5107,5191,5259,5348,5437,5499,5564,5627,5697,5804,5893,5993,6078,6168,6254,6314,6372,6452,6542,6617,6698"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3104,3178,3262,4080,4179,4304,4392,4455,4522,4619,4688,4751,4830,4917,4981,5047,5107,5176,5237,5312,5389,5451,5505,5620,5679,5739,5793,5865,5995,6083,6162,6260,6348,6432,6570,6648,6724,6863,6957,7037,7093,7147,7213,7286,7364,7435,7519,7592,7670,7743,7818,7928,8018,8093,8187,8285,8359,8436,8536,8589,8673,8741,8830,8919,8981,9046,9109,9179,9286,9375,9475,9560,9650,9736,9796,9854,10016,10106,10181", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,77,73,83,93,98,124,87,62,66,96,68,62,78,86,63,65,59,68,60,74,76,61,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,88,99,84,89,85,59,57,79,89,74,80", "endOffsets": "318,3099,3173,3257,3351,4174,4299,4387,4450,4517,4614,4683,4746,4825,4912,4976,5042,5102,5171,5232,5307,5384,5446,5500,5615,5674,5734,5788,5860,5990,6078,6157,6255,6343,6427,6565,6643,6719,6858,6952,7032,7088,7142,7208,7281,7359,7430,7514,7587,7665,7738,7813,7923,8013,8088,8182,8280,8354,8431,8531,8584,8668,8736,8825,8914,8976,9041,9104,9174,9281,9370,9470,9555,9645,9731,9791,9849,9929,10101,10176,10257"}}]}]}