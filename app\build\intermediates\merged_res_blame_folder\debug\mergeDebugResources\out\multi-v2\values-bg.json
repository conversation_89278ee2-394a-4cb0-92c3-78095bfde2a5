{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,616,711,844,933,996,1062,1159,1239,1301,1381,1470,1533,1598,1657,1730,1793,1863,1937,1996,2050,2178,2235,2297,2351,2424,2567,2651,2729,2822,2904,2992,3128,3216,3304,3440,3525,3602,3655,3706,3772,3847,3923,3994,4073,4150,4226,4303,4377,4489,4580,4655,4746,4838,4912,4999,5090,5145,5227,5293,5376,5462,5524,5588,5651,5721,5838,5938,6049,6146,6247,6345,6402,6457,6543,6634,6710", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,78,78,82,109,94,132,88,62,65,96,79,61,79,88,62,64,58,72,62,69,73,58,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,99,110,96,100,97,56,54,85,90,75,78", "endOffsets": "260,339,418,501,611,706,839,928,991,1057,1154,1234,1296,1376,1465,1528,1593,1652,1725,1788,1858,1932,1991,2045,2173,2230,2292,2346,2419,2562,2646,2724,2817,2899,2987,3123,3211,3299,3435,3520,3597,3650,3701,3767,3842,3918,3989,4068,4145,4221,4298,4372,4484,4575,4650,4741,4833,4907,4994,5085,5140,5222,5288,5371,5457,5519,5583,5646,5716,5833,5933,6044,6141,6242,6340,6397,6452,6538,6629,6705,6784"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3064,3143,3222,3305,4156,4251,4384,4473,4536,4602,4699,4779,4841,4921,5010,5073,5138,5197,5270,5333,5403,5477,5536,5590,5718,5775,5837,5891,5964,6107,6191,6269,6362,6444,6532,6668,6756,6844,6980,7065,7142,7195,7246,7312,7387,7463,7534,7613,7690,7766,7843,7917,8029,8120,8195,8286,8378,8452,8539,8630,8685,8767,8833,8916,9002,9064,9128,9191,9261,9378,9478,9589,9686,9787,9885,9942,9997,10167,10258,10334", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,78,78,82,109,94,132,88,62,65,96,79,61,79,88,62,64,58,72,62,69,73,58,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,99,110,96,100,97,56,54,85,90,75,78", "endOffsets": "310,3138,3217,3300,3410,4246,4379,4468,4531,4597,4694,4774,4836,4916,5005,5068,5133,5192,5265,5328,5398,5472,5531,5585,5713,5770,5832,5886,5959,6102,6186,6264,6357,6439,6527,6663,6751,6839,6975,7060,7137,7190,7241,7307,7382,7458,7529,7608,7685,7761,7838,7912,8024,8115,8190,8281,8373,8447,8534,8625,8680,8762,8828,8911,8997,9059,9123,9186,9256,9373,9473,9584,9681,9782,9880,9937,9992,10078,10253,10329,10408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3415,3512,3622,3724,3825,3932,4037,10413", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3507,3617,3719,3820,3927,4032,4151,10509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,10083", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,10162"}}]}]}