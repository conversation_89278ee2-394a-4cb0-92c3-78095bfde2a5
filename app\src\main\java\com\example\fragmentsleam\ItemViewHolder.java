package com.example.fragmentsleam;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * ViewHolder class for RecyclerView items
 * Holds references to the views in each item layout
 */
public class ItemViewHolder extends RecyclerView.ViewHolder {
    
    public ImageView itemImage;
    public TextView itemName;
    public TextView itemRole;

    public ItemViewHolder(@NonNull View itemView) {
        super(itemView);
        
        // Initialize view references
        itemImage = itemView.findViewById(R.id.item_image);
        itemName = itemView.findViewById(R.id.item_name);
        itemRole = itemView.findViewById(R.id.item_role);
    }
}
