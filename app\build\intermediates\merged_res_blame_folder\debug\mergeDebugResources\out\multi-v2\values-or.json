{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3395,3498,3600,3703,3808,3909,4011,10084", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3493,3595,3698,3803,3904,4006,4125,10180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,326,433,519,623,743,822,903,994,1087,1188,1283,1383,1476,1571,1667,1758,1848,1937,2047,2151,2257,2368,2470,2588,2751,2857", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "211,321,428,514,618,738,817,898,989,1082,1183,1278,1378,1471,1566,1662,1753,1843,1932,2042,2146,2252,2363,2465,2583,2746,2852,2942"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,433,543,650,736,840,960,1039,1120,1211,1304,1405,1500,1600,1693,1788,1884,1975,2065,2154,2264,2368,2474,2585,2687,2805,2968,9763", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "428,538,645,731,835,955,1034,1115,1206,1299,1400,1495,1595,1688,1783,1879,1970,2060,2149,2259,2363,2469,2580,2682,2800,2963,3069,9848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,593,687,792,871,931,996,1085,1150,1209,1286,1372,1436,1500,1563,1636,1700,1764,1832,1888,1942,2054,2112,2174,2228,2300,2422,2509,2585,2677,2759,2845,2985,3062,3143,3270,3361,3438,3492,3543,3609,3679,3756,3827,3902,3973,4050,4119,4188,4295,4386,4458,4547,4636,4710,4782,4868,4918,4997,5063,5143,5227,5289,5353,5416,5485,5585,5673,5765,5850,5941,6029,6087,6142,6226,6307,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,81,77,76,83,93,104,78,59,64,88,64,58,76,85,63,63,62,72,63,63,67,55,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,87,91,84,90,87,57,54,83,80,74,74", "endOffsets": "267,349,427,504,588,682,787,866,926,991,1080,1145,1204,1281,1367,1431,1495,1558,1631,1695,1759,1827,1883,1937,2049,2107,2169,2223,2295,2417,2504,2580,2672,2754,2840,2980,3057,3138,3265,3356,3433,3487,3538,3604,3674,3751,3822,3897,3968,4045,4114,4183,4290,4381,4453,4542,4631,4705,4777,4863,4913,4992,5058,5138,5222,5284,5348,5411,5480,5580,5668,5760,5845,5936,6024,6082,6137,6221,6302,6377,6452"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3074,3156,3234,3311,4130,4224,4329,4408,4468,4533,4622,4687,4746,4823,4909,4973,5037,5100,5173,5237,5301,5369,5425,5479,5591,5649,5711,5765,5837,5959,6046,6122,6214,6296,6382,6522,6599,6680,6807,6898,6975,7029,7080,7146,7216,7293,7364,7439,7510,7587,7656,7725,7832,7923,7995,8084,8173,8247,8319,8405,8455,8534,8600,8680,8764,8826,8890,8953,9022,9122,9210,9302,9387,9478,9566,9624,9679,9853,9934,10009", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,81,77,76,83,93,104,78,59,64,88,64,58,76,85,63,63,62,72,63,63,67,55,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,87,91,84,90,87,57,54,83,80,74,74", "endOffsets": "317,3151,3229,3306,3390,4219,4324,4403,4463,4528,4617,4682,4741,4818,4904,4968,5032,5095,5168,5232,5296,5364,5420,5474,5586,5644,5706,5760,5832,5954,6041,6117,6209,6291,6377,6517,6594,6675,6802,6893,6970,7024,7075,7141,7211,7288,7359,7434,7505,7582,7651,7720,7827,7918,7990,8079,8168,8242,8314,8400,8450,8529,8595,8675,8759,8821,8885,8948,9017,9117,9205,9297,9382,9473,9561,9619,9674,9758,9929,10004,10079"}}]}]}