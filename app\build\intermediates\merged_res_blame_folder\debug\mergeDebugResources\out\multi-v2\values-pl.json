{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "436,551,653,761,847,954,1073,1152,1228,1319,1412,1507,1601,1702,1795,1890,1985,2076,2167,2249,2358,2458,2557,2666,2778,2889,3052,10018", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "546,648,756,842,949,1068,1147,1223,1314,1407,1502,1596,1697,1790,1885,1980,2071,2162,2244,2353,2453,2552,2661,2773,2884,3047,3143,10096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3569,3671,3769,3868,3982,4087,10340", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3564,3666,3764,3863,3977,4082,4204,10436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,710,795,912,994,1059,1123,1204,1268,1329,1407,1518,1582,1650,1704,1773,1835,1904,1977,2035,2089,2200,2261,2323,2377,2449,2578,2667,2746,2841,2926,3008,3157,3239,3322,3459,3546,3623,3677,3728,3794,3865,3941,4012,4095,4172,4250,4328,4404,4512,4602,4675,4770,4867,4939,5013,5113,5165,5250,5316,5404,5494,5556,5620,5683,5754,5861,5954,6053,6141,6238,6330,6388,6443,6519,6603,6680", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,74,74,78,94,84,116,81,64,63,80,63,60,77,110,63,67,53,68,61,68,72,57,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,92,98,87,96,91,57,54,75,83,76,77", "endOffsets": "381,456,531,610,705,790,907,989,1054,1118,1199,1263,1324,1402,1513,1577,1645,1699,1768,1830,1899,1972,2030,2084,2195,2256,2318,2372,2444,2573,2662,2741,2836,2921,3003,3152,3234,3317,3454,3541,3618,3672,3723,3789,3860,3936,4007,4090,4167,4245,4323,4399,4507,4597,4670,4765,4862,4934,5008,5108,5160,5245,5311,5399,5489,5551,5615,5678,5749,5856,5949,6048,6136,6233,6325,6383,6438,6514,6598,6675,6753"}, "to": {"startLines": "2,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3223,3298,3377,4209,4294,4411,4493,4558,4622,4703,4767,4828,4906,5017,5081,5149,5203,5272,5334,5403,5476,5534,5588,5699,5760,5822,5876,5948,6077,6166,6245,6340,6425,6507,6656,6738,6821,6958,7045,7122,7176,7227,7293,7364,7440,7511,7594,7671,7749,7827,7903,8011,8101,8174,8269,8366,8438,8512,8612,8664,8749,8815,8903,8993,9055,9119,9182,9253,9360,9453,9552,9640,9737,9829,9887,9942,10101,10185,10262", "endLines": "7,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "endColumns": "12,74,74,78,94,84,116,81,64,63,80,63,60,77,110,63,67,53,68,61,68,72,57,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,92,98,87,96,91,57,54,75,83,76,77", "endOffsets": "431,3218,3293,3372,3467,4289,4406,4488,4553,4617,4698,4762,4823,4901,5012,5076,5144,5198,5267,5329,5398,5471,5529,5583,5694,5755,5817,5871,5943,6072,6161,6240,6335,6420,6502,6651,6733,6816,6953,7040,7117,7171,7222,7288,7359,7435,7506,7589,7666,7744,7822,7898,8006,8096,8169,8264,8361,8433,8507,8607,8659,8744,8810,8898,8988,9050,9114,9177,9248,9355,9448,9547,9635,9732,9824,9882,9937,10013,10180,10257,10335"}}]}]}