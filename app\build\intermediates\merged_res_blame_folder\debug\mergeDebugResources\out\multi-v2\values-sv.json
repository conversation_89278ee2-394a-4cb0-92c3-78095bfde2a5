{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,817,930,1007,1082,1175,1270,1365,1459,1561,1656,1753,1851,1947,2040,2120,2226,2325,2421,2526,2629,2731,2885,9727", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,812,925,1002,1077,1170,1265,1360,1454,1556,1651,1748,1846,1942,2035,2115,2221,2320,2416,2521,2624,2726,2880,2982,9802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,618,716,838,917,976,1039,1131,1195,1255,1333,1425,1490,1553,1615,1682,1746,1818,1894,1950,2004,2109,2168,2229,2283,2352,2471,2554,2631,2721,2805,2889,3025,3104,3188,3310,3396,3474,3528,3579,3645,3714,3788,3859,3935,4007,4084,4155,4229,4340,4431,4510,4597,4685,4757,4831,4916,4967,5046,5113,5194,5278,5340,5404,5467,5535,5642,5726,5825,5905,5998,6087,6145,6200,6278,6359,6438", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,98,91,80,79,97,121,78,58,62,91,63,59,77,91,64,62,61,66,63,71,75,55,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,83,98,79,92,88,57,54,77,80,78,87", "endOffsets": "261,360,452,533,613,711,833,912,971,1034,1126,1190,1250,1328,1420,1485,1548,1610,1677,1741,1813,1889,1945,1999,2104,2163,2224,2278,2347,2466,2549,2626,2716,2800,2884,3020,3099,3183,3305,3391,3469,3523,3574,3640,3709,3783,3854,3930,4002,4079,4150,4224,4335,4426,4505,4592,4680,4752,4826,4911,4962,5041,5108,5189,5273,5335,5399,5462,5530,5637,5721,5820,5900,5993,6082,6140,6195,6273,6354,6433,6521"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2987,3086,3178,3259,4067,4165,4287,4366,4425,4488,4580,4644,4704,4782,4874,4939,5002,5064,5131,5195,5267,5343,5399,5453,5558,5617,5678,5732,5801,5920,6003,6080,6170,6254,6338,6474,6553,6637,6759,6845,6923,6977,7028,7094,7163,7237,7308,7384,7456,7533,7604,7678,7789,7880,7959,8046,8134,8206,8280,8365,8416,8495,8562,8643,8727,8789,8853,8916,8984,9091,9175,9274,9354,9447,9536,9594,9649,9807,9888,9967", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,98,91,80,79,97,121,78,58,62,91,63,59,77,91,64,62,61,66,63,71,75,55,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,83,98,79,92,88,57,54,77,80,78,87", "endOffsets": "311,3081,3173,3254,3334,4160,4282,4361,4420,4483,4575,4639,4699,4777,4869,4934,4997,5059,5126,5190,5262,5338,5394,5448,5553,5612,5673,5727,5796,5915,5998,6075,6165,6249,6333,6469,6548,6632,6754,6840,6918,6972,7023,7089,7158,7232,7303,7379,7451,7528,7599,7673,7784,7875,7954,8041,8129,8201,8275,8360,8411,8490,8557,8638,8722,8784,8848,8911,8979,9086,9170,9269,9349,9442,9531,9589,9644,9722,9883,9962,10050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3339,3434,3536,3634,3733,3841,3946,10055", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3429,3531,3629,3728,3836,3941,4062,10151"}}]}]}