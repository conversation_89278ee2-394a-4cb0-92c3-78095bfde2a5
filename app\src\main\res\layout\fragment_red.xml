<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F44336"
    tools:context=".RedFragment" >

    <ImageView
        android:id="@+id/imageView3"
        android:layout_width="367dp"
        android:layout_height="322dp"
        tools:layout_editor_absoluteX="16dp"
        tools:layout_editor_absoluteY="131dp"
        tools:src="@tools:sample/avatars" />

    <TextView
        android:id="@+id/decreption"
        android:layout_width="345dp"
        android:layout_height="180dp"
        android:text="TextView"
        tools:layout_editor_absoluteX="37dp"
        tools:layout_editor_absoluteY="508dp" />

    <TextView
        android:id="@+id/name"
        android:layout_width="156dp"
        android:layout_height="39dp"
        android:text="TextView"
        tools:layout_editor_absoluteX="130dp"
        tools:layout_editor_absoluteY="460dp" />
</androidx.constraintlayout.widget.ConstraintLayout>