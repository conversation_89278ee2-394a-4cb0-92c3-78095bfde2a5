{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3415,3513,3620,3717,3816,3920,4024,10333", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3508,3615,3712,3811,3915,4019,4136,10429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,654,750,880,964,1031,1099,1195,1263,1326,1406,1514,1574,1640,1696,1767,1827,1894,1966,2027,2081,2207,2264,2326,2380,2455,2589,2674,2752,2847,2932,3013,3150,3234,3320,3453,3544,3622,3678,3733,3799,3873,3951,4022,4104,4176,4253,4333,4407,4514,4607,4680,4772,4868,4942,5018,5114,5166,5248,5315,5402,5489,5551,5615,5678,5748,5854,5947,6044,6139,6234,6327,6387,6446,6526,6609,6686", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,77,77,84,92,95,129,83,66,67,95,67,62,79,107,59,65,55,70,59,66,71,60,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,92,96,94,94,92,59,58,79,82,76,74", "endOffsets": "315,393,471,556,649,745,875,959,1026,1094,1190,1258,1321,1401,1509,1569,1635,1691,1762,1822,1889,1961,2022,2076,2202,2259,2321,2375,2450,2584,2669,2747,2842,2927,3008,3145,3229,3315,3448,3539,3617,3673,3728,3794,3868,3946,4017,4099,4171,4248,4328,4402,4509,4602,4675,4767,4863,4937,5013,5109,5161,5243,5310,5397,5484,5546,5610,5673,5743,5849,5942,6039,6134,6229,6322,6382,6441,6521,6604,6681,6756"}, "to": {"startLines": "2,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3081,3159,3237,3322,4141,4237,4367,4451,4518,4586,4682,4750,4813,4893,5001,5061,5127,5183,5254,5314,5381,5453,5514,5568,5694,5751,5813,5867,5942,6076,6161,6239,6334,6419,6500,6637,6721,6807,6940,7031,7109,7165,7220,7286,7360,7438,7509,7591,7663,7740,7820,7894,8001,8094,8167,8259,8355,8429,8505,8601,8653,8735,8802,8889,8976,9038,9102,9165,9235,9341,9434,9531,9626,9721,9814,9874,9933,10098,10181,10258", "endLines": "6,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "endColumns": "12,77,77,84,92,95,129,83,66,67,95,67,62,79,107,59,65,55,70,59,66,71,60,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,92,96,94,94,92,59,58,79,82,76,74", "endOffsets": "365,3154,3232,3317,3410,4232,4362,4446,4513,4581,4677,4745,4808,4888,4996,5056,5122,5178,5249,5309,5376,5448,5509,5563,5689,5746,5808,5862,5937,6071,6156,6234,6329,6414,6495,6632,6716,6802,6935,7026,7104,7160,7215,7281,7355,7433,7504,7586,7658,7735,7815,7889,7996,8089,8162,8254,8350,8424,8500,8596,8648,8730,8797,8884,8971,9033,9097,9160,9230,9336,9429,9526,9621,9716,9809,9869,9928,10008,10176,10253,10328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,10013", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,10093"}}]}]}