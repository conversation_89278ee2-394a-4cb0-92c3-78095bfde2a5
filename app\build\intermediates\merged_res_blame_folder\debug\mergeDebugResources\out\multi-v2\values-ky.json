{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,628,729,870,954,1014,1078,1172,1242,1303,1383,1470,1533,1597,1656,1730,1792,1863,1936,1992,2046,2163,2221,2282,2336,2410,2532,2616,2695,2795,2881,2977,3109,3187,3265,3394,3483,3563,3624,3679,3745,3814,3891,3962,4043,4117,4193,4283,4356,4458,4543,4622,4712,4804,4878,4963,5053,5105,5189,5254,5339,5424,5486,5550,5613,5682,5799,5893,5993,6083,6178,6271,6336,6395,6477,6563,6639", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,82,84,84,109,100,140,83,59,63,93,69,60,79,86,62,63,58,73,61,70,72,55,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,93,99,89,94,92,64,58,81,85,75,82", "endOffsets": "260,343,428,513,623,724,865,949,1009,1073,1167,1237,1298,1378,1465,1528,1592,1651,1725,1787,1858,1931,1987,2041,2158,2216,2277,2331,2405,2527,2611,2690,2790,2876,2972,3104,3182,3260,3389,3478,3558,3619,3674,3740,3809,3886,3957,4038,4112,4188,4278,4351,4453,4538,4617,4707,4799,4873,4958,5048,5100,5184,5249,5334,5419,5481,5545,5608,5677,5794,5888,5988,6078,6173,6266,6331,6390,6472,6558,6634,6717"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3027,3110,3195,3280,4121,4222,4363,4447,4507,4571,4665,4735,4796,4876,4963,5026,5090,5149,5223,5285,5356,5429,5485,5539,5656,5714,5775,5829,5903,6025,6109,6188,6288,6374,6470,6602,6680,6758,6887,6976,7056,7117,7172,7238,7307,7384,7455,7536,7610,7686,7776,7849,7951,8036,8115,8205,8297,8371,8456,8546,8598,8682,8747,8832,8917,8979,9043,9106,9175,9292,9386,9486,9576,9671,9764,9829,9888,10052,10138,10214", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,82,84,84,109,100,140,83,59,63,93,69,60,79,86,62,63,58,73,61,70,72,55,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,93,99,89,94,92,64,58,81,85,75,82", "endOffsets": "310,3105,3190,3275,3385,4217,4358,4442,4502,4566,4660,4730,4791,4871,4958,5021,5085,5144,5218,5280,5351,5424,5480,5534,5651,5709,5770,5824,5898,6020,6104,6183,6283,6369,6465,6597,6675,6753,6882,6971,7051,7112,7167,7233,7302,7379,7450,7531,7605,7681,7771,7844,7946,8031,8110,8200,8292,8366,8451,8541,8593,8677,8742,8827,8912,8974,9038,9101,9170,9287,9381,9481,9571,9666,9759,9824,9883,9965,10133,10209,10292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3390,3490,3592,3695,3802,3906,4010,10297", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "3485,3587,3690,3797,3901,4005,4116,10393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,9970", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,10047"}}]}]}