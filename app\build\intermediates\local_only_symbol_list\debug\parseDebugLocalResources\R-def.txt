R_DEF: Internal format may change without notice
local
color black
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable lee
drawable mf
drawable nasuse
drawable s1
drawable s2
drawable s3
drawable s4
drawable s5
drawable thre
drawable thresh
id frame
id fromWhere
id frrg1
id frrg2
id gotoFrg3
id imageButton
id imageView
id item_image
id item_name
id item_role
id linearLayout
id linearLayout2
id main
id recyclerView
id textView
layout activity_main
layout fragment_blue
layout fragment_pink
layout fragment_red
layout recycler_single_item
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string hello_blank_fragment
style Base.Theme.FragmentSleam
style Theme.FragmentSleam
xml backup_rules
xml data_extraction_rules
