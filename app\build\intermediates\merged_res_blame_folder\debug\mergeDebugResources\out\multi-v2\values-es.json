{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,10102", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,10180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,621,725,847,928,990,1055,1150,1231,1294,1372,1461,1525,1594,1657,1731,1795,1867,1944,2000,2057,2175,2233,2295,2352,2432,2571,2660,2736,2831,2912,2994,3135,3216,3296,3447,3537,3617,3673,3729,3795,3874,3956,4027,4116,4190,4267,4337,4416,4516,4600,4684,4776,4876,4950,5031,5133,5186,5271,5338,5431,5520,5582,5646,5709,5777,5890,5987,6091,6182,6287,6386,6446,6506,6589,6672,6748", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,80,78,86,95,103,121,80,61,64,94,80,62,77,88,63,68,62,73,63,71,76,55,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,96,103,90,104,98,59,59,82,82,75,76", "endOffsets": "273,354,433,520,616,720,842,923,985,1050,1145,1226,1289,1367,1456,1520,1589,1652,1726,1790,1862,1939,1995,2052,2170,2228,2290,2347,2427,2566,2655,2731,2826,2907,2989,3130,3211,3291,3442,3532,3612,3668,3724,3790,3869,3951,4022,4111,4185,4262,4332,4411,4511,4595,4679,4771,4871,4945,5026,5128,5181,5266,5333,5426,5515,5577,5641,5704,5772,5885,5982,6086,6177,6282,6381,6441,6501,6584,6667,6743,6820"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,4134,4238,4360,4441,4503,4568,4663,4744,4807,4885,4974,5038,5107,5170,5244,5308,5380,5457,5513,5570,5688,5746,5808,5865,5945,6084,6173,6249,6344,6425,6507,6648,6729,6809,6960,7050,7130,7186,7242,7308,7387,7469,7540,7629,7703,7780,7850,7929,8029,8113,8197,8289,8389,8463,8544,8646,8699,8784,8851,8944,9033,9095,9159,9222,9290,9403,9500,9604,9695,9800,9899,9959,10019,10185,10268,10344", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,80,78,86,95,103,121,80,61,64,94,80,62,77,88,63,68,62,73,63,71,76,55,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,96,103,90,104,98,59,59,82,82,75,76", "endOffsets": "323,3135,3214,3301,3397,4233,4355,4436,4498,4563,4658,4739,4802,4880,4969,5033,5102,5165,5239,5303,5375,5452,5508,5565,5683,5741,5803,5860,5940,6079,6168,6244,6339,6420,6502,6643,6724,6804,6955,7045,7125,7181,7237,7303,7382,7464,7535,7624,7698,7775,7845,7924,8024,8108,8192,8284,8384,8458,8539,8641,8694,8779,8846,8939,9028,9090,9154,9217,9285,9398,9495,9599,9690,9795,9894,9954,10014,10097,10263,10339,10416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3402,3501,3603,3703,3801,3908,4014,10421", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3496,3598,3698,3796,3903,4009,4129,10517"}}]}]}