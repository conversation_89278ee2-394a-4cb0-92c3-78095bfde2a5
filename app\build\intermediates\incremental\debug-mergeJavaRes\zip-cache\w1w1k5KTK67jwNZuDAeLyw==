[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 139, "crc": -1457599804}, {"key": "com/google/errorprone/annotations/InlineMe.class", "name": "com/google/errorprone/annotations/InlineMe.class", "size": 514, "crc": 1817728846}, {"key": "com/google/errorprone/annotations/DoNotMock.class", "name": "com/google/errorprone/annotations/DoNotMock.class", "size": 598, "crc": 1869330468}, {"key": "com/google/errorprone/annotations/concurrent/LazyInit.class", "name": "com/google/errorprone/annotations/concurrent/LazyInit.class", "size": 591, "crc": 308212981}, {"key": "com/google/errorprone/annotations/concurrent/UnlockMethod.class", "name": "com/google/errorprone/annotations/concurrent/UnlockMethod.class", "size": 500, "crc": 150860817}, {"key": "com/google/errorprone/annotations/concurrent/GuardedBy.class", "name": "com/google/errorprone/annotations/concurrent/GuardedBy.class", "size": 458, "crc": 1218447702}, {"key": "com/google/errorprone/annotations/concurrent/LockMethod.class", "name": "com/google/errorprone/annotations/concurrent/LockMethod.class", "size": 496, "crc": 2018923386}, {"key": "com/google/errorprone/annotations/IncompatibleModifiers.class", "name": "com/google/errorprone/annotations/IncompatibleModifiers.class", "size": 688, "crc": -1139074101}, {"key": "com/google/errorprone/annotations/RequiredModifiers.class", "name": "com/google/errorprone/annotations/RequiredModifiers.class", "size": 680, "crc": 134640325}, {"key": "com/google/errorprone/annotations/NoAllocation.class", "name": "com/google/errorprone/annotations/NoAllocation.class", "size": 354, "crc": 1930943754}, {"key": "com/google/errorprone/annotations/FormatString.class", "name": "com/google/errorprone/annotations/FormatString.class", "size": 452, "crc": 2079482498}, {"key": "com/google/errorprone/annotations/InlineMeValidationDisabled.class", "name": "com/google/errorprone/annotations/InlineMeValidationDisabled.class", "size": 392, "crc": -156616520}, {"key": "com/google/errorprone/annotations/DoNotCall.class", "name": "com/google/errorprone/annotations/DoNotCall.class", "size": 466, "crc": -448569131}, {"key": "com/google/errorprone/annotations/Modifier.class", "name": "com/google/errorprone/annotations/Modifier.class", "size": 1632, "crc": -1260114136}, {"key": "com/google/errorprone/annotations/CompileTimeConstant.class", "name": "com/google/errorprone/annotations/CompileTimeConstant.class", "size": 479, "crc": -2051196131}, {"key": "com/google/errorprone/annotations/CompatibleWith.class", "name": "com/google/errorprone/annotations/CompatibleWith.class", "size": 487, "crc": -1842157779}, {"key": "com/google/errorprone/annotations/Keep.class", "name": "com/google/errorprone/annotations/Keep.class", "size": 502, "crc": 1587736424}, {"key": "com/google/errorprone/annotations/SuppressPackageLocation.class", "name": "com/google/errorprone/annotations/SuppressPackageLocation.class", "size": 432, "crc": 1617213606}, {"key": "com/google/errorprone/annotations/RestrictedApi.class", "name": "com/google/errorprone/annotations/RestrictedApi.class", "size": 652, "crc": -1139660081}, {"key": "com/google/errorprone/annotations/FormatMethod.class", "name": "com/google/errorprone/annotations/FormatMethod.class", "size": 468, "crc": -1039771910}, {"key": "com/google/errorprone/annotations/Var.class", "name": "com/google/errorprone/annotations/Var.class", "size": 609, "crc": -2045960780}, {"key": "com/google/errorprone/annotations/OverridingMethodsMustInvokeSuper.class", "name": "com/google/errorprone/annotations/OverridingMethodsMustInvokeSuper.class", "size": 489, "crc": -1245821162}, {"key": "com/google/errorprone/annotations/CheckReturnValue.class", "name": "com/google/errorprone/annotations/CheckReturnValue.class", "size": 505, "crc": 1302111325}, {"key": "com/google/errorprone/annotations/Immutable.class", "name": "com/google/errorprone/annotations/Immutable.class", "size": 557, "crc": -845112464}, {"key": "com/google/errorprone/annotations/MustBeClosed.class", "name": "com/google/errorprone/annotations/MustBeClosed.class", "size": 373, "crc": 1838364320}, {"key": "com/google/errorprone/annotations/CanIgnoreReturnValue.class", "name": "com/google/errorprone/annotations/CanIgnoreReturnValue.class", "size": 496, "crc": 131530274}, {"key": "com/google/errorprone/annotations/ForOverride.class", "name": "com/google/errorprone/annotations/ForOverride.class", "size": 668, "crc": -1356507517}, {"key": "META-INF/maven/com.google.errorprone/error_prone_annotations/pom.xml", "name": "META-INF/maven/com.google.errorprone/error_prone_annotations/pom.xml", "size": 2163, "crc": 1965607386}, {"key": "META-INF/maven/com.google.errorprone/error_prone_annotations/pom.properties", "name": "META-INF/maven/com.google.errorprone/error_prone_annotations/pom.properties", "size": 80, "crc": 1426145016}]