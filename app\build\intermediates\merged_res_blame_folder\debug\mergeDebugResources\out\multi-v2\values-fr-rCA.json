{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,677,777,899,984,1049,1115,1212,1292,1354,1444,1536,1603,1677,1738,1817,1881,1951,2025,2082,2136,2252,2311,2373,2427,2509,2638,2730,2805,2900,2981,3065,3209,3288,3369,3516,3609,3688,3743,3794,3860,3939,4020,4091,4171,4243,4321,4396,4468,4579,4676,4753,4851,4943,5021,5102,5194,5251,5335,5401,5484,5571,5633,5697,5760,5836,5938,6037,6134,6224,6314,6404,6463,6518,6607,6694,6771", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,106,107,81,96,99,121,84,64,65,96,79,61,89,91,66,73,60,78,63,69,73,56,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,91,77,80,91,56,83,65,82,86,61,63,62,75,101,98,96,89,89,89,58,54,88,86,76,80", "endOffsets": "278,385,493,575,672,772,894,979,1044,1110,1207,1287,1349,1439,1531,1598,1672,1733,1812,1876,1946,2020,2077,2131,2247,2306,2368,2422,2504,2633,2725,2800,2895,2976,3060,3204,3283,3364,3511,3604,3683,3738,3789,3855,3934,4015,4086,4166,4238,4316,4391,4463,4574,4671,4748,4846,4938,5016,5097,5189,5246,5330,5396,5479,5566,5628,5692,5755,5831,5933,6032,6129,6219,6309,6399,6458,6513,6602,6689,6766,6847"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3083,3190,3298,3380,4200,4300,4422,4507,4572,4638,4735,4815,4877,4967,5059,5126,5200,5261,5340,5404,5474,5548,5605,5659,5775,5834,5896,5950,6032,6161,6253,6328,6423,6504,6588,6732,6811,6892,7039,7132,7211,7266,7317,7383,7462,7543,7614,7694,7766,7844,7919,7991,8102,8199,8276,8374,8466,8544,8625,8717,8774,8858,8924,9007,9094,9156,9220,9283,9359,9461,9560,9657,9747,9837,9927,9986,10041,10217,10304,10381", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,106,107,81,96,99,121,84,64,65,96,79,61,89,91,66,73,60,78,63,69,73,56,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,91,77,80,91,56,83,65,82,86,61,63,62,75,101,98,96,89,89,89,58,54,88,86,76,80", "endOffsets": "328,3185,3293,3375,3472,4295,4417,4502,4567,4633,4730,4810,4872,4962,5054,5121,5195,5256,5335,5399,5469,5543,5600,5654,5770,5829,5891,5945,6027,6156,6248,6323,6418,6499,6583,6727,6806,6887,7034,7127,7206,7261,7312,7378,7457,7538,7609,7689,7761,7839,7914,7986,8097,8194,8271,8369,8461,8539,8620,8712,8769,8853,8919,9002,9089,9151,9215,9278,9354,9456,9555,9652,9742,9832,9922,9981,10036,10125,10299,10376,10457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,10130", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,10212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3477,3575,3677,3776,3878,3982,4086,10462", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3570,3672,3771,3873,3977,4081,4195,10558"}}]}]}