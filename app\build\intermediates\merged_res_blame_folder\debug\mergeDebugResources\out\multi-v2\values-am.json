{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3224,3317,3417,3514,3613,3709,3811,9623", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3312,3412,3509,3608,3704,3806,3906,9719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,409,507,613,699,802,919,997,1073,1164,1257,1349,1443,1543,1636,1731,1824,1915,2006,2086,2186,2286,2382,2484,2584,2683,2833,9318", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "404,502,608,694,797,914,992,1068,1159,1252,1344,1438,1538,1631,1726,1819,1910,2001,2081,2181,2281,2377,2479,2579,2678,2828,2924,9393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,556,645,747,824,883,947,1032,1094,1152,1229,1314,1377,1439,1497,1563,1625,1689,1756,1810,1865,1961,2018,2077,2133,2200,2305,2385,2466,2558,2643,2724,2853,2926,2997,3111,3193,3269,3320,3371,3437,3503,3576,3647,3722,3790,3863,3934,4001,4099,4184,4251,4338,4426,4500,4568,4653,4704,4782,4846,4926,5008,5070,5134,5197,5263,5358,5442,5527,5607,5694,5777,5832,5887,5963,6042,6117", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,70,67,74,80,88,101,76,58,63,84,61,57,76,84,62,61,57,65,61,63,66,53,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,83,84,79,86,82,54,54,75,78,74,70", "endOffsets": "256,327,395,470,551,640,742,819,878,942,1027,1089,1147,1224,1309,1372,1434,1492,1558,1620,1684,1751,1805,1860,1956,2013,2072,2128,2195,2300,2380,2461,2553,2638,2719,2848,2921,2992,3106,3188,3264,3315,3366,3432,3498,3571,3642,3717,3785,3858,3929,3996,4094,4179,4246,4333,4421,4495,4563,4648,4699,4777,4841,4921,5003,5065,5129,5192,5258,5353,5437,5522,5602,5689,5772,5827,5882,5958,6037,6112,6183"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,3000,3068,3143,3911,4000,4102,4179,4238,4302,4387,4449,4507,4584,4669,4732,4794,4852,4918,4980,5044,5111,5165,5220,5316,5373,5432,5488,5555,5660,5740,5821,5913,5998,6079,6208,6281,6352,6466,6548,6624,6675,6726,6792,6858,6931,7002,7077,7145,7218,7289,7356,7454,7539,7606,7693,7781,7855,7923,8008,8059,8137,8201,8281,8363,8425,8489,8552,8618,8713,8797,8882,8962,9049,9132,9187,9242,9398,9477,9552", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,70,67,74,80,88,101,76,58,63,84,61,57,76,84,62,61,57,65,61,63,66,53,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,83,84,79,86,82,54,54,75,78,74,70", "endOffsets": "306,2995,3063,3138,3219,3995,4097,4174,4233,4297,4382,4444,4502,4579,4664,4727,4789,4847,4913,4975,5039,5106,5160,5215,5311,5368,5427,5483,5550,5655,5735,5816,5908,5993,6074,6203,6276,6347,6461,6543,6619,6670,6721,6787,6853,6926,6997,7072,7140,7213,7284,7351,7449,7534,7601,7688,7776,7850,7918,8003,8054,8132,8196,8276,8358,8420,8484,8547,8613,8708,8792,8877,8957,9044,9127,9182,9237,9313,9472,9547,9618"}}]}]}