{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3320,3415,3518,3616,3716,3817,3929,10197", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3410,3513,3611,3711,3812,3924,4036,10293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,682,794,874,940,1005,1099,1169,1231,1311,1398,1461,1526,1585,1650,1711,1777,1848,1904,1961,2080,2138,2199,2256,2327,2457,2543,2619,2704,2786,2864,3002,3077,3148,3298,3395,3473,3528,3584,3650,3730,3820,3891,3976,4055,4132,4202,4277,4389,4477,4550,4650,4749,4823,4899,5006,5060,5150,5223,5314,5410,5472,5536,5599,5670,5769,5857,5949,6035,6127,6217,6275,6335,6418,6500,6578", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,77,75,79,78,99,111,79,65,64,93,69,61,79,86,62,64,58,64,60,65,70,55,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,87,91,85,91,89,57,59,82,81,77,75", "endOffsets": "264,342,418,498,577,677,789,869,935,1000,1094,1164,1226,1306,1393,1456,1521,1580,1645,1706,1772,1843,1899,1956,2075,2133,2194,2251,2322,2452,2538,2614,2699,2781,2859,2997,3072,3143,3293,3390,3468,3523,3579,3645,3725,3815,3886,3971,4050,4127,4197,4272,4384,4472,4545,4645,4744,4818,4894,5001,5055,5145,5218,5309,5405,5467,5531,5594,5665,5764,5852,5944,6030,6122,6212,6270,6330,6413,6495,6573,6649"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3161,3241,4041,4141,4253,4333,4399,4464,4558,4628,4690,4770,4857,4920,4985,5044,5109,5170,5236,5307,5363,5420,5539,5597,5658,5715,5786,5916,6002,6078,6163,6245,6323,6461,6536,6607,6757,6854,6932,6987,7043,7109,7189,7279,7350,7435,7514,7591,7661,7736,7848,7936,8009,8109,8208,8282,8358,8465,8519,8609,8682,8773,8869,8931,8995,9058,9129,9228,9316,9408,9494,9586,9676,9734,9794,9961,10043,10121", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,77,75,79,78,99,111,79,65,64,93,69,61,79,86,62,64,58,64,60,65,70,55,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,87,91,85,91,89,57,59,82,81,77,75", "endOffsets": "314,3080,3156,3236,3315,4136,4248,4328,4394,4459,4553,4623,4685,4765,4852,4915,4980,5039,5104,5165,5231,5302,5358,5415,5534,5592,5653,5710,5781,5911,5997,6073,6158,6240,6318,6456,6531,6602,6752,6849,6927,6982,7038,7104,7184,7274,7345,7430,7509,7586,7656,7731,7843,7931,8004,8104,8203,8277,8353,8460,8514,8604,8677,8768,8864,8926,8990,9053,9124,9223,9311,9403,9489,9581,9671,9729,9789,9872,10038,10116,10192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,9877", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,9956"}}]}]}