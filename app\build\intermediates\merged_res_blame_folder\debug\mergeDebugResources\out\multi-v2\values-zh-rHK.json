{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3126,3218,3317,3411,3505,3598,3691,9163", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3213,3312,3406,3500,3593,3686,3782,9259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,8866", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,8940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,515,605,712,785,836,898,976,1035,1093,1167,1245,1306,1363,1419,1478,1536,1595,1656,1708,1762,1848,1904,1962,2016,2081,2174,2248,2320,2400,2474,2552,2672,2735,2798,2897,2974,3048,3098,3149,3215,3279,3347,3418,3490,3551,3622,3689,3749,3837,3917,3980,4063,4148,4222,4287,4363,4411,4485,4549,4625,4703,4765,4829,4892,4958,5038,5112,5188,5263,5339,5416,5470,5525,5594,5669,5742", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,62,60,66,76,89,106,72,50,61,77,58,57,73,77,60,56,55,58,57,58,60,51,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,73,75,74,75,76,53,54,68,74,72,69", "endOffsets": "242,305,366,433,510,600,707,780,831,893,971,1030,1088,1162,1240,1301,1358,1414,1473,1531,1590,1651,1703,1757,1843,1899,1957,2011,2076,2169,2243,2315,2395,2469,2547,2667,2730,2793,2892,2969,3043,3093,3144,3210,3274,3342,3413,3485,3546,3617,3684,3744,3832,3912,3975,4058,4143,4217,4282,4358,4406,4480,4544,4620,4698,4760,4824,4887,4953,5033,5107,5183,5258,5334,5411,5465,5520,5589,5664,5737,5807"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2858,2921,2982,3049,3787,3877,3984,4057,4108,4170,4248,4307,4365,4439,4517,4578,4635,4691,4750,4808,4867,4928,4980,5034,5120,5176,5234,5288,5353,5446,5520,5592,5672,5746,5824,5944,6007,6070,6169,6246,6320,6370,6421,6487,6551,6619,6690,6762,6823,6894,6961,7021,7109,7189,7252,7335,7420,7494,7559,7635,7683,7757,7821,7897,7975,8037,8101,8164,8230,8310,8384,8460,8535,8611,8688,8742,8797,8945,9020,9093", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,62,60,66,76,89,106,72,50,61,77,58,57,73,77,60,56,55,58,57,58,60,51,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,73,75,74,75,76,53,54,68,74,72,69", "endOffsets": "292,2916,2977,3044,3121,3872,3979,4052,4103,4165,4243,4302,4360,4434,4512,4573,4630,4686,4745,4803,4862,4923,4975,5029,5115,5171,5229,5283,5348,5441,5515,5587,5667,5741,5819,5939,6002,6065,6164,6241,6315,6365,6416,6482,6546,6614,6685,6757,6818,6889,6956,7016,7104,7184,7247,7330,7415,7489,7554,7630,7678,7752,7816,7892,7970,8032,8096,8159,8225,8305,8379,8455,8530,8606,8683,8737,8792,8861,9015,9088,9158"}}]}]}