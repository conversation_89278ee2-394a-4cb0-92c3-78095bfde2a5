{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "361,466,566,674,758,860,976,1055,1133,1224,1318,1412,1506,1606,1699,1794,1887,1978,2070,2151,2256,2359,2457,2562,2664,2766,2920,9571", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "461,561,669,753,855,971,1050,1128,1219,1313,1407,1501,1601,1694,1789,1882,1973,2065,2146,2251,2354,2452,2557,2659,2761,2915,3012,9648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,631,724,837,917,977,1042,1130,1200,1263,1339,1431,1494,1554,1613,1676,1737,1801,1869,1923,1977,2079,2136,2195,2249,2317,2428,2509,2584,2671,2751,2833,2965,3036,3109,3233,3321,3397,3450,3504,3570,3643,3719,3790,3868,3938,4013,4095,4163,4264,4349,4419,4509,4600,4674,4747,4836,4887,4968,5035,5117,5202,5264,5328,5391,5459,5553,5634,5724,5807,5889,5978,6035,6093,6168,6250,6325", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,76,74,76,90,92,112,79,59,64,87,69,62,75,91,62,59,58,62,60,63,67,53,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,80,89,82,81,88,56,57,74,81,74,75", "endOffsets": "306,383,458,535,626,719,832,912,972,1037,1125,1195,1258,1334,1426,1489,1549,1608,1671,1732,1796,1864,1918,1972,2074,2131,2190,2244,2312,2423,2504,2579,2666,2746,2828,2960,3031,3104,3228,3316,3392,3445,3499,3565,3638,3714,3785,3863,3933,4008,4090,4158,4259,4344,4414,4504,4595,4669,4742,4831,4882,4963,5030,5112,5197,5259,5323,5386,5454,5548,5629,5719,5802,5884,5973,6030,6088,6163,6245,6320,6396"}, "to": {"startLines": "2,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3017,3094,3169,3246,4034,4127,4240,4320,4380,4445,4533,4603,4666,4742,4834,4897,4957,5016,5079,5140,5204,5272,5326,5380,5482,5539,5598,5652,5720,5831,5912,5987,6074,6154,6236,6368,6439,6512,6636,6724,6800,6853,6907,6973,7046,7122,7193,7271,7341,7416,7498,7566,7667,7752,7822,7912,8003,8077,8150,8239,8290,8371,8438,8520,8605,8667,8731,8794,8862,8956,9037,9127,9210,9292,9381,9438,9496,9653,9735,9810", "endLines": "6,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "endColumns": "12,76,74,76,90,92,112,79,59,64,87,69,62,75,91,62,59,58,62,60,63,67,53,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,80,89,82,81,88,56,57,74,81,74,75", "endOffsets": "356,3089,3164,3241,3332,4122,4235,4315,4375,4440,4528,4598,4661,4737,4829,4892,4952,5011,5074,5135,5199,5267,5321,5375,5477,5534,5593,5647,5715,5826,5907,5982,6069,6149,6231,6363,6434,6507,6631,6719,6795,6848,6902,6968,7041,7117,7188,7266,7336,7411,7493,7561,7662,7747,7817,7907,7998,8072,8145,8234,8285,8366,8433,8515,8600,8662,8726,8789,8857,8951,9032,9122,9205,9287,9376,9433,9491,9566,9730,9805,9881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "38,39,40,41,42,43,44,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3337,3431,3533,3630,3727,3828,3928,9886", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3426,3528,3625,3722,3823,3923,4029,9982"}}]}]}