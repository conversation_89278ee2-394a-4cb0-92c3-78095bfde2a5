{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,8988", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,9062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3171,3263,3363,3457,3553,3646,3739,9289", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3258,3358,3452,3548,3641,3734,3835,9385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,535,620,724,800,853,916,1000,1064,1122,1200,1281,1340,1397,1452,1511,1568,1628,1690,1742,1796,1889,1944,2001,2055,2121,2221,2297,2368,2447,2520,2601,2723,2785,2847,2948,3027,3102,3155,3206,3272,3342,3412,3483,3553,3617,3688,3756,3819,3910,3989,4052,4132,4214,4286,4357,4429,4477,4549,4613,4688,4765,4827,4891,4954,5021,5107,5184,5265,5339,5420,5498,5555,5610,5683,5761,5834", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,66,63,68,81,84,103,75,52,62,83,63,57,77,80,58,56,54,58,56,59,61,51,53,92,54,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,76,80,73,80,77,56,54,72,77,72,70", "endOffsets": "248,315,379,448,530,615,719,795,848,911,995,1059,1117,1195,1276,1335,1392,1447,1506,1563,1623,1685,1737,1791,1884,1939,1996,2050,2116,2216,2292,2363,2442,2515,2596,2718,2780,2842,2943,3022,3097,3150,3201,3267,3337,3407,3478,3548,3612,3683,3751,3814,3905,3984,4047,4127,4209,4281,4352,4424,4472,4544,4608,4683,4760,4822,4886,4949,5016,5102,5179,5260,5334,5415,5493,5550,5605,5678,5756,5829,5900"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2889,2956,3020,3089,3840,3925,4029,4105,4158,4221,4305,4369,4427,4505,4586,4645,4702,4757,4816,4873,4933,4995,5047,5101,5194,5249,5306,5360,5426,5526,5602,5673,5752,5825,5906,6028,6090,6152,6253,6332,6407,6460,6511,6577,6647,6717,6788,6858,6922,6993,7061,7124,7215,7294,7357,7437,7519,7591,7662,7734,7782,7854,7918,7993,8070,8132,8196,8259,8326,8412,8489,8570,8644,8725,8803,8860,8915,9067,9145,9218", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,66,63,68,81,84,103,75,52,62,83,63,57,77,80,58,56,54,58,56,59,61,51,53,92,54,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,76,80,73,80,77,56,54,72,77,72,70", "endOffsets": "298,2951,3015,3084,3166,3920,4024,4100,4153,4216,4300,4364,4422,4500,4581,4640,4697,4752,4811,4868,4928,4990,5042,5096,5189,5244,5301,5355,5421,5521,5597,5668,5747,5820,5901,6023,6085,6147,6248,6327,6402,6455,6506,6572,6642,6712,6783,6853,6917,6988,7056,7119,7210,7289,7352,7432,7514,7586,7657,7729,7777,7849,7913,7988,8065,8127,8191,8254,8321,8407,8484,8565,8639,8720,8798,8855,8910,8983,9140,9213,9284"}}]}]}