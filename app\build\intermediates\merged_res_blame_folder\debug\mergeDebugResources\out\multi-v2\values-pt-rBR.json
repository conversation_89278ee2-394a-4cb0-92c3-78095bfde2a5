{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,10021", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,10102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3393,3490,3592,3691,3791,3898,4008,10346", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3485,3587,3686,3786,3893,4003,4123,10442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,605,706,826,907,967,1031,1123,1202,1262,1342,1432,1496,1567,1630,1705,1769,1840,1916,1972,2026,2153,2211,2273,2327,2406,2547,2634,2710,2805,2886,2968,3107,3190,3274,3413,3500,3580,3636,3687,3753,3827,3907,3978,4061,4134,4211,4280,4354,4456,4544,4621,4714,4810,4884,4964,5061,5113,5197,5263,5350,5438,5500,5564,5627,5695,5804,5904,6008,6106,6207,6306,6366,6421,6498,6581,6658", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,80,77,83,88,100,119,80,59,63,91,78,59,79,89,63,70,62,74,63,70,75,55,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,99,103,97,100,98,59,54,76,82,76,78", "endOffsets": "268,349,427,511,600,701,821,902,962,1026,1118,1197,1257,1337,1427,1491,1562,1625,1700,1764,1835,1911,1967,2021,2148,2206,2268,2322,2401,2542,2629,2705,2800,2881,2963,3102,3185,3269,3408,3495,3575,3631,3682,3748,3822,3902,3973,4056,4129,4206,4275,4349,4451,4539,4616,4709,4805,4879,4959,5056,5108,5192,5258,5345,5433,5495,5559,5622,5690,5799,5899,6003,6101,6202,6301,6361,6416,6493,6576,6653,6732"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,4128,4229,4349,4430,4490,4554,4646,4725,4785,4865,4955,5019,5090,5153,5228,5292,5363,5439,5495,5549,5676,5734,5796,5850,5929,6070,6157,6233,6328,6409,6491,6630,6713,6797,6936,7023,7103,7159,7210,7276,7350,7430,7501,7584,7657,7734,7803,7877,7979,8067,8144,8237,8333,8407,8487,8584,8636,8720,8786,8873,8961,9023,9087,9150,9218,9327,9427,9531,9629,9730,9829,9889,9944,10107,10190,10267", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,80,77,83,88,100,119,80,59,63,91,78,59,79,89,63,70,62,74,63,70,75,55,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,99,103,97,100,98,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3388,4224,4344,4425,4485,4549,4641,4720,4780,4860,4950,5014,5085,5148,5223,5287,5358,5434,5490,5544,5671,5729,5791,5845,5924,6065,6152,6228,6323,6404,6486,6625,6708,6792,6931,7018,7098,7154,7205,7271,7345,7425,7496,7579,7652,7729,7798,7872,7974,8062,8139,8232,8328,8402,8482,8579,8631,8715,8781,8868,8956,9018,9082,9145,9213,9322,9422,9526,9624,9725,9824,9884,9939,10016,10185,10262,10341"}}]}]}