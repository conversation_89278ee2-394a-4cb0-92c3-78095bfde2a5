{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3447,3545,3648,3748,3851,3959,4065,10500", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3540,3643,3743,3846,3954,4060,4177,10596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,10170", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,10251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,616,718,835,921,984,1050,1150,1232,1295,1380,1471,1534,1599,1661,1730,1792,1859,1927,1982,2036,2174,2231,2292,2346,2419,2572,2657,2736,2832,2916,3000,3139,3220,3305,3446,3536,3622,3677,3728,3794,3872,3957,4028,4111,4183,4263,4343,4414,4521,4613,4685,4782,4879,4953,5027,5129,5185,5272,5344,5432,5524,5586,5650,5713,5783,5899,5996,6105,6198,6301,6399,6458,6513,6604,6692,6767", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,87,85,84,86,101,116,85,62,65,99,81,62,84,90,62,64,61,68,61,66,67,54,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,96,108,92,102,97,58,54,90,87,74,80", "endOffsets": "265,353,439,524,611,713,830,916,979,1045,1145,1227,1290,1375,1466,1529,1594,1656,1725,1787,1854,1922,1977,2031,2169,2226,2287,2341,2414,2567,2652,2731,2827,2911,2995,3134,3215,3300,3441,3531,3617,3672,3723,3789,3867,3952,4023,4106,4178,4258,4338,4409,4516,4608,4680,4777,4874,4948,5022,5124,5180,5267,5339,5427,5519,5581,5645,5708,5778,5894,5991,6100,6193,6296,6394,6453,6508,6599,6687,6762,6843"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3189,3275,3360,4182,4284,4401,4487,4550,4616,4716,4798,4861,4946,5037,5100,5165,5227,5296,5358,5425,5493,5548,5602,5740,5797,5858,5912,5985,6138,6223,6302,6398,6482,6566,6705,6786,6871,7012,7102,7188,7243,7294,7360,7438,7523,7594,7677,7749,7829,7909,7980,8087,8179,8251,8348,8445,8519,8593,8695,8751,8838,8910,8998,9090,9152,9216,9279,9349,9465,9562,9671,9764,9867,9965,10024,10079,10256,10344,10419", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,87,85,84,86,101,116,85,62,65,99,81,62,84,90,62,64,61,68,61,66,67,54,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,96,108,92,102,97,58,54,90,87,74,80", "endOffsets": "315,3184,3270,3355,3442,4279,4396,4482,4545,4611,4711,4793,4856,4941,5032,5095,5160,5222,5291,5353,5420,5488,5543,5597,5735,5792,5853,5907,5980,6133,6218,6297,6393,6477,6561,6700,6781,6866,7007,7097,7183,7238,7289,7355,7433,7518,7589,7672,7744,7824,7904,7975,8082,8174,8246,8343,8440,8514,8588,8690,8746,8833,8905,8993,9085,9147,9211,9274,9344,9460,9557,9666,9759,9862,9960,10019,10074,10165,10339,10414,10495"}}]}]}