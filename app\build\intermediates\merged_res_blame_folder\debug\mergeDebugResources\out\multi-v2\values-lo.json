{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,422,525,638,723,827,938,1016,1093,1184,1277,1369,1463,1563,1656,1751,1847,1938,2029,2110,2217,2321,2419,2522,2626,2730,2887,9706", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "417,520,633,718,822,933,1011,1088,1179,1272,1364,1458,1558,1651,1746,1842,1933,2024,2105,2212,2316,2414,2517,2621,2725,2882,2981,9783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,578,693,812,895,962,1028,1117,1186,1245,1324,1419,1485,1550,1608,1673,1734,1800,1870,1924,1984,2090,2151,2211,2269,2340,2459,2545,2622,2712,2797,2879,3022,3097,3173,3304,3394,3472,3527,3582,3648,3717,3791,3862,3941,4014,4091,4160,4230,4327,4412,4487,4580,4673,4747,4816,4910,4962,5045,5112,5196,5280,5342,5406,5469,5539,5638,5724,5819,5904,5997,6086,6145,6204,6283,6368,6445", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,73,70,80,82,114,118,82,66,65,88,68,58,78,94,65,64,57,64,60,65,69,53,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,85,94,84,92,88,58,58,78,84,76,75", "endOffsets": "264,338,409,490,573,688,807,890,957,1023,1112,1181,1240,1319,1414,1480,1545,1603,1668,1729,1795,1865,1919,1979,2085,2146,2206,2264,2335,2454,2540,2617,2707,2792,2874,3017,3092,3168,3299,3389,3467,3522,3577,3643,3712,3786,3857,3936,4009,4086,4155,4225,4322,4407,4482,4575,4668,4742,4811,4905,4957,5040,5107,5191,5275,5337,5401,5464,5534,5633,5719,5814,5899,5992,6081,6140,6199,6278,6363,6440,6516"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2986,3060,3131,3212,4001,4116,4235,4318,4385,4451,4540,4609,4668,4747,4842,4908,4973,5031,5096,5157,5223,5293,5347,5407,5513,5574,5634,5692,5763,5882,5968,6045,6135,6220,6302,6445,6520,6596,6727,6817,6895,6950,7005,7071,7140,7214,7285,7364,7437,7514,7583,7653,7750,7835,7910,8003,8096,8170,8239,8333,8385,8468,8535,8619,8703,8765,8829,8892,8962,9061,9147,9242,9327,9420,9509,9568,9627,9788,9873,9950", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,73,70,80,82,114,118,82,66,65,88,68,58,78,94,65,64,57,64,60,65,69,53,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,85,94,84,92,88,58,58,78,84,76,75", "endOffsets": "314,3055,3126,3207,3290,4111,4230,4313,4380,4446,4535,4604,4663,4742,4837,4903,4968,5026,5091,5152,5218,5288,5342,5402,5508,5569,5629,5687,5758,5877,5963,6040,6130,6215,6297,6440,6515,6591,6722,6812,6890,6945,7000,7066,7135,7209,7280,7359,7432,7509,7578,7648,7745,7830,7905,7998,8091,8165,8234,8328,8380,8463,8530,8614,8698,8760,8824,8887,8957,9056,9142,9237,9322,9415,9504,9563,9622,9701,9868,9945,10021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3295,3391,3494,3593,3691,3792,3890,10026", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3386,3489,3588,3686,3787,3885,3996,10122"}}]}]}