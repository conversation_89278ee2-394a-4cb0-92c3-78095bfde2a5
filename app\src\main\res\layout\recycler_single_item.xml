<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:layout_marginTop="15px"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="8dp">

        <ImageView
            android:id="@+id/item_image"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="8dp">

            <TextView
                android:id="@+id/item_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20px"
                android:text="NAME"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/item_role"
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:text="Role"
                android:textSize="14sp" />
        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
