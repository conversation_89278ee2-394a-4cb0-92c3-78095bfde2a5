{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,22,31,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,204,269,336,406,478,547,616,698,788,864,932,999,1077,1142,1209,1381,1950,2219", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,21,30,35,38", "endColumns": "74,73,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "125,199,264,331,401,473,542,611,693,783,859,927,994,1072,1137,1204,1376,1945,2214,2442"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,25,34,39", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,339,413,478,545,615,687,756,825,907,997,1073,1141,1208,1286,1351,1418,1590,2159,2428", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,24,33,38,41", "endColumns": "74,73,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "334,408,473,540,610,682,751,820,902,992,1068,1136,1203,1281,1346,1413,1585,2154,2423,2651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}]}]}