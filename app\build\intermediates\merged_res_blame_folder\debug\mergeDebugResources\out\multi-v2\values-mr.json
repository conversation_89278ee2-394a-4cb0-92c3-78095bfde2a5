{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,420,526,633,723,824,936,1014,1091,1182,1275,1368,1465,1565,1658,1753,1847,1938,2029,2109,2216,2317,2414,2523,2625,2739,2896,9819", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "415,521,628,718,819,931,1009,1086,1177,1270,1363,1460,1560,1653,1748,1842,1933,2024,2104,2211,2312,2409,2518,2620,2734,2891,2994,9894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,598,698,814,896,953,1016,1107,1172,1231,1312,1400,1462,1524,1584,1651,1714,1780,1851,1907,1961,2075,2132,2193,2247,2317,2436,2517,2594,2683,2765,2850,2985,3062,3139,3280,3366,3450,3506,3558,3624,3694,3772,3843,3925,3995,4071,4142,4211,4325,4421,4495,4593,4689,4763,4833,4935,4990,5078,5145,5232,5325,5388,5452,5515,5581,5681,5772,5866,5955,6053,6153,6213,6269,6347,6431,6509", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,84,86,82,83,99,115,81,56,62,90,64,58,80,87,61,61,59,66,62,65,70,55,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,90,93,88,97,99,59,55,77,83,77,72", "endOffsets": "254,339,426,509,593,693,809,891,948,1011,1102,1167,1226,1307,1395,1457,1519,1579,1646,1709,1775,1846,1902,1956,2070,2127,2188,2242,2312,2431,2512,2589,2678,2760,2845,2980,3057,3134,3275,3361,3445,3501,3553,3619,3689,3767,3838,3920,3990,4066,4137,4206,4320,4416,4490,4588,4684,4758,4828,4930,4985,5073,5140,5227,5320,5383,5447,5510,5576,5676,5767,5861,5950,6048,6148,6208,6264,6342,6426,6504,6577"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2999,3084,3171,3254,4070,4170,4286,4368,4425,4488,4579,4644,4703,4784,4872,4934,4996,5056,5123,5186,5252,5323,5379,5433,5547,5604,5665,5719,5789,5908,5989,6066,6155,6237,6322,6457,6534,6611,6752,6838,6922,6978,7030,7096,7166,7244,7315,7397,7467,7543,7614,7683,7797,7893,7967,8065,8161,8235,8305,8407,8462,8550,8617,8704,8797,8860,8924,8987,9053,9153,9244,9338,9427,9525,9625,9685,9741,9899,9983,10061", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,84,86,82,83,99,115,81,56,62,90,64,58,80,87,61,61,59,66,62,65,70,55,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,90,93,88,97,99,59,55,77,83,77,72", "endOffsets": "304,3079,3166,3249,3333,4165,4281,4363,4420,4483,4574,4639,4698,4779,4867,4929,4991,5051,5118,5181,5247,5318,5374,5428,5542,5599,5660,5714,5784,5903,5984,6061,6150,6232,6317,6452,6529,6606,6747,6833,6917,6973,7025,7091,7161,7239,7310,7392,7462,7538,7609,7678,7792,7888,7962,8060,8156,8230,8300,8402,8457,8545,8612,8699,8792,8855,8919,8982,9048,9148,9239,9333,9422,9520,9620,9680,9736,9814,9978,10056,10129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3338,3438,3542,3643,3746,3848,3953,10134", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3433,3537,3638,3741,3843,3948,4065,10230"}}]}]}