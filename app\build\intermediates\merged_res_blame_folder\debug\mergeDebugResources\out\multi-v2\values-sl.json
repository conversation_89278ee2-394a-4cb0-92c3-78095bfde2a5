{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,10052", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,10131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,724,830,956,1040,1104,1170,1264,1340,1403,1482,1594,1654,1719,1773,1843,1903,1971,2043,2102,2158,2270,2327,2389,2445,2518,2652,2737,2814,2903,2984,3069,3212,3296,3379,3513,3602,3679,3735,3790,3856,3929,4006,4077,4156,4230,4306,4381,4454,4559,4647,4720,4810,4901,4973,5047,5138,5190,5272,5339,5423,5510,5572,5636,5699,5768,5871,5964,6062,6151,6249,6343,6403,6462,6539,6626,6702", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,88,88,87,90,105,125,83,63,65,93,75,62,78,111,59,64,53,69,59,67,71,58,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,92,97,88,97,93,59,58,76,86,75,77", "endOffsets": "362,451,540,628,719,825,951,1035,1099,1165,1259,1335,1398,1477,1589,1649,1714,1768,1838,1898,1966,2038,2097,2153,2265,2322,2384,2440,2513,2647,2732,2809,2898,2979,3064,3207,3291,3374,3508,3597,3674,3730,3785,3851,3924,4001,4072,4151,4225,4301,4376,4449,4554,4642,4715,4805,4896,4968,5042,5133,5185,5267,5334,5418,5505,5567,5631,5694,5763,5866,5959,6057,6146,6244,6338,6398,6457,6534,6621,6697,6775"}, "to": {"startLines": "2,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,4237,4343,4469,4553,4617,4683,4777,4853,4916,4995,5107,5167,5232,5286,5356,5416,5484,5556,5615,5671,5783,5840,5902,5958,6031,6165,6250,6327,6416,6497,6582,6725,6809,6892,7026,7115,7192,7248,7303,7369,7442,7519,7590,7669,7743,7819,7894,7967,8072,8160,8233,8323,8414,8486,8560,8651,8703,8785,8852,8936,9023,9085,9149,9212,9281,9384,9477,9575,9664,9762,9856,9916,9975,10136,10223,10299", "endLines": "7,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "endColumns": "12,88,88,87,90,105,125,83,63,65,93,75,62,78,111,59,64,53,69,59,67,71,58,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,92,97,88,97,93,59,58,76,86,75,77", "endOffsets": "412,3241,3330,3418,3509,4338,4464,4548,4612,4678,4772,4848,4911,4990,5102,5162,5227,5281,5351,5411,5479,5551,5610,5666,5778,5835,5897,5953,6026,6160,6245,6322,6411,6492,6577,6720,6804,6887,7021,7110,7187,7243,7298,7364,7437,7514,7585,7664,7738,7814,7889,7962,8067,8155,8228,8318,8409,8481,8555,8646,8698,8780,8847,8931,9018,9080,9144,9207,9276,9379,9472,9570,9659,9757,9851,9911,9970,10047,10218,10294,10372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3514,3611,3713,3811,3915,4018,4120,10377", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3606,3708,3806,3910,4013,4115,4232,10473"}}]}]}