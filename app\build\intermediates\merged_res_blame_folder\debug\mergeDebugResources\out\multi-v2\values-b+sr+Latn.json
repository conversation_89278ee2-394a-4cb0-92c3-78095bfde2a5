{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3433,3531,3633,3730,3834,3938,4043,10285", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3526,3628,3725,3829,3933,4038,4154,10381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,652,746,878,959,1022,1088,1181,1249,1312,1392,1495,1555,1621,1677,1748,1808,1874,1945,2004,2058,2170,2227,2288,2342,2418,2543,2630,2707,2800,2884,2967,3106,3188,3271,3402,3490,3568,3622,3678,3744,3818,3896,3967,4049,4125,4201,4276,4348,4455,4545,4618,4710,4806,4878,4954,5050,5103,5185,5252,5339,5426,5488,5552,5615,5684,5789,5881,5977,6067,6163,6257,6315,6375,6455,6538,6614", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,76,76,79,93,93,131,80,62,65,92,67,62,79,102,59,65,55,70,59,65,70,58,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,91,95,89,95,93,57,59,79,82,75,76", "endOffsets": "319,396,473,553,647,741,873,954,1017,1083,1176,1244,1307,1387,1490,1550,1616,1672,1743,1803,1869,1940,1999,2053,2165,2222,2283,2337,2413,2538,2625,2702,2795,2879,2962,3101,3183,3266,3397,3485,3563,3617,3673,3739,3813,3891,3962,4044,4120,4196,4271,4343,4450,4540,4613,4705,4801,4873,4949,5045,5098,5180,5247,5334,5421,5483,5547,5610,5679,5784,5876,5972,6062,6158,6252,6310,6370,6450,6533,6609,6686"}, "to": {"startLines": "2,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3105,3182,3259,3339,4159,4253,4385,4466,4529,4595,4688,4756,4819,4899,5002,5062,5128,5184,5255,5315,5381,5452,5511,5565,5677,5734,5795,5849,5925,6050,6137,6214,6307,6391,6474,6613,6695,6778,6909,6997,7075,7129,7185,7251,7325,7403,7474,7556,7632,7708,7783,7855,7962,8052,8125,8217,8313,8385,8461,8557,8610,8692,8759,8846,8933,8995,9059,9122,9191,9296,9388,9484,9574,9670,9764,9822,9882,10049,10132,10208", "endLines": "6,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "endColumns": "12,76,76,79,93,93,131,80,62,65,92,67,62,79,102,59,65,55,70,59,65,70,58,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,91,95,89,95,93,57,59,79,82,75,76", "endOffsets": "369,3177,3254,3334,3428,4248,4380,4461,4524,4590,4683,4751,4814,4894,4997,5057,5123,5179,5250,5310,5376,5447,5506,5560,5672,5729,5790,5844,5920,6045,6132,6209,6302,6386,6469,6608,6690,6773,6904,6992,7070,7124,7180,7246,7320,7398,7469,7551,7627,7703,7778,7850,7957,8047,8120,8212,8308,8380,8456,8552,8605,8687,8754,8841,8928,8990,9054,9117,9186,9291,9383,9479,9569,9665,9759,9817,9877,9957,10127,10203,10280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,481,582,688,774,878,1000,1085,1167,1258,1351,1446,1540,1640,1733,1828,1933,2024,2115,2201,2306,2412,2515,2622,2731,2838,3008,9962", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "476,577,683,769,873,995,1080,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2617,2726,2833,3003,3100,10044"}}]}]}