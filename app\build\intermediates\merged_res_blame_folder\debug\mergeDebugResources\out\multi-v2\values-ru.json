{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,10102", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,10179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,712,809,946,1038,1113,1179,1278,1355,1418,1496,1614,1675,1740,1797,1867,1928,1998,2071,2130,2184,2300,2357,2419,2473,2547,2675,2763,2850,2953,3045,3131,3268,3352,3437,3571,3662,3738,3792,3843,3909,3981,4059,4130,4212,4292,4368,4445,4522,4629,4718,4791,4881,4976,5050,5131,5224,5279,5360,5426,5512,5597,5659,5723,5786,5858,5956,6048,6143,6236,6324,6415,6473,6528,6608,6702,6778", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,77,77,83,90,96,136,91,74,65,98,76,62,77,117,60,64,56,69,60,69,72,58,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,91,94,92,87,90,57,54,79,93,75,78", "endOffsets": "376,454,532,616,707,804,941,1033,1108,1174,1273,1350,1413,1491,1609,1670,1735,1792,1862,1923,1993,2066,2125,2179,2295,2352,2414,2468,2542,2670,2758,2845,2948,3040,3126,3263,3347,3432,3566,3657,3733,3787,3838,3904,3976,4054,4125,4207,4287,4363,4440,4517,4624,4713,4786,4876,4971,5045,5126,5219,5274,5355,5421,5507,5592,5654,5718,5781,5853,5951,6043,6138,6231,6319,6410,6468,6523,6603,6697,6773,6852"}, "to": {"startLines": "2,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3388,4206,4303,4440,4532,4607,4673,4772,4849,4912,4990,5108,5169,5234,5291,5361,5422,5492,5565,5624,5678,5794,5851,5913,5967,6041,6169,6257,6344,6447,6539,6625,6762,6846,6931,7065,7156,7232,7286,7337,7403,7475,7553,7624,7706,7786,7862,7939,8016,8123,8212,8285,8375,8470,8544,8625,8718,8773,8854,8920,9006,9091,9153,9217,9280,9352,9450,9542,9637,9730,9818,9909,9967,10022,10184,10278,10354", "endLines": "7,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "endColumns": "12,77,77,83,90,96,136,91,74,65,98,76,62,77,117,60,64,56,69,60,69,72,58,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,91,94,92,87,90,57,54,79,93,75,78", "endOffsets": "426,3221,3299,3383,3474,4298,4435,4527,4602,4668,4767,4844,4907,4985,5103,5164,5229,5286,5356,5417,5487,5560,5619,5673,5789,5846,5908,5962,6036,6164,6252,6339,6442,6534,6620,6757,6841,6926,7060,7151,7227,7281,7332,7398,7470,7548,7619,7701,7781,7857,7934,8011,8118,8207,8280,8370,8465,8539,8620,8713,8768,8849,8915,9001,9086,9148,9212,9275,9347,9445,9537,9632,9725,9813,9904,9962,10017,10097,10273,10349,10428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3479,3577,3679,3780,3881,3986,4089,10433", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3572,3674,3775,3876,3981,4084,4201,10529"}}]}]}