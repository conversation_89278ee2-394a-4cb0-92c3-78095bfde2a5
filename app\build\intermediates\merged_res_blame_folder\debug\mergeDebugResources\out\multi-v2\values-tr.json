{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,583,679,791,873,933,997,1088,1165,1226,1306,1397,1460,1523,1582,1651,1714,1780,1854,1910,1964,2072,2130,2192,2246,2319,2440,2524,2604,2703,2787,2878,3018,3095,3171,3302,3389,3465,3518,3572,3638,3708,3785,3856,3936,4007,4082,4160,4231,4332,4417,4506,4601,4694,4766,4838,4934,4986,5072,5139,5223,5313,5375,5439,5502,5572,5666,5752,5841,5925,6016,6105,6162,6220,6299,6383,6458", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,74,74,76,90,95,111,81,59,63,90,76,60,79,90,62,62,58,68,62,65,73,55,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,85,88,83,90,88,56,57,78,83,74,73", "endOffsets": "260,335,410,487,578,674,786,868,928,992,1083,1160,1221,1301,1392,1455,1518,1577,1646,1709,1775,1849,1905,1959,2067,2125,2187,2241,2314,2435,2519,2599,2698,2782,2873,3013,3090,3166,3297,3384,3460,3513,3567,3633,3703,3780,3851,3931,4002,4077,4155,4226,4327,4412,4501,4596,4689,4761,4833,4929,4981,5067,5134,5218,5308,5370,5434,5497,5567,5661,5747,5836,5920,6011,6100,6157,6215,6294,6378,6453,6527"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3082,3157,3234,4038,4134,4246,4328,4388,4452,4543,4620,4681,4761,4852,4915,4978,5037,5106,5169,5235,5309,5365,5419,5527,5585,5647,5701,5774,5895,5979,6059,6158,6242,6333,6473,6550,6626,6757,6844,6920,6973,7027,7093,7163,7240,7311,7391,7462,7537,7615,7686,7787,7872,7961,8056,8149,8221,8293,8389,8441,8527,8594,8678,8768,8830,8894,8957,9027,9121,9207,9296,9380,9471,9560,9617,9675,9834,9918,9993", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,74,74,76,90,95,111,81,59,63,90,76,60,79,90,62,62,58,68,62,65,73,55,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,85,88,83,90,88,56,57,78,83,74,73", "endOffsets": "310,3077,3152,3229,3320,4129,4241,4323,4383,4447,4538,4615,4676,4756,4847,4910,4973,5032,5101,5164,5230,5304,5360,5414,5522,5580,5642,5696,5769,5890,5974,6054,6153,6237,6328,6468,6545,6621,6752,6839,6915,6968,7022,7088,7158,7235,7306,7386,7457,7532,7610,7681,7782,7867,7956,8051,8144,8216,8288,8384,8436,8522,8589,8673,8763,8825,8889,8952,9022,9116,9202,9291,9375,9466,9555,9612,9670,9749,9913,9988,10062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3325,3422,3524,3622,3719,3821,3927,10067", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3417,3519,3617,3714,3816,3922,4033,10163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,9754", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,9829"}}]}]}