{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3393,3490,3592,3691,3791,3898,4004,10353", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3485,3587,3686,3786,3893,3999,4120,10449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,10026", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,10107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,612,715,835,916,976,1040,1132,1211,1276,1356,1446,1510,1578,1640,1713,1777,1848,1924,1980,2034,2160,2218,2280,2334,2410,2553,2640,2720,2819,2905,2987,3126,3208,3290,3432,3519,3599,3655,3706,3772,3847,3927,3998,4077,4150,4227,4296,4370,4477,4570,4647,4740,4832,4906,4987,5080,5133,5217,5283,5366,5454,5516,5580,5643,5711,5827,5924,6031,6122,6220,6312,6372,6427,6513,6596,6675", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,80,79,81,95,102,119,80,59,63,91,78,64,79,89,63,67,61,72,63,70,75,55,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,141,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,91,73,80,92,52,83,65,82,87,61,63,62,67,115,96,106,90,97,91,59,54,85,82,78,78", "endOffsets": "268,349,429,511,607,710,830,911,971,1035,1127,1206,1271,1351,1441,1505,1573,1635,1708,1772,1843,1919,1975,2029,2155,2213,2275,2329,2405,2548,2635,2715,2814,2900,2982,3121,3203,3285,3427,3514,3594,3650,3701,3767,3842,3922,3993,4072,4145,4222,4291,4365,4472,4565,4642,4735,4827,4901,4982,5075,5128,5212,5278,5361,5449,5511,5575,5638,5706,5822,5919,6026,6117,6215,6307,6367,6422,6508,6591,6670,6749"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3054,3135,3215,3297,4125,4228,4348,4429,4489,4553,4645,4724,4789,4869,4959,5023,5091,5153,5226,5290,5361,5437,5493,5547,5673,5731,5793,5847,5923,6066,6153,6233,6332,6418,6500,6639,6721,6803,6945,7032,7112,7168,7219,7285,7360,7440,7511,7590,7663,7740,7809,7883,7990,8083,8160,8253,8345,8419,8500,8593,8646,8730,8796,8879,8967,9029,9093,9156,9224,9340,9437,9544,9635,9733,9825,9885,9940,10112,10195,10274", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,80,79,81,95,102,119,80,59,63,91,78,64,79,89,63,67,61,72,63,70,75,55,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,141,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,91,73,80,92,52,83,65,82,87,61,63,62,67,115,96,106,90,97,91,59,54,85,82,78,78", "endOffsets": "318,3130,3210,3292,3388,4223,4343,4424,4484,4548,4640,4719,4784,4864,4954,5018,5086,5148,5221,5285,5356,5432,5488,5542,5668,5726,5788,5842,5918,6061,6148,6228,6327,6413,6495,6634,6716,6798,6940,7027,7107,7163,7214,7280,7355,7435,7506,7585,7658,7735,7804,7878,7985,8078,8155,8248,8340,8414,8495,8588,8641,8725,8791,8874,8962,9024,9088,9151,9219,9335,9432,9539,9630,9728,9820,9880,9935,10021,10190,10269,10348"}}]}]}