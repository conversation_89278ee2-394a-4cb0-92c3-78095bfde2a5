{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3463,3561,3663,3763,3862,3964,4073,10398", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3556,3658,3758,3857,3959,4068,4185,10494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,678,779,900,984,1046,1112,1207,1281,1341,1421,1505,1567,1633,1691,1764,1827,1901,1977,2035,2091,2210,2267,2328,2384,2458,2603,2689,2764,2853,2932,3016,3149,3231,3314,3460,3550,3630,3685,3736,3802,3875,3953,4024,4109,4180,4257,4331,4403,4509,4600,4674,4769,4867,4941,5021,5122,5175,5261,5327,5416,5506,5568,5632,5695,5769,5881,5974,6084,6176,6273,6369,6428,6483,6562,6648,6725", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,91,87,86,89,100,120,83,61,65,94,73,59,79,83,61,65,57,72,62,73,75,57,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,92,109,91,96,95,58,54,78,85,76,78", "endOffsets": "316,408,496,583,673,774,895,979,1041,1107,1202,1276,1336,1416,1500,1562,1628,1686,1759,1822,1896,1972,2030,2086,2205,2262,2323,2379,2453,2598,2684,2759,2848,2927,3011,3144,3226,3309,3455,3545,3625,3680,3731,3797,3870,3948,4019,4104,4175,4252,4326,4398,4504,4595,4669,4764,4862,4936,5016,5117,5170,5256,5322,5411,5501,5563,5627,5690,5764,5876,5969,6079,6171,6268,6364,6423,6478,6557,6643,6720,6799"}, "to": {"startLines": "2,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3106,3198,3286,3373,4190,4291,4412,4496,4558,4624,4719,4793,4853,4933,5017,5079,5145,5203,5276,5339,5413,5489,5547,5603,5722,5779,5840,5896,5970,6115,6201,6276,6365,6444,6528,6661,6743,6826,6972,7062,7142,7197,7248,7314,7387,7465,7536,7621,7692,7769,7843,7915,8021,8112,8186,8281,8379,8453,8533,8634,8687,8773,8839,8928,9018,9080,9144,9207,9281,9393,9486,9596,9688,9785,9881,9940,9995,10156,10242,10319", "endLines": "6,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "endColumns": "12,91,87,86,89,100,120,83,61,65,94,73,59,79,83,61,65,57,72,62,73,75,57,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,92,109,91,96,95,58,54,78,85,76,78", "endOffsets": "366,3193,3281,3368,3458,4286,4407,4491,4553,4619,4714,4788,4848,4928,5012,5074,5140,5198,5271,5334,5408,5484,5542,5598,5717,5774,5835,5891,5965,6110,6196,6271,6360,6439,6523,6656,6738,6821,6967,7057,7137,7192,7243,7309,7382,7460,7531,7616,7687,7764,7838,7910,8016,8107,8181,8276,8374,8448,8528,8629,8682,8768,8834,8923,9013,9075,9139,9202,9276,9388,9481,9591,9683,9780,9876,9935,9990,10069,10237,10314,10393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,494,600,713,797,902,1021,1106,1186,1277,1370,1465,1559,1659,1752,1847,1941,2032,2124,2205,2315,2423,2521,2633,2739,2843,3005,10074", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "489,595,708,792,897,1016,1101,1181,1272,1365,1460,1554,1654,1747,1842,1936,2027,2119,2200,2310,2418,2516,2628,2734,2838,3000,3101,10151"}}]}]}