{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3286,3382,3484,3581,3679,3786,3895,10041", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3377,3479,3576,3674,3781,3890,4008,10137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,9726", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,9801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,584,684,833,911,970,1034,1120,1193,1253,1331,1418,1482,1544,1606,1674,1739,1810,1884,1940,1994,2112,2170,2231,2287,2362,2488,2574,2651,2742,2826,2906,3047,3125,3205,3327,3413,3491,3547,3598,3664,3732,3806,3877,3952,4024,4102,4172,4245,4349,4433,4510,4598,4687,4761,4834,4919,4968,5046,5112,5192,5275,5337,5401,5464,5533,5641,5731,5832,5918,6012,6102,6162,6217,6297,6377,6455", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,77,75,77,79,99,148,77,58,63,85,72,59,77,86,63,61,61,67,64,70,73,55,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,89,100,85,93,89,59,54,79,79,77,76", "endOffsets": "267,345,421,499,579,679,828,906,965,1029,1115,1188,1248,1326,1413,1477,1539,1601,1669,1734,1805,1879,1935,1989,2107,2165,2226,2282,2357,2483,2569,2646,2737,2821,2901,3042,3120,3200,3322,3408,3486,3542,3593,3659,3727,3801,3872,3947,4019,4097,4167,4240,4344,4428,4505,4593,4682,4756,4829,4914,4963,5041,5107,5187,5270,5332,5396,5459,5528,5636,5726,5827,5913,6007,6097,6157,6212,6292,6372,6450,6527"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,4013,4113,4262,4340,4399,4463,4549,4622,4682,4760,4847,4911,4973,5035,5103,5168,5239,5313,5369,5423,5541,5599,5660,5716,5791,5917,6003,6080,6171,6255,6335,6476,6554,6634,6756,6842,6920,6976,7027,7093,7161,7235,7306,7381,7453,7531,7601,7674,7778,7862,7939,8027,8116,8190,8263,8348,8397,8475,8541,8621,8704,8766,8830,8893,8962,9070,9160,9261,9347,9441,9531,9591,9646,9806,9886,9964", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,77,75,77,79,99,148,77,58,63,85,72,59,77,86,63,61,61,67,64,70,73,55,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,89,100,85,93,89,59,54,79,79,77,76", "endOffsets": "317,3047,3123,3201,3281,4108,4257,4335,4394,4458,4544,4617,4677,4755,4842,4906,4968,5030,5098,5163,5234,5308,5364,5418,5536,5594,5655,5711,5786,5912,5998,6075,6166,6250,6330,6471,6549,6629,6751,6837,6915,6971,7022,7088,7156,7230,7301,7376,7448,7526,7596,7669,7773,7857,7934,8022,8111,8185,8258,8343,8392,8470,8536,8616,8699,8761,8825,8888,8957,9065,9155,9256,9342,9436,9526,9586,9641,9721,9881,9959,10036"}}]}]}