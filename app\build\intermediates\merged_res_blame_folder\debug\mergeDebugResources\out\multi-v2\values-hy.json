{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,585,680,810,891,952,1016,1113,1198,1260,1337,1424,1486,1550,1611,1678,1739,1810,1884,1940,1994,2116,2173,2233,2287,2368,2503,2587,2663,2753,2832,2917,3053,3128,3203,3346,3441,3521,3577,3630,3696,3770,3849,3920,4003,4074,4150,4226,4303,4409,4497,4577,4673,4769,4843,4921,5021,5072,5156,5225,5312,5403,5465,5529,5592,5663,5768,5852,5952,6052,6154,6253,6313,6370,6455,6538,6612", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,75,75,79,87,94,129,80,60,63,96,84,61,76,86,61,63,60,66,60,70,73,55,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,83,99,99,101,98,59,56,84,82,73,79", "endOffsets": "260,336,412,492,580,675,805,886,947,1011,1108,1193,1255,1332,1419,1481,1545,1606,1673,1734,1805,1879,1935,1989,2111,2168,2228,2282,2363,2498,2582,2658,2748,2827,2912,3048,3123,3198,3341,3436,3516,3572,3625,3691,3765,3844,3915,3998,4069,4145,4221,4298,4404,4492,4572,4668,4764,4838,4916,5016,5067,5151,5220,5307,5398,5460,5524,5587,5658,5763,5847,5947,6047,6149,6248,6308,6365,6450,6533,6607,6687"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3100,3176,3256,4064,4159,4289,4370,4431,4495,4592,4677,4739,4816,4903,4965,5029,5090,5157,5218,5289,5363,5419,5473,5595,5652,5712,5766,5847,5982,6066,6142,6232,6311,6396,6532,6607,6682,6825,6920,7000,7056,7109,7175,7249,7328,7399,7482,7553,7629,7705,7782,7888,7976,8056,8152,8248,8322,8400,8500,8551,8635,8704,8791,8882,8944,9008,9071,9142,9247,9331,9431,9531,9633,9732,9792,9849,10017,10100,10174", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,75,75,79,87,94,129,80,60,63,96,84,61,76,86,61,63,60,66,60,70,73,55,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,83,99,99,101,98,59,56,84,82,73,79", "endOffsets": "310,3095,3171,3251,3339,4154,4284,4365,4426,4490,4587,4672,4734,4811,4898,4960,5024,5085,5152,5213,5284,5358,5414,5468,5590,5647,5707,5761,5842,5977,6061,6137,6227,6306,6391,6527,6602,6677,6820,6915,6995,7051,7104,7170,7244,7323,7394,7477,7548,7624,7700,7777,7883,7971,8051,8147,8243,8317,8395,8495,8546,8630,8699,8786,8877,8939,9003,9066,9137,9242,9326,9426,9526,9628,9727,9787,9844,9929,10095,10169,10249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3344,3444,3549,3647,3746,3851,3953,10254", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3439,3544,3642,3741,3846,3948,4059,10350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,9934", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,10012"}}]}]}