{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,9846", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,9923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,594,686,798,880,940,1004,1099,1169,1232,1316,1423,1488,1555,1616,1683,1745,1814,1885,1940,1994,2108,2167,2228,2282,2357,2483,2571,2657,2758,2848,2938,3080,3152,3225,3362,3451,3532,3589,3645,3711,3782,3859,3930,4010,4082,4158,4239,4309,4409,4496,4568,4659,4752,4826,4901,4993,5045,5127,5193,5277,5363,5425,5489,5552,5621,5725,5815,5909,5995,6089,6186,6247,6307,6391,6475,6551", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,77,75,78,87,91,111,81,59,63,94,69,62,83,106,64,66,60,66,61,68,70,54,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,89,93,85,93,96,60,59,83,83,75,78", "endOffsets": "268,346,422,501,589,681,793,875,935,999,1094,1164,1227,1311,1418,1483,1550,1611,1678,1740,1809,1880,1935,1989,2103,2162,2223,2277,2352,2478,2566,2652,2753,2843,2933,3075,3147,3220,3357,3446,3527,3584,3640,3706,3777,3854,3925,4005,4077,4153,4234,4304,4404,4491,4563,4654,4747,4821,4896,4988,5040,5122,5188,5272,5358,5420,5484,5547,5616,5720,5810,5904,5990,6084,6181,6242,6302,6386,6470,6546,6625"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3014,3092,3168,3247,4049,4141,4253,4335,4395,4459,4554,4624,4687,4771,4878,4943,5010,5071,5138,5200,5269,5340,5395,5449,5563,5622,5683,5737,5812,5938,6026,6112,6213,6303,6393,6535,6607,6680,6817,6906,6987,7044,7100,7166,7237,7314,7385,7465,7537,7613,7694,7764,7864,7951,8023,8114,8207,8281,8356,8448,8500,8582,8648,8732,8818,8880,8944,9007,9076,9180,9270,9364,9450,9544,9641,9702,9762,9928,10012,10088", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,77,75,78,87,91,111,81,59,63,94,69,62,83,106,64,66,60,66,61,68,70,54,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,89,93,85,93,96,60,59,83,83,75,78", "endOffsets": "318,3087,3163,3242,3330,4136,4248,4330,4390,4454,4549,4619,4682,4766,4873,4938,5005,5066,5133,5195,5264,5335,5390,5444,5558,5617,5678,5732,5807,5933,6021,6107,6208,6298,6388,6530,6602,6675,6812,6901,6982,7039,7095,7161,7232,7309,7380,7460,7532,7608,7689,7759,7859,7946,8018,8109,8202,8276,8351,8443,8495,8577,8643,8727,8813,8875,8939,9002,9071,9175,9265,9359,9445,9539,9636,9697,9757,9841,10007,10083,10162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3335,3430,3532,3634,3737,3841,3938,10167", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3425,3527,3629,3732,3836,3933,4044,10263"}}]}]}