package com.example.fragmentsleam;

import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import java.util.ArrayList;
import java.util.List;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link PinkFragment#newInstance} factory method to
 * create an instance of this fragment.
 */
public class PinkFragment extends Fragment {
    String fromWhere;
    TextView tv;
    RecyclerView recyclerView;
    ItemAdapter itemAdapter;
    List<Item> itemList;

    // TODO: Rename parameter arguments, choose names that match
    // the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
    private static final String ARG_PARAM1 = "param1";
    private static final String ARG_PARAM2 = "param2";

    // TODO: Rename and change types of parameters
    private String mParam1;
    private String mParam2;

    public PinkFragment(String fromWhere) {

        this.fromWhere = fromWhere;
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @param param2 Parameter 2.
     * @return A new instance of fragment PinkFragment.
     */
    // TODO: Rename and change types and number of parameters
    public static PinkFragment newInstance(String param1, String param2) {
        PinkFragment fragment = new PinkFragment("from pink");
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        args.putString(ARG_PARAM2, param2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mParam1 = getArguments().getString(ARG_PARAM1);
            mParam2 = getArguments().getString(ARG_PARAM2);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View v = inflater.inflate(R.layout.fragment_pink, container, false);

        // Set up the "from where" TextView
        tv = v.findViewById(R.id.fromWhere);
        tv.setText("From: " + fromWhere);

        // Initialize RecyclerView
        recyclerView = v.findViewById(R.id.recyclerView);

        // Create data for pink fragment
        createPinkFragmentData();

        // Set up RecyclerView
        setupRecyclerView();

        return v;
    }

    private void createPinkFragmentData() {
        itemList = new ArrayList<>();
        // Using sample data for pink fragment - you can customize this
        itemList.add(new Item("Sample Item 1", "Role 1", android.R.drawable.ic_menu_gallery));
        itemList.add(new Item("Sample Item 2", "Role 2", android.R.drawable.ic_menu_gallery));
        itemList.add(new Item("Sample Item 3", "Role 3", android.R.drawable.ic_menu_gallery));
        itemList.add(new Item("Sample Item 4", "Role 4", android.R.drawable.ic_menu_gallery));
        itemList.add(new Item("Sample Item 5", "Role 5", android.R.drawable.ic_menu_gallery));
    }

    private void setupRecyclerView() {
        // Create adapter with data
        itemAdapter = new ItemAdapter(itemList);

        // Set adapter to RecyclerView
        recyclerView.setAdapter(itemAdapter);

        // Add LayoutManager for RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
    }
}