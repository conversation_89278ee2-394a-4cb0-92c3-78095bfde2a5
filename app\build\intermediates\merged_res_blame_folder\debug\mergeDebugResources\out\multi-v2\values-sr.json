{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,646,740,871,952,1014,1080,1172,1240,1303,1383,1486,1546,1612,1668,1739,1799,1865,1936,1995,2049,2161,2218,2279,2333,2409,2534,2620,2697,2790,2874,2957,3095,3176,3259,3390,3478,3556,3610,3666,3732,3806,3884,3955,4037,4112,4188,4263,4334,4441,4531,4604,4696,4792,4864,4940,5036,5089,5171,5238,5325,5412,5474,5538,5601,5670,5775,5867,5963,6053,6149,6243,6301,6361,6441,6524,6600", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,75,75,79,92,93,130,80,61,65,91,67,62,79,102,59,65,55,70,59,65,70,58,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,91,95,89,95,93,57,59,79,82,75,76", "endOffsets": "316,392,468,548,641,735,866,947,1009,1075,1167,1235,1298,1378,1481,1541,1607,1663,1734,1794,1860,1931,1990,2044,2156,2213,2274,2328,2404,2529,2615,2692,2785,2869,2952,3090,3171,3254,3385,3473,3551,3605,3661,3727,3801,3879,3950,4032,4107,4183,4258,4329,4436,4526,4599,4691,4787,4859,4935,5031,5084,5166,5233,5320,5407,5469,5533,5596,5665,5770,5862,5958,6048,6144,6238,6296,6356,6436,6519,6595,6672"}, "to": {"startLines": "2,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3099,3175,3251,3331,4150,4244,4375,4456,4518,4584,4676,4744,4807,4887,4990,5050,5116,5172,5243,5303,5369,5440,5499,5553,5665,5722,5783,5837,5913,6038,6124,6201,6294,6378,6461,6599,6680,6763,6894,6982,7060,7114,7170,7236,7310,7388,7459,7541,7616,7692,7767,7838,7945,8035,8108,8200,8296,8368,8444,8540,8593,8675,8742,8829,8916,8978,9042,9105,9174,9279,9371,9467,9557,9653,9747,9805,9865,10032,10115,10191", "endLines": "6,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "endColumns": "12,75,75,79,92,93,130,80,61,65,91,67,62,79,102,59,65,55,70,59,65,70,58,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,91,95,89,95,93,57,59,79,82,75,76", "endOffsets": "366,3170,3246,3326,3419,4239,4370,4451,4513,4579,4671,4739,4802,4882,4985,5045,5111,5167,5238,5298,5364,5435,5494,5548,5660,5717,5778,5832,5908,6033,6119,6196,6289,6373,6456,6594,6675,6758,6889,6977,7055,7109,7165,7231,7305,7383,7454,7536,7611,7687,7762,7833,7940,8030,8103,8195,8291,8363,8439,8535,8588,8670,8737,8824,8911,8973,9037,9100,9169,9274,9366,9462,9552,9648,9742,9800,9860,9940,10110,10186,10263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3424,3522,3624,3721,3825,3929,4034,10268", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3517,3619,3716,3820,3924,4029,4145,10364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,478,579,685,771,875,997,1081,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2616,2725,2832,3002,9945", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "473,574,680,766,870,992,1076,1157,1248,1341,1436,1530,1630,1723,1818,1923,2014,2105,2191,2296,2402,2505,2611,2720,2827,2997,3094,10027"}}]}]}