{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,605,704,844,927,991,1057,1152,1237,1299,1390,1478,1540,1609,1672,1745,1808,1878,1952,2009,2063,2184,2241,2303,2357,2434,2571,2656,2736,2835,2921,3003,3138,3219,3300,3447,3538,3628,3683,3734,3800,3873,3953,4024,4104,4179,4256,4325,4402,4507,4595,4684,4777,4870,4944,5024,5118,5169,5253,5319,5403,5491,5553,5617,5680,5748,5863,5965,6071,6168,6286,6399,6458,6513,6593,6678,6757", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,83,80,76,94,98,139,82,63,65,94,84,61,90,87,61,68,62,72,62,69,73,56,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,146,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,101,105,96,117,112,58,54,79,84,78,81", "endOffsets": "263,347,428,505,600,699,839,922,986,1052,1147,1232,1294,1385,1473,1535,1604,1667,1740,1803,1873,1947,2004,2058,2179,2236,2298,2352,2429,2566,2651,2731,2830,2916,2998,3133,3214,3295,3442,3533,3623,3678,3729,3795,3868,3948,4019,4099,4174,4251,4320,4397,4502,4590,4679,4772,4865,4939,5019,5113,5164,5248,5314,5398,5486,5548,5612,5675,5743,5858,5960,6066,6163,6281,6394,6453,6508,6588,6673,6752,6834"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3012,3096,3177,3254,4096,4195,4335,4418,4482,4548,4643,4728,4790,4881,4969,5031,5100,5163,5236,5299,5369,5443,5500,5554,5675,5732,5794,5848,5925,6062,6147,6227,6326,6412,6494,6629,6710,6791,6938,7029,7119,7174,7225,7291,7364,7444,7515,7595,7670,7747,7816,7893,7998,8086,8175,8268,8361,8435,8515,8609,8660,8744,8810,8894,8982,9044,9108,9171,9239,9354,9456,9562,9659,9777,9890,9949,10004,10166,10251,10330", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,83,80,76,94,98,139,82,63,65,94,84,61,90,87,61,68,62,72,62,69,73,56,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,146,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,101,105,96,117,112,58,54,79,84,78,81", "endOffsets": "313,3091,3172,3249,3344,4190,4330,4413,4477,4543,4638,4723,4785,4876,4964,5026,5095,5158,5231,5294,5364,5438,5495,5549,5670,5727,5789,5843,5920,6057,6142,6222,6321,6407,6489,6624,6705,6786,6933,7024,7114,7169,7220,7286,7359,7439,7510,7590,7665,7742,7811,7888,7993,8081,8170,8263,8356,8430,8510,8604,8655,8739,8805,8889,8977,9039,9103,9166,9234,9349,9451,9557,9654,9772,9885,9944,9999,10079,10246,10325,10407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,10084", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,10161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3349,3447,3549,3648,3750,3859,3966,10412", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3442,3544,3643,3745,3854,3961,4091,10508"}}]}]}