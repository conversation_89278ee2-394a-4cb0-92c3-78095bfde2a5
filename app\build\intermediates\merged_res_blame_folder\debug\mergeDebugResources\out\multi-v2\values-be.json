{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,710,807,939,1022,1100,1167,1260,1337,1400,1483,1599,1662,1731,1790,1861,1920,1990,2064,2123,2177,2298,2359,2422,2476,2549,2671,2759,2835,2926,3007,3090,3242,3328,3415,3549,3640,3723,3780,3831,3897,3969,4046,4117,4200,4275,4352,4434,4510,4618,4707,4789,4880,4976,5050,5131,5226,5280,5362,5428,5515,5601,5663,5727,5790,5859,5969,6077,6180,6282,6385,6482,6543,6598,6678,6763,6839", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,76,76,81,91,96,131,82,77,66,92,76,62,82,115,62,68,58,70,58,69,73,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,133,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,107,102,101,102,96,60,54,79,84,75,78", "endOffsets": "377,454,531,613,705,802,934,1017,1095,1162,1255,1332,1395,1478,1594,1657,1726,1785,1856,1915,1985,2059,2118,2172,2293,2354,2417,2471,2544,2666,2754,2830,2921,3002,3085,3237,3323,3410,3544,3635,3718,3775,3826,3892,3964,4041,4112,4195,4270,4347,4429,4505,4613,4702,4784,4875,4971,5045,5126,5221,5275,5357,5423,5510,5596,5658,5722,5785,5854,5964,6072,6175,6277,6380,6477,6538,6593,6673,6758,6834,6913"}, "to": {"startLines": "2,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3162,3239,3316,3398,4221,4318,4450,4533,4611,4678,4771,4848,4911,4994,5110,5173,5242,5301,5372,5431,5501,5575,5634,5688,5809,5870,5933,5987,6060,6182,6270,6346,6437,6518,6601,6753,6839,6926,7060,7151,7234,7291,7342,7408,7480,7557,7628,7711,7786,7863,7945,8021,8129,8218,8300,8391,8487,8561,8642,8737,8791,8873,8939,9026,9112,9174,9238,9301,9370,9480,9588,9691,9793,9896,9993,10054,10109,10271,10356,10432", "endLines": "7,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "endColumns": "12,76,76,81,91,96,131,82,77,66,92,76,62,82,115,62,68,58,70,58,69,73,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,133,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,107,102,101,102,96,60,54,79,84,75,78", "endOffsets": "427,3234,3311,3393,3485,4313,4445,4528,4606,4673,4766,4843,4906,4989,5105,5168,5237,5296,5367,5426,5496,5570,5629,5683,5804,5865,5928,5982,6055,6177,6265,6341,6432,6513,6596,6748,6834,6921,7055,7146,7229,7286,7337,7403,7475,7552,7623,7706,7781,7858,7940,8016,8124,8213,8295,8386,8482,8556,8637,8732,8786,8868,8934,9021,9107,9169,9233,9296,9365,9475,9583,9686,9788,9891,9988,10049,10104,10184,10351,10427,10506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3490,3588,3690,3790,3891,3997,4100,10511", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3583,3685,3785,3886,3992,4095,4216,10607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,10189", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,10266"}}]}]}