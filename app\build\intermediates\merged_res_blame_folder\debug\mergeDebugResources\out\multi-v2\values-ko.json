{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3156,3248,3348,3442,3539,3635,3733,9296", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3243,3343,3437,3534,3630,3728,3828,9392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,526,609,715,790,843,905,986,1048,1105,1179,1266,1326,1384,1442,1501,1558,1617,1679,1731,1785,1880,1936,1993,2047,2113,2217,2292,2364,2445,2523,2600,2721,2786,2851,2951,3030,3105,3155,3206,3272,3336,3406,3477,3548,3616,3687,3759,3829,3922,4002,4076,4156,4238,4310,4375,4447,4495,4568,4632,4707,4784,4846,4910,4973,5040,5124,5198,5278,5353,5430,5508,5562,5617,5689,5766,5839", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,64,63,68,78,82,105,74,52,61,80,61,56,73,86,59,57,57,58,56,58,61,51,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,73,79,74,76,77,53,54,71,76,72,70", "endOffsets": "244,309,373,442,521,604,710,785,838,900,981,1043,1100,1174,1261,1321,1379,1437,1496,1553,1612,1674,1726,1780,1875,1931,1988,2042,2108,2212,2287,2359,2440,2518,2595,2716,2781,2846,2946,3025,3100,3150,3201,3267,3331,3401,3472,3543,3611,3682,3754,3824,3917,3997,4071,4151,4233,4305,4370,4442,4490,4563,4627,4702,4779,4841,4905,4968,5035,5119,5193,5273,5348,5425,5503,5557,5612,5684,5761,5834,5905"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2879,2944,3008,3077,3833,3916,4022,4097,4150,4212,4293,4355,4412,4486,4573,4633,4691,4749,4808,4865,4924,4986,5038,5092,5187,5243,5300,5354,5420,5524,5599,5671,5752,5830,5907,6028,6093,6158,6258,6337,6412,6462,6513,6579,6643,6713,6784,6855,6923,6994,7066,7136,7229,7309,7383,7463,7545,7617,7682,7754,7802,7875,7939,8014,8091,8153,8217,8280,8347,8431,8505,8585,8660,8737,8815,8869,8924,9075,9152,9225", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,64,63,68,78,82,105,74,52,61,80,61,56,73,86,59,57,57,58,56,58,61,51,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,73,79,74,76,77,53,54,71,76,72,70", "endOffsets": "294,2939,3003,3072,3151,3911,4017,4092,4145,4207,4288,4350,4407,4481,4568,4628,4686,4744,4803,4860,4919,4981,5033,5087,5182,5238,5295,5349,5415,5519,5594,5666,5747,5825,5902,6023,6088,6153,6253,6332,6407,6457,6508,6574,6638,6708,6779,6850,6918,6989,7061,7131,7224,7304,7378,7458,7540,7612,7677,7749,7797,7870,7934,8009,8086,8148,8212,8275,8342,8426,8500,8580,8655,8732,8810,8864,8919,8991,9147,9220,9291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,8996", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,9070"}}]}]}