{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,10222", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,10299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3440,3536,3639,3738,3836,3943,4058,10561", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3531,3634,3733,3831,3938,4053,4181,10657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,624,733,851,935,994,1058,1166,1234,1295,1375,1483,1550,1636,1694,1778,1845,1925,2010,2068,2122,2245,2307,2370,2424,2512,2640,2726,2818,2921,3013,3095,3227,3307,3388,3531,3620,3704,3761,3813,3879,3964,4052,4123,4203,4272,4349,4429,4497,4612,4711,4794,4886,4980,5054,5140,5234,5284,5367,5433,5518,5605,5668,5733,5796,5865,5973,6067,6165,6258,6358,6457,6518,6574,6660,6752,6835", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,87,85,83,93,108,117,83,58,63,107,67,60,79,107,66,85,57,83,66,79,84,57,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,142,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,93,97,92,99,98,60,55,85,91,82,81", "endOffsets": "267,355,441,525,619,728,846,930,989,1053,1161,1229,1290,1370,1478,1545,1631,1689,1773,1840,1920,2005,2063,2117,2240,2302,2365,2419,2507,2635,2721,2813,2916,3008,3090,3222,3302,3383,3526,3615,3699,3756,3808,3874,3959,4047,4118,4198,4267,4344,4424,4492,4607,4706,4789,4881,4975,5049,5135,5229,5279,5362,5428,5513,5600,5663,5728,5791,5860,5968,6062,6160,6253,6353,6452,6513,6569,6655,6747,6830,6912"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3088,3176,3262,3346,4186,4295,4413,4497,4556,4620,4728,4796,4857,4937,5045,5112,5198,5256,5340,5407,5487,5572,5630,5684,5807,5869,5932,5986,6074,6202,6288,6380,6483,6575,6657,6789,6869,6950,7093,7182,7266,7323,7375,7441,7526,7614,7685,7765,7834,7911,7991,8059,8174,8273,8356,8448,8542,8616,8702,8796,8846,8929,8995,9080,9167,9230,9295,9358,9427,9535,9629,9727,9820,9920,10019,10080,10136,10304,10396,10479", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,87,85,83,93,108,117,83,58,63,107,67,60,79,107,66,85,57,83,66,79,84,57,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,142,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,93,97,92,99,98,60,55,85,91,82,81", "endOffsets": "317,3171,3257,3341,3435,4290,4408,4492,4551,4615,4723,4791,4852,4932,5040,5107,5193,5251,5335,5402,5482,5567,5625,5679,5802,5864,5927,5981,6069,6197,6283,6375,6478,6570,6652,6784,6864,6945,7088,7177,7261,7318,7370,7436,7521,7609,7680,7760,7829,7906,7986,8054,8169,8268,8351,8443,8537,8611,8697,8791,8841,8924,8990,9075,9162,9225,9290,9353,9422,9530,9624,9722,9815,9915,10014,10075,10131,10217,10391,10474,10556"}}]}]}