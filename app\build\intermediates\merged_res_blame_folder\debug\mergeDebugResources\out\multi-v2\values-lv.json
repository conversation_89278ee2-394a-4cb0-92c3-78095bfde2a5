{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,665,766,900,983,1044,1109,1203,1276,1337,1421,1546,1612,1680,1741,1813,1873,1944,2017,2075,2129,2249,2309,2371,2425,2502,2632,2719,2796,2886,2969,3051,3192,3272,3357,3484,3575,3651,3705,3758,3824,3898,3979,4050,4130,4203,4280,4357,4431,4541,4634,4709,4799,4890,4962,5040,5131,5185,5268,5336,5420,5507,5569,5633,5696,5768,5878,5973,6076,6167,6261,6351,6409,6466,6543,6628,6706", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,86,84,80,87,100,133,82,60,64,93,72,60,83,124,65,67,60,71,59,70,72,57,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,94,102,90,93,89,57,56,76,84,77,73", "endOffsets": "319,406,491,572,660,761,895,978,1039,1104,1198,1271,1332,1416,1541,1607,1675,1736,1808,1868,1939,2012,2070,2124,2244,2304,2366,2420,2497,2627,2714,2791,2881,2964,3046,3187,3267,3352,3479,3570,3646,3700,3753,3819,3893,3974,4045,4125,4198,4275,4352,4426,4536,4629,4704,4794,4885,4957,5035,5126,5180,5263,5331,5415,5502,5564,5628,5691,5763,5873,5968,6071,6162,6256,6346,6404,6461,6538,6623,6701,6775"}, "to": {"startLines": "2,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3258,3345,3430,3511,4330,4431,4565,4648,4709,4774,4868,4941,5002,5086,5211,5277,5345,5406,5478,5538,5609,5682,5740,5794,5914,5974,6036,6090,6167,6297,6384,6461,6551,6634,6716,6857,6937,7022,7149,7240,7316,7370,7423,7489,7563,7644,7715,7795,7868,7945,8022,8096,8206,8299,8374,8464,8555,8627,8705,8796,8850,8933,9001,9085,9172,9234,9298,9361,9433,9543,9638,9741,9832,9926,10016,10074,10131,10291,10376,10454", "endLines": "6,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121", "endColumns": "12,86,84,80,87,100,133,82,60,64,93,72,60,83,124,65,67,60,71,59,70,72,57,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,94,102,90,93,89,57,56,76,84,77,73", "endOffsets": "369,3340,3425,3506,3594,4426,4560,4643,4704,4769,4863,4936,4997,5081,5206,5272,5340,5401,5473,5533,5604,5677,5735,5789,5909,5969,6031,6085,6162,6292,6379,6456,6546,6629,6711,6852,6932,7017,7144,7235,7311,7365,7418,7484,7558,7639,7710,7790,7863,7940,8017,8091,8201,8294,8369,8459,8550,8622,8700,8791,8845,8928,8996,9080,9167,9229,9293,9356,9428,9538,9633,9736,9827,9921,10011,10069,10126,10203,10371,10449,10523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,494,604,713,799,903,1025,1107,1187,1297,1405,1511,1620,1731,1834,1946,2053,2158,2258,2343,2452,2563,2662,2773,2880,2985,3159,10208", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "489,599,708,794,898,1020,1102,1182,1292,1400,1506,1615,1726,1829,1941,2048,2153,2253,2338,2447,2558,2657,2768,2875,2980,3154,3253,10286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3599,3697,3799,3899,4000,4107,4215,10528", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3692,3794,3894,3995,4102,4210,4325,10624"}}]}]}