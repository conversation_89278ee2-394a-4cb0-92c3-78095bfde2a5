{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,10004", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,10081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,629,730,852,933,995,1061,1155,1225,1284,1366,1474,1540,1609,1667,1739,1803,1870,1940,1995,2049,2177,2237,2299,2353,2431,2568,2660,2738,2832,2918,3002,3147,3231,3317,3450,3540,3619,3676,3727,3793,3867,3949,4020,4095,4169,4247,4319,4393,4503,4595,4677,4766,4855,4929,5007,5093,5148,5227,5294,5374,5458,5520,5584,5647,5716,5823,5916,6015,6107,6203,6298,6359,6414,6496,6579,6656", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,90,88,83,81,100,121,80,61,65,93,69,58,81,107,65,68,57,71,63,66,69,54,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,92,98,91,95,94,60,54,81,82,76,75", "endOffsets": "278,369,458,542,624,725,847,928,990,1056,1150,1220,1279,1361,1469,1535,1604,1662,1734,1798,1865,1935,1990,2044,2172,2232,2294,2348,2426,2563,2655,2733,2827,2913,2997,3142,3226,3312,3445,3535,3614,3671,3722,3788,3862,3944,4015,4090,4164,4242,4314,4388,4498,4590,4672,4761,4850,4924,5002,5088,5143,5222,5289,5369,5453,5515,5579,5642,5711,5818,5911,6010,6102,6198,6293,6354,6409,6491,6574,6651,6727"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,4137,4238,4360,4441,4503,4569,4663,4733,4792,4874,4982,5048,5117,5175,5247,5311,5378,5448,5503,5557,5685,5745,5807,5861,5939,6076,6168,6246,6340,6426,6510,6655,6739,6825,6958,7048,7127,7184,7235,7301,7375,7457,7528,7603,7677,7755,7827,7901,8011,8103,8185,8274,8363,8437,8515,8601,8656,8735,8802,8882,8966,9028,9092,9155,9224,9331,9424,9523,9615,9711,9806,9867,9922,10086,10169,10246", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,90,88,83,81,100,121,80,61,65,93,69,58,81,107,65,68,57,71,63,66,69,54,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,92,98,91,95,94,60,54,81,82,76,75", "endOffsets": "328,3146,3235,3319,3401,4233,4355,4436,4498,4564,4658,4728,4787,4869,4977,5043,5112,5170,5242,5306,5373,5443,5498,5552,5680,5740,5802,5856,5934,6071,6163,6241,6335,6421,6505,6650,6734,6820,6953,7043,7122,7179,7230,7296,7370,7452,7523,7598,7672,7750,7822,7896,8006,8098,8180,8269,8358,8432,8510,8596,8651,8730,8797,8877,8961,9023,9087,9150,9219,9326,9419,9518,9610,9706,9801,9862,9917,9999,10164,10241,10317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3406,3504,3606,3706,3806,3914,4019,10322", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3499,3601,3701,3801,3909,4014,4132,10418"}}]}]}