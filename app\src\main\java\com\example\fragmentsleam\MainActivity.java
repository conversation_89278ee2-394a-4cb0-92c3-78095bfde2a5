package com.example.fragmentsleam;

import android.os.Bundle;
import android.widget.Button;
import android.widget.FrameLayout;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

public class MainActivity extends AppCompatActivity {
    Button frg1, frg2;
    FrameLayout frame;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        frg1 = findViewById(R.id.frrg1);
        frg2 = findViewById(R.id.frrg2);
        frame = findViewById(R.id.frame);

        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.frame, new BlueFragment())
                .commit();

        frg1.setOnClickListener(v -> {
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.frame, new BlueFragment())
                    .commit();

        });
        frg2.setOnClickListener(v -> {
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.frame, new RedFragment())
                    .commit();

        });
        // frg3 button doesn't exist in the layout, so commenting out this code
        // frg3.setOnClickListener(v -> {
        //     getSupportFragmentManager()
        //             .beginTransaction()
        //             .replace(R.id.frame, new PinkFragment("from activity"))
        //             .commit();
        // });

    }
}