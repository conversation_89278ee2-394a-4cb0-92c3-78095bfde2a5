{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,10073", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,10154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,645,751,880,965,1028,1093,1183,1258,1317,1397,1488,1551,1616,1675,1746,1808,1881,1953,2011,2068,2187,2245,2306,2361,2434,2566,2657,2741,2841,2927,3016,3157,3235,3312,3435,3527,3604,3662,3713,3779,3851,3933,4004,4082,4157,4231,4303,4382,4490,4587,4668,4754,4846,4920,4999,5085,5139,5215,5283,5366,5447,5509,5573,5636,5704,5816,5917,6021,6118,6222,6322,6383,6438,6520,6607,6687", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,100,98,75,83,105,128,84,62,64,89,74,58,79,90,62,64,58,70,61,72,71,57,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,100,103,96,103,99,60,54,81,86,79,77", "endOffsets": "280,381,480,556,640,746,875,960,1023,1088,1178,1253,1312,1392,1483,1546,1611,1670,1741,1803,1876,1948,2006,2063,2182,2240,2301,2356,2429,2561,2652,2736,2836,2922,3011,3152,3230,3307,3430,3522,3599,3657,3708,3774,3846,3928,3999,4077,4152,4226,4298,4377,4485,4582,4663,4749,4841,4915,4994,5080,5134,5210,5278,5361,5442,5504,5568,5631,5699,5811,5912,6016,6113,6217,6317,6378,6433,6515,6602,6682,6760"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3096,3197,3296,3372,4198,4304,4433,4518,4581,4646,4736,4811,4870,4950,5041,5104,5169,5228,5299,5361,5434,5506,5564,5621,5740,5798,5859,5914,5987,6119,6210,6294,6394,6480,6569,6710,6788,6865,6988,7080,7157,7215,7266,7332,7404,7486,7557,7635,7710,7784,7856,7935,8043,8140,8221,8307,8399,8473,8552,8638,8692,8768,8836,8919,9000,9062,9126,9189,9257,9369,9470,9574,9671,9775,9875,9936,9991,10159,10246,10326", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,100,98,75,83,105,128,84,62,64,89,74,58,79,90,62,64,58,70,61,72,71,57,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,100,103,96,103,99,60,54,81,86,79,77", "endOffsets": "330,3192,3291,3367,3451,4299,4428,4513,4576,4641,4731,4806,4865,4945,5036,5099,5164,5223,5294,5356,5429,5501,5559,5616,5735,5793,5854,5909,5982,6114,6205,6289,6389,6475,6564,6705,6783,6860,6983,7075,7152,7210,7261,7327,7399,7481,7552,7630,7705,7779,7851,7930,8038,8135,8216,8302,8394,8468,8547,8633,8687,8763,8831,8914,8995,9057,9121,9184,9252,9364,9465,9569,9666,9770,9870,9931,9986,10068,10241,10321,10399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3456,3559,3663,3766,3868,3973,4079,10404", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3554,3658,3761,3863,3968,4074,4193,10500"}}]}]}