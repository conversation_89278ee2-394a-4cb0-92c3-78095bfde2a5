{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,9990", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,10068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,571,670,799,882,947,1015,1107,1180,1243,1321,1407,1469,1532,1597,1665,1728,1800,1874,1932,1986,2118,2175,2237,2291,2365,2503,2584,2664,2767,2851,2931,3063,3148,3235,3376,3464,3543,3597,3650,3716,3788,3870,3941,4026,4098,4173,4244,4317,4423,4520,4594,4689,4786,4860,4945,5045,5098,5183,5251,5339,5429,5491,5555,5618,5685,5802,5904,6015,6116,6221,6325,6383,6440,6521,6606,6687", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,75,73,72,88,98,128,82,64,67,91,72,62,77,85,61,62,64,67,62,71,73,57,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,101,110,100,104,103,57,56,80,84,80,79", "endOffsets": "254,330,404,477,566,665,794,877,942,1010,1102,1175,1238,1316,1402,1464,1527,1592,1660,1723,1795,1869,1927,1981,2113,2170,2232,2286,2360,2498,2579,2659,2762,2846,2926,3058,3143,3230,3371,3459,3538,3592,3645,3711,3783,3865,3936,4021,4093,4168,4239,4312,4418,4515,4589,4684,4781,4855,4940,5040,5093,5178,5246,5334,5424,5486,5550,5613,5680,5797,5899,6010,6111,6216,6320,6378,6435,6516,6601,6682,6762"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3005,3081,3155,3228,4040,4139,4268,4351,4416,4484,4576,4649,4712,4790,4876,4938,5001,5066,5134,5197,5269,5343,5401,5455,5587,5644,5706,5760,5834,5972,6053,6133,6236,6320,6400,6532,6617,6704,6845,6933,7012,7066,7119,7185,7257,7339,7410,7495,7567,7642,7713,7786,7892,7989,8063,8158,8255,8329,8414,8514,8567,8652,8720,8808,8898,8960,9024,9087,9154,9271,9373,9484,9585,9690,9794,9852,9909,10073,10158,10239", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,75,73,72,88,98,128,82,64,67,91,72,62,77,85,61,62,64,67,62,71,73,57,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,101,110,100,104,103,57,56,80,84,80,79", "endOffsets": "304,3076,3150,3223,3312,4134,4263,4346,4411,4479,4571,4644,4707,4785,4871,4933,4996,5061,5129,5192,5264,5338,5396,5450,5582,5639,5701,5755,5829,5967,6048,6128,6231,6315,6395,6527,6612,6699,6840,6928,7007,7061,7114,7180,7252,7334,7405,7490,7562,7637,7708,7781,7887,7984,8058,8153,8250,8324,8409,8509,8562,8647,8715,8803,8893,8955,9019,9082,9149,9266,9368,9479,9580,9685,9789,9847,9904,9985,10153,10234,10314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3317,3411,3513,3610,3711,3818,3925,10319", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3406,3508,3605,3706,3813,3920,4035,10415"}}]}]}