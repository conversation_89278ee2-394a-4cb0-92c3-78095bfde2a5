{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-32:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0052189a63599730bae39f6d1c42be05\\transformed\\appcompat-1.7.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,443,555,668,758,863,982,1060,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2163,2277,2381,2480,2595,2700,2815,2977,10078", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "438,550,663,753,858,977,1055,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2158,2272,2376,2475,2590,2695,2810,2972,3075,10156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\144ebe07efd2e937ae1b0213dd1f189c\\transformed\\material-1.13.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,276,365,455,541,628,732,848,939,1000,1066,1160,1227,1289,1372,1465,1529,1597,1660,1734,1799,1873,1949,2005,2059,2180,2237,2299,2353,2432,2560,2648,2729,2827,2910,3002,3147,3227,3309,3434,3522,3604,3664,3716,3782,3857,3935,4006,4085,4158,4234,4315,4384,4504,4609,4686,4777,4870,4944,5021,5113,5170,5251,5317,5401,5487,5550,5615,5679,5748,5858,5949,6048,6138,6230,6321,6385,6441,6524,6621,6699", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,88,89,85,86,103,115,90,60,65,93,66,61,82,92,63,67,62,73,64,73,75,55,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,90,98,89,91,90,63,55,82,96,77,73", "endOffsets": "271,360,450,536,623,727,843,934,995,1061,1155,1222,1284,1367,1460,1524,1592,1655,1729,1794,1868,1944,2000,2054,2175,2232,2294,2348,2427,2555,2643,2724,2822,2905,2997,3142,3222,3304,3429,3517,3599,3659,3711,3777,3852,3930,4001,4080,4153,4229,4310,4379,4499,4604,4681,4772,4865,4939,5016,5108,5165,5246,5312,5396,5482,5545,5610,5674,5743,5853,5944,6043,6133,6225,6316,6380,6436,6519,6616,6694,6768"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3169,3259,3345,4182,4286,4402,4493,4554,4620,4714,4781,4843,4926,5019,5083,5151,5214,5288,5353,5427,5503,5559,5613,5734,5791,5853,5907,5986,6114,6202,6283,6381,6464,6556,6701,6781,6863,6988,7076,7158,7218,7270,7336,7411,7489,7560,7639,7712,7788,7869,7938,8058,8163,8240,8331,8424,8498,8575,8667,8724,8805,8871,8955,9041,9104,9169,9233,9302,9412,9503,9602,9692,9784,9875,9939,9995,10161,10258,10336", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,88,89,85,86,103,115,90,60,65,93,66,61,82,92,63,67,62,73,64,73,75,55,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,90,98,89,91,90,63,55,82,96,77,73", "endOffsets": "321,3164,3254,3340,3427,4281,4397,4488,4549,4615,4709,4776,4838,4921,5014,5078,5146,5209,5283,5348,5422,5498,5554,5608,5729,5786,5848,5902,5981,6109,6197,6278,6376,6459,6551,6696,6776,6858,6983,7071,7153,7213,7265,7331,7406,7484,7555,7634,7707,7783,7864,7933,8053,8158,8235,8326,8419,8493,8570,8662,8719,8800,8866,8950,9036,9099,9164,9228,9297,9407,9498,9597,9687,9779,9870,9934,9990,10073,10253,10331,10405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57d25f5517be61de28471c628c095146\\transformed\\core-1.13.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3432,3534,3642,3744,3845,3951,4058,10410", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3529,3637,3739,3840,3946,4053,4177,10506"}}]}]}