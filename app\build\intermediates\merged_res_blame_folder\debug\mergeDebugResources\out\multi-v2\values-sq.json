{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3447,3549,3647,3744,3852,3963,10288", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3442,3544,3642,3739,3847,3958,4080,10384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,602,703,829,912,977,1042,1142,1212,1271,1353,1451,1513,1577,1636,1708,1771,1844,1921,1977,2031,2148,2205,2267,2321,2393,2528,2611,2690,2786,2869,2947,3088,3172,3254,3402,3492,3570,3623,3682,3748,3819,3898,3969,4052,4128,4206,4278,4351,4455,4544,4616,4710,4809,4883,4955,5056,5106,5191,5257,5347,5436,5498,5562,5625,5692,5808,5899,6008,6095,6193,6283,6340,6403,6486,6571,6645", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,78,77,85,91,100,125,82,64,64,99,69,58,81,97,61,63,58,71,62,72,76,55,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,90,108,86,97,89,56,62,82,84,73,77", "endOffsets": "262,341,419,505,597,698,824,907,972,1037,1137,1207,1266,1348,1446,1508,1572,1631,1703,1766,1839,1916,1972,2026,2143,2200,2262,2316,2388,2523,2606,2685,2781,2864,2942,3083,3167,3249,3397,3487,3565,3618,3677,3743,3814,3893,3964,4047,4123,4201,4273,4346,4450,4539,4611,4705,4804,4878,4950,5051,5101,5186,5252,5342,5431,5493,5557,5620,5687,5803,5894,6003,6090,6188,6278,6335,6398,6481,6566,6640,6718"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3013,3092,3170,3256,4085,4186,4312,4395,4460,4525,4625,4695,4754,4836,4934,4996,5060,5119,5191,5254,5327,5404,5460,5514,5631,5688,5750,5804,5876,6011,6094,6173,6269,6352,6430,6571,6655,6737,6885,6975,7053,7106,7165,7231,7302,7381,7452,7535,7611,7689,7761,7834,7938,8027,8099,8193,8292,8366,8438,8539,8589,8674,8740,8830,8919,8981,9045,9108,9175,9291,9382,9491,9578,9676,9766,9823,9886,10051,10136,10210", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,78,77,85,91,100,125,82,64,64,99,69,58,81,97,61,63,58,71,62,72,76,55,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,90,108,86,97,89,56,62,82,84,73,77", "endOffsets": "312,3087,3165,3251,3343,4181,4307,4390,4455,4520,4620,4690,4749,4831,4929,4991,5055,5114,5186,5249,5322,5399,5455,5509,5626,5683,5745,5799,5871,6006,6089,6168,6264,6347,6425,6566,6650,6732,6880,6970,7048,7101,7160,7226,7297,7376,7447,7530,7606,7684,7756,7829,7933,8022,8094,8188,8287,8361,8433,8534,8584,8669,8735,8825,8914,8976,9040,9103,9170,9286,9377,9486,9573,9671,9761,9818,9881,9964,10131,10205,10283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,9969", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,10046"}}]}]}