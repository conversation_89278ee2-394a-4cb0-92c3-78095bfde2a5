[{"key": "androidx/fragment/app/FragmentContainerView$Api20Impl.class", "name": "androidx/fragment/app/FragmentContainerView$Api20Impl.class", "size": 1809, "crc": 84557025}, {"key": "androidx/fragment/app/FragmentContainerView.class", "name": "androidx/fragment/app/FragmentContainerView.class", "size": 13462, "crc": 2045222957}, {"key": "androidx/fragment/app/strictmode/FragmentReuseViolation.class", "name": "androidx/fragment/app/strictmode/FragmentReuseViolation.class", "size": 1580, "crc": -12463999}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$Flag.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$Flag.class", "size": 2098, "crc": 1471650558}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$OnViolationListener.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$OnViolationListener.class", "size": 826, "crc": 1282360019}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy$Builder.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy$Builder.class", "size": 5785, "crc": 1494803448}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy$Companion.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy$Companion.class", "size": 1067, "crc": 1302852397}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy.class", "size": 4307, "crc": 1411146223}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode.class", "size": 12102, "crc": -1743595643}, {"key": "androidx/fragment/app/strictmode/FragmentTagUsageViolation.class", "name": "androidx/fragment/app/strictmode/FragmentTagUsageViolation.class", "size": 1727, "crc": 1471548116}, {"key": "androidx/fragment/app/strictmode/GetRetainInstanceUsageViolation.class", "name": "androidx/fragment/app/strictmode/GetRetainInstanceUsageViolation.class", "size": 1384, "crc": 1042345824}, {"key": "androidx/fragment/app/strictmode/GetTargetFragmentRequestCodeUsageViolation.class", "name": "androidx/fragment/app/strictmode/GetTargetFragmentRequestCodeUsageViolation.class", "size": 1422, "crc": -327637220}, {"key": "androidx/fragment/app/strictmode/GetTargetFragmentUsageViolation.class", "name": "androidx/fragment/app/strictmode/GetTargetFragmentUsageViolation.class", "size": 1385, "crc": -2006954663}, {"key": "androidx/fragment/app/strictmode/RetainInstanceUsageViolation.class", "name": "androidx/fragment/app/strictmode/RetainInstanceUsageViolation.class", "size": 1316, "crc": 1557008343}, {"key": "androidx/fragment/app/strictmode/SetRetainInstanceUsageViolation.class", "name": "androidx/fragment/app/strictmode/SetRetainInstanceUsageViolation.class", "size": 1384, "crc": -739357861}, {"key": "androidx/fragment/app/strictmode/SetTargetFragmentUsageViolation.class", "name": "androidx/fragment/app/strictmode/SetTargetFragmentUsageViolation.class", "size": 2004, "crc": 2064861573}, {"key": "androidx/fragment/app/strictmode/SetUserVisibleHintViolation.class", "name": "androidx/fragment/app/strictmode/SetUserVisibleHintViolation.class", "size": 1573, "crc": 601363892}, {"key": "androidx/fragment/app/strictmode/TargetFragmentUsageViolation.class", "name": "androidx/fragment/app/strictmode/TargetFragmentUsageViolation.class", "size": 1316, "crc": -2035091379}, {"key": "androidx/fragment/app/strictmode/Violation.class", "name": "androidx/fragment/app/strictmode/Violation.class", "size": 1523, "crc": 1168552908}, {"key": "androidx/fragment/app/strictmode/WrongFragmentContainerViolation.class", "name": "androidx/fragment/app/strictmode/WrongFragmentContainerViolation.class", "size": 1725, "crc": -1204876313}, {"key": "androidx/fragment/app/BackStackRecord.class", "name": "androidx/fragment/app/BackStackRecord.class", "size": 16232, "crc": 1420312174}, {"key": "androidx/fragment/app/BackStackRecordState$1.class", "name": "androidx/fragment/app/BackStackRecordState$1.class", "size": 1219, "crc": 1774290043}, {"key": "androidx/fragment/app/BackStackRecordState.class", "name": "androidx/fragment/app/BackStackRecordState.class", "size": 7485, "crc": -561643328}, {"key": "androidx/fragment/app/BackStackState$1.class", "name": "androidx/fragment/app/BackStackState$1.class", "size": 1177, "crc": 188052553}, {"key": "androidx/fragment/app/BackStackState.class", "name": "androidx/fragment/app/BackStackState.class", "size": 4819, "crc": 387889}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$1.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$1.class", "size": 1423, "crc": -609461971}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$10.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$10.class", "size": 1151, "crc": -1667665756}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$2.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$2.class", "size": 2579, "crc": -1500403963}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$3.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$3.class", "size": 1875, "crc": -1513594520}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$4$1.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$4$1.class", "size": 1329, "crc": 1417708369}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$4.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$4.class", "size": 2771, "crc": -1042202268}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$5.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$5.class", "size": 2384, "crc": 32972262}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$6.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$6.class", "size": 1743, "crc": 505954056}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$7.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$7.class", "size": 1526, "crc": -2076110502}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$8.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$8.class", "size": 1293, "crc": 1626590668}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$9.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$9.class", "size": 2054, "crc": 34844560}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$AnimationInfo.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$AnimationInfo.class", "size": 2559, "crc": 1477451912}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$SpecialEffectsInfo.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$SpecialEffectsInfo.class", "size": 2281, "crc": 211544840}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionInfo.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionInfo.class", "size": 4489, "crc": -1539940027}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController.class", "size": 25700, "crc": -2035179745}, {"key": "androidx/fragment/app/DialogFragment$1.class", "name": "androidx/fragment/app/DialogFragment$1.class", "size": 1153, "crc": 938399057}, {"key": "androidx/fragment/app/DialogFragment$2.class", "name": "androidx/fragment/app/DialogFragment$2.class", "size": 1177, "crc": -1141424293}, {"key": "androidx/fragment/app/DialogFragment$3.class", "name": "androidx/fragment/app/DialogFragment$3.class", "size": 1180, "crc": -703508033}, {"key": "androidx/fragment/app/DialogFragment$4.class", "name": "androidx/fragment/app/DialogFragment$4.class", "size": 2272, "crc": -1462123626}, {"key": "androidx/fragment/app/DialogFragment$5.class", "name": "androidx/fragment/app/DialogFragment$5.class", "size": 1170, "crc": 519057648}, {"key": "androidx/fragment/app/DialogFragment.class", "name": "androidx/fragment/app/DialogFragment.class", "size": 14607, "crc": 835216558}, {"key": "androidx/fragment/app/Fragment$1.class", "name": "androidx/fragment/app/Fragment$1.class", "size": 649, "crc": -758071550}, {"key": "androidx/fragment/app/Fragment$10.class", "name": "androidx/fragment/app/Fragment$10.class", "size": 2454, "crc": -1780621174}, {"key": "androidx/fragment/app/Fragment$2.class", "name": "androidx/fragment/app/Fragment$2.class", "size": 1070, "crc": -659132671}, {"key": "androidx/fragment/app/Fragment$3.class", "name": "androidx/fragment/app/Fragment$3.class", "size": 692, "crc": 1697259530}, {"key": "androidx/fragment/app/Fragment$4.class", "name": "androidx/fragment/app/Fragment$4.class", "size": 877, "crc": -1130602230}, {"key": "androidx/fragment/app/Fragment$5.class", "name": "androidx/fragment/app/Fragment$5.class", "size": 1394, "crc": -147606741}, {"key": "androidx/fragment/app/Fragment$6.class", "name": "androidx/fragment/app/Fragment$6.class", "size": 1299, "crc": 1357476513}, {"key": "androidx/fragment/app/Fragment$7.class", "name": "androidx/fragment/app/Fragment$7.class", "size": 1600, "crc": -93106632}, {"key": "androidx/fragment/app/Fragment$8.class", "name": "androidx/fragment/app/Fragment$8.class", "size": 1383, "crc": -1612351254}, {"key": "androidx/fragment/app/Fragment$9.class", "name": "androidx/fragment/app/Fragment$9.class", "size": 2201, "crc": -1717270516}, {"key": "androidx/fragment/app/Fragment$AnimationInfo.class", "name": "androidx/fragment/app/Fragment$AnimationInfo.class", "size": 1622, "crc": -1436520577}, {"key": "androidx/fragment/app/Fragment$Api19Impl.class", "name": "androidx/fragment/app/Fragment$Api19Impl.class", "size": 751, "crc": -595060607}, {"key": "androidx/fragment/app/Fragment$InstantiationException.class", "name": "androidx/fragment/app/Fragment$InstantiationException.class", "size": 733, "crc": 995537107}, {"key": "androidx/fragment/app/Fragment$OnPreAttachedListener.class", "name": "androidx/fragment/app/Fragment$OnPreAttachedListener.class", "size": 656, "crc": -891610344}, {"key": "androidx/fragment/app/Fragment$SavedState$1.class", "name": "androidx/fragment/app/Fragment$SavedState$1.class", "size": 1664, "crc": 1698676680}, {"key": "androidx/fragment/app/Fragment$SavedState.class", "name": "androidx/fragment/app/Fragment$SavedState.class", "size": 1856, "crc": 1627026650}, {"key": "androidx/fragment/app/Fragment.class", "name": "androidx/fragment/app/Fragment.class", "size": 57743, "crc": 1912338700}, {"key": "androidx/fragment/app/FragmentActivity$HostCallbacks.class", "name": "androidx/fragment/app/FragmentActivity$HostCallbacks.class", "size": 8516, "crc": -814219659}, {"key": "androidx/fragment/app/FragmentActivity.class", "name": "androidx/fragment/app/FragmentActivity.class", "size": 14163, "crc": 1636408235}, {"key": "androidx/fragment/app/FragmentAnim$AnimationOrAnimator.class", "name": "androidx/fragment/app/FragmentAnim$AnimationOrAnimator.class", "size": 1066, "crc": 1776596771}, {"key": "androidx/fragment/app/FragmentAnim$EndViewTransitionAnimation.class", "name": "androidx/fragment/app/FragmentAnim$EndViewTransitionAnimation.class", "size": 2188, "crc": -1531645387}, {"key": "androidx/fragment/app/FragmentAnim.class", "name": "androidx/fragment/app/FragmentAnim.class", "size": 4873, "crc": -480830026}, {"key": "androidx/fragment/app/FragmentContainer.class", "name": "androidx/fragment/app/FragmentContainer.class", "size": 1063, "crc": 652806675}, {"key": "androidx/fragment/app/FragmentController.class", "name": "androidx/fragment/app/FragmentController.class", "size": 10142, "crc": 946324772}, {"key": "androidx/fragment/app/FragmentFactory.class", "name": "androidx/fragment/app/FragmentFactory.class", "size": 4280, "crc": 1015559261}, {"key": "androidx/fragment/app/FragmentHostCallback.class", "name": "androidx/fragment/app/FragmentHostCallback.class", "size": 6457, "crc": -723066335}, {"key": "androidx/fragment/app/FragmentLayoutInflaterFactory$1.class", "name": "androidx/fragment/app/FragmentLayoutInflaterFactory$1.class", "size": 2035, "crc": -509564591}, {"key": "androidx/fragment/app/FragmentLayoutInflaterFactory.class", "name": "androidx/fragment/app/FragmentLayoutInflaterFactory.class", "size": 6709, "crc": 1934407957}, {"key": "androidx/fragment/app/FragmentLifecycleCallbacksDispatcher$FragmentLifecycleCallbacksHolder.class", "name": "androidx/fragment/app/FragmentLifecycleCallbacksDispatcher$FragmentLifecycleCallbacksHolder.class", "size": 1123, "crc": -209343377}, {"key": "androidx/fragment/app/FragmentLifecycleCallbacksDispatcher.class", "name": "androidx/fragment/app/FragmentLifecycleCallbacksDispatcher.class", "size": 8822, "crc": -1298657822}, {"key": "androidx/fragment/app/FragmentManager$1.class", "name": "androidx/fragment/app/FragmentManager$1.class", "size": 702, "crc": -2038354534}, {"key": "androidx/fragment/app/FragmentManager$10.class", "name": "androidx/fragment/app/FragmentManager$10.class", "size": 3676, "crc": 1741836562}, {"key": "androidx/fragment/app/FragmentManager$2.class", "name": "androidx/fragment/app/FragmentManager$2.class", "size": 1558, "crc": -1529877733}, {"key": "androidx/fragment/app/FragmentManager$3.class", "name": "androidx/fragment/app/FragmentManager$3.class", "size": 1245, "crc": 245277164}, {"key": "androidx/fragment/app/FragmentManager$4.class", "name": "androidx/fragment/app/FragmentManager$4.class", "size": 1045, "crc": 145835460}, {"key": "androidx/fragment/app/FragmentManager$5.class", "name": "androidx/fragment/app/FragmentManager$5.class", "size": 689, "crc": 350216772}, {"key": "androidx/fragment/app/FragmentManager$6.class", "name": "androidx/fragment/app/FragmentManager$6.class", "size": 2205, "crc": 344756701}, {"key": "androidx/fragment/app/FragmentManager$7.class", "name": "androidx/fragment/app/FragmentManager$7.class", "size": 1164, "crc": 669695924}, {"key": "androidx/fragment/app/FragmentManager$8.class", "name": "androidx/fragment/app/FragmentManager$8.class", "size": 2714, "crc": 2038081164}, {"key": "androidx/fragment/app/FragmentManager$9.class", "name": "androidx/fragment/app/FragmentManager$9.class", "size": 2711, "crc": -58837389}, {"key": "androidx/fragment/app/FragmentManager$BackStackEntry.class", "name": "androidx/fragment/app/FragmentManager$BackStackEntry.class", "size": 758, "crc": 684767018}, {"key": "androidx/fragment/app/FragmentManager$ClearBackStackState.class", "name": "androidx/fragment/app/FragmentManager$ClearBackStackState.class", "size": 1437, "crc": -647801113}, {"key": "androidx/fragment/app/FragmentManager$FragmentIntentSenderContract.class", "name": "androidx/fragment/app/FragmentManager$FragmentIntentSenderContract.class", "size": 4056, "crc": 1568872062}, {"key": "androidx/fragment/app/FragmentManager$FragmentLifecycleCallbacks.class", "name": "androidx/fragment/app/FragmentManager$FragmentLifecycleCallbacks.class", "size": 3043, "crc": 63423793}, {"key": "androidx/fragment/app/FragmentManager$LaunchedFragmentInfo$1.class", "name": "androidx/fragment/app/FragmentManager$LaunchedFragmentInfo$1.class", "size": 1384, "crc": 1698647826}, {"key": "androidx/fragment/app/FragmentManager$LaunchedFragmentInfo.class", "name": "androidx/fragment/app/FragmentManager$LaunchedFragmentInfo.class", "size": 1793, "crc": 996263970}, {"key": "androidx/fragment/app/FragmentManager$LifecycleAwareResultListener.class", "name": "androidx/fragment/app/FragmentManager$LifecycleAwareResultListener.class", "size": 1803, "crc": -258099796}, {"key": "androidx/fragment/app/FragmentManager$OnBackStackChangedListener.class", "name": "androidx/fragment/app/FragmentManager$OnBackStackChangedListener.class", "size": 375, "crc": 1890076294}, {"key": "androidx/fragment/app/FragmentManager$OpGenerator.class", "name": "androidx/fragment/app/FragmentManager$OpGenerator.class", "size": 523, "crc": 1107302979}, {"key": "androidx/fragment/app/FragmentManager$PopBackStackState.class", "name": "androidx/fragment/app/FragmentManager$PopBackStackState.class", "size": 1910, "crc": 1115558612}, {"key": "androidx/fragment/app/FragmentManager$RestoreBackStackState.class", "name": "androidx/fragment/app/FragmentManager$RestoreBackStackState.class", "size": 1445, "crc": -578289189}, {"key": "androidx/fragment/app/FragmentManager$SaveBackStackState.class", "name": "androidx/fragment/app/FragmentManager$SaveBackStackState.class", "size": 1433, "crc": -1302627936}, {"key": "androidx/fragment/app/FragmentManager.class", "name": "androidx/fragment/app/FragmentManager.class", "size": 75276, "crc": -291808164}, {"key": "androidx/fragment/app/FragmentManagerImpl.class", "name": "androidx/fragment/app/FragmentManagerImpl.class", "size": 347, "crc": 1248689216}, {"key": "androidx/fragment/app/FragmentManagerNonConfig.class", "name": "androidx/fragment/app/FragmentManagerNonConfig.class", "size": 2295, "crc": -450646324}, {"key": "androidx/fragment/app/FragmentManagerState$1.class", "name": "androidx/fragment/app/FragmentManagerState$1.class", "size": 1219, "crc": -624770318}, {"key": "androidx/fragment/app/FragmentManagerState.class", "name": "androidx/fragment/app/FragmentManagerState.class", "size": 2886, "crc": 838494163}, {"key": "androidx/fragment/app/FragmentManagerViewModel$1.class", "name": "androidx/fragment/app/FragmentManagerViewModel$1.class", "size": 1111, "crc": -1667991463}, {"key": "androidx/fragment/app/FragmentManagerViewModel.class", "name": "androidx/fragment/app/FragmentManagerViewModel.class", "size": 9895, "crc": -1527127060}, {"key": "androidx/fragment/app/FragmentOnAttachListener.class", "name": "androidx/fragment/app/FragmentOnAttachListener.class", "size": 422, "crc": 611399867}, {"key": "androidx/fragment/app/FragmentPagerAdapter.class", "name": "androidx/fragment/app/FragmentPagerAdapter.class", "size": 5478, "crc": 493634549}, {"key": "androidx/fragment/app/FragmentResultListener.class", "name": "androidx/fragment/app/FragmentResultListener.class", "size": 307, "crc": 742877927}, {"key": "androidx/fragment/app/FragmentResultOwner.class", "name": "androidx/fragment/app/FragmentResultOwner.class", "size": 586, "crc": 799903770}, {"key": "androidx/fragment/app/FragmentState$1.class", "name": "androidx/fragment/app/FragmentState$1.class", "size": 1170, "crc": -1773717170}, {"key": "androidx/fragment/app/FragmentState.class", "name": "androidx/fragment/app/FragmentState.class", "size": 4876, "crc": 729997759}, {"key": "androidx/fragment/app/FragmentStateManager$1.class", "name": "androidx/fragment/app/FragmentStateManager$1.class", "size": 1197, "crc": -1140093686}, {"key": "androidx/fragment/app/FragmentStateManager$2.class", "name": "androidx/fragment/app/FragmentStateManager$2.class", "size": 910, "crc": 1851493813}, {"key": "androidx/fragment/app/FragmentStateManager.class", "name": "androidx/fragment/app/FragmentStateManager.class", "size": 23026, "crc": -2057983647}, {"key": "androidx/fragment/app/FragmentStatePagerAdapter.class", "name": "androidx/fragment/app/FragmentStatePagerAdapter.class", "size": 8006, "crc": 1184517014}, {"key": "androidx/fragment/app/FragmentStore.class", "name": "androidx/fragment/app/FragmentStore.class", "size": 12471, "crc": 1926896512}, {"key": "androidx/fragment/app/FragmentTabHost$DummyTabFactory.class", "name": "androidx/fragment/app/FragmentTabHost$DummyTabFactory.class", "size": 986, "crc": 185174705}, {"key": "androidx/fragment/app/FragmentTabHost$SavedState$1.class", "name": "androidx/fragment/app/FragmentTabHost$SavedState$1.class", "size": 1314, "crc": 803874787}, {"key": "androidx/fragment/app/FragmentTabHost$SavedState.class", "name": "androidx/fragment/app/FragmentTabHost$SavedState.class", "size": 1939, "crc": -1694812910}, {"key": "androidx/fragment/app/FragmentTabHost$TabInfo.class", "name": "androidx/fragment/app/FragmentTabHost$TabInfo.class", "size": 1102, "crc": 76700791}, {"key": "androidx/fragment/app/FragmentTabHost.class", "name": "androidx/fragment/app/FragmentTabHost.class", "size": 9415, "crc": -1478464487}, {"key": "androidx/fragment/app/FragmentTransaction$Op.class", "name": "androidx/fragment/app/FragmentTransaction$Op.class", "size": 1903, "crc": 1447313950}, {"key": "androidx/fragment/app/FragmentTransaction.class", "name": "androidx/fragment/app/FragmentTransaction.class", "size": 15222, "crc": 1421029618}, {"key": "androidx/fragment/app/FragmentTransition.class", "name": "androidx/fragment/app/FragmentTransition.class", "size": 4707, "crc": -490941022}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$1.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$1.class", "size": 1054, "crc": 1580717916}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$2.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$2.class", "size": 2049, "crc": -1587164724}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$3.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$3.class", "size": 2283, "crc": 2042696315}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$4.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$4.class", "size": 1554, "crc": 902227834}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$5.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$5.class", "size": 1164, "crc": -1852709467}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$Api19Impl.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$Api19Impl.class", "size": 1276, "crc": -619191184}, {"key": "androidx/fragment/app/FragmentTransitionCompat21.class", "name": "androidx/fragment/app/FragmentTransitionCompat21.class", "size": 9325, "crc": -1234292176}, {"key": "androidx/fragment/app/FragmentTransitionImpl$1.class", "name": "androidx/fragment/app/FragmentTransitionImpl$1.class", "size": 1492, "crc": 1881540695}, {"key": "androidx/fragment/app/FragmentTransitionImpl.class", "name": "androidx/fragment/app/FragmentTransitionImpl.class", "size": 7930, "crc": 1482248584}, {"key": "androidx/fragment/app/FragmentViewLifecycleOwner.class", "name": "androidx/fragment/app/FragmentViewLifecycleOwner.class", "size": 5387, "crc": -1301480024}, {"key": "androidx/fragment/app/ListFragment$1.class", "name": "androidx/fragment/app/ListFragment$1.class", "size": 776, "crc": 297831562}, {"key": "androidx/fragment/app/ListFragment$2.class", "name": "androidx/fragment/app/ListFragment$2.class", "size": 1186, "crc": 122451947}, {"key": "androidx/fragment/app/ListFragment.class", "name": "androidx/fragment/app/ListFragment.class", "size": 7947, "crc": 1855399880}, {"key": "androidx/fragment/app/LogWriter.class", "name": "androidx/fragment/app/LogWriter.class", "size": 1330, "crc": 480273190}, {"key": "androidx/fragment/app/SpecialEffectsController$1.class", "name": "androidx/fragment/app/SpecialEffectsController$1.class", "size": 1960, "crc": -1728265729}, {"key": "androidx/fragment/app/SpecialEffectsController$2.class", "name": "androidx/fragment/app/SpecialEffectsController$2.class", "size": 1677, "crc": -891291370}, {"key": "androidx/fragment/app/SpecialEffectsController$3.class", "name": "androidx/fragment/app/SpecialEffectsController$3.class", "size": 1611, "crc": -959374194}, {"key": "androidx/fragment/app/SpecialEffectsController$FragmentStateManagerOperation.class", "name": "androidx/fragment/app/SpecialEffectsController$FragmentStateManagerOperation.class", "size": 3648, "crc": -743794516}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation$1.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation$1.class", "size": 1357, "crc": -860122377}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation$LifecycleImpact.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation$LifecycleImpact.class", "size": 1502, "crc": 696147102}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation$State.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation$State.class", "size": 3693, "crc": -1968522949}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation.class", "size": 6576, "crc": -408382288}, {"key": "androidx/fragment/app/SpecialEffectsController.class", "name": "androidx/fragment/app/SpecialEffectsController.class", "size": 11734, "crc": 665184191}, {"key": "androidx/fragment/app/SpecialEffectsControllerFactory.class", "name": "androidx/fragment/app/SpecialEffectsControllerFactory.class", "size": 395, "crc": 1934036371}, {"key": "androidx/fragment/app/SuperNotCalledException.class", "name": "androidx/fragment/app/SuperNotCalledException.class", "size": 418, "crc": 470611905}, {"key": "META-INF/androidx.fragment_fragment.version", "name": "META-INF/androidx.fragment_fragment.version", "size": 6, "crc": -1362322082}, {"key": "META-INF/fragment_release.kotlin_module", "name": "META-INF/fragment_release.kotlin_module", "size": 24, "crc": -1697843264}]