package com.example.fragmentsleam;

import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import java.util.ArrayList;
import java.util.List;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link BlueFragment#newInstance} factory method to
 * create an instance of this fragment.
 */
public class BlueFragment extends Fragment {

    Button frg3;
    RecyclerView recyclerView;
    ItemAdapter itemAdapter;
    List<Item> itemList;

    // TODO: Rename parameter arguments, choose names that match
    // the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
    private static final String ARG_PARAM1 = "param1";
    private static final String ARG_PARAM2 = "param2";

    // TODO: Rename and change types of parameters
    private String mParam1;
    private String mParam2;

    public BlueFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @param param2 Parameter 2.
     * @return A new instance of fragment BlueFragment.
     */
    // TODO: Rename and change types and number of parameters
    public static BlueFragment newInstance(String param1, String param2) {
        BlueFragment fragment = new BlueFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        args.putString(ARG_PARAM2, param2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mParam1 = getArguments().getString(ARG_PARAM1);
            mParam2 = getArguments().getString(ARG_PARAM2);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View v = inflater.inflate(R.layout.fragment_blue, container, false);

        // Initialize RecyclerView
        recyclerView = v.findViewById(R.id.recyclerView);

        // Create data for blue fragment
        createBlueFragmentData();

        // Set up RecyclerView
        setupRecyclerView();

        // Set up button
        frg3 = v.findViewById(R.id.gotoFrg3);
        frg3.setOnClickListener(view -> {
            getActivity()
                    .getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.frame, new PinkFragment("blue"))
                    .commit();
        });

        return v;
    }

    private void createBlueFragmentData() {
        itemList = new ArrayList<>();
        itemList.add(new Item("NAME : Lee Sine", "Role : jungler", R.drawable.lee));
        itemList.add(new Item("NAME : Miss Fortune", "Role : ADC", R.drawable.mf));
        itemList.add(new Item("NAME : Thresh", "Support", R.drawable.nasuse));
        itemList.add(new Item("NAME : Nasus", "Top", R.drawable.thresh));
    }

    private void setupRecyclerView() {
        // Create adapter with data
        itemAdapter = new ItemAdapter(itemList);

        // Set adapter to RecyclerView
        recyclerView.setAdapter(itemAdapter);

        // Add LayoutManager for RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
    }
}