package com.example.fragmentsleam;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

/**
 * Adapter class for RecyclerView
 * Manages the data binding and view creation for the RecyclerView
 */
public class ItemAdapter extends RecyclerView.Adapter<ItemViewHolder> {
    
    private List<Item> itemList;

    public ItemAdapter(List<Item> itemList) {
        this.itemList = itemList;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        // Inflate the layout for each item
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.recycler_single_item, parent, false);
        return new ItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        // Get the current item
        Item currentItem = itemList.get(position);
        
        // Bind data to views
        holder.itemName.setText(currentItem.getName());
        holder.itemRole.setText(currentItem.getRole());
        holder.itemImage.setImageResource(currentItem.getImageResource());
    }

    @Override
    public int getItemCount() {
        return itemList != null ? itemList.size() : 0;
    }
    
    // Method to update the data in the adapter
    public void updateData(List<Item> newItemList) {
        this.itemList = newItemList;
        notifyDataSetChanged();
    }
}
