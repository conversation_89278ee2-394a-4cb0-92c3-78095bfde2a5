{"logs": [{"outputFile": "com.example.fragmentsleam.app-mergeDebugResources-33:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fc6d4f31928c96985f70a5986d93afa\\transformed\\appcompat-1.7.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,420,517,629,714,815,929,1010,1089,1180,1273,1366,1460,1566,1659,1754,1849,1940,2034,2115,2225,2332,2429,2538,2638,2741,2896,9722", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "415,512,624,709,810,924,1005,1084,1175,1268,1361,1455,1561,1654,1749,1844,1935,2029,2110,2220,2327,2424,2533,2633,2736,2891,2989,9798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b8892a28d34db9040615d0cac9baabc9\\transformed\\core-1.13.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3299,3394,3501,3598,3698,3801,3905,10041", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3389,3496,3593,3693,3796,3900,4011,10137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\120a1cf461e0e0ab18d09223aae72de6\\transformed\\material-1.13.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,575,672,787,869,927,992,1080,1144,1205,1281,1371,1435,1498,1560,1628,1692,1756,1825,1881,1935,2058,2123,2185,2239,2310,2437,2521,2595,2692,2773,2857,2993,3070,3147,3263,3350,3429,3486,3541,3607,3683,3763,3834,3910,3977,4051,4121,4187,4289,4375,4445,4536,4626,4700,4773,4862,4913,4994,5066,5147,5233,5295,5359,5422,5491,5605,5699,5807,5897,5991,6081,6142,6201,6281,6365,6444", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,73,71,78,79,96,114,81,57,64,87,63,60,75,89,63,62,61,67,63,63,68,55,53,122,64,61,53,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,93,107,89,93,89,60,58,79,83,78,74", "endOffsets": "265,339,411,490,570,667,782,864,922,987,1075,1139,1200,1276,1366,1430,1493,1555,1623,1687,1751,1820,1876,1930,2053,2118,2180,2234,2305,2432,2516,2590,2687,2768,2852,2988,3065,3142,3258,3345,3424,3481,3536,3602,3678,3758,3829,3905,3972,4046,4116,4182,4284,4370,4440,4531,4621,4695,4768,4857,4908,4989,5061,5142,5228,5290,5354,5417,5486,5600,5694,5802,5892,5986,6076,6137,6196,6276,6360,6439,6514"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2994,3068,3140,3219,4016,4113,4228,4310,4368,4433,4521,4585,4646,4722,4812,4876,4939,5001,5069,5133,5197,5266,5322,5376,5499,5564,5626,5680,5751,5878,5962,6036,6133,6214,6298,6434,6511,6588,6704,6791,6870,6927,6982,7048,7124,7204,7275,7351,7418,7492,7562,7628,7730,7816,7886,7977,8067,8141,8214,8303,8354,8435,8507,8588,8674,8736,8800,8863,8932,9046,9140,9248,9338,9432,9522,9583,9642,9803,9887,9966", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,73,71,78,79,96,114,81,57,64,87,63,60,75,89,63,62,61,67,63,63,68,55,53,122,64,61,53,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,93,107,89,93,89,60,58,79,83,78,74", "endOffsets": "315,3063,3135,3214,3294,4108,4223,4305,4363,4428,4516,4580,4641,4717,4807,4871,4934,4996,5064,5128,5192,5261,5317,5371,5494,5559,5621,5675,5746,5873,5957,6031,6128,6209,6293,6429,6506,6583,6699,6786,6865,6922,6977,7043,7119,7199,7270,7346,7413,7487,7557,7623,7725,7811,7881,7972,8062,8136,8209,8298,8349,8430,8502,8583,8669,8731,8795,8858,8927,9041,9135,9243,9333,9427,9517,9578,9637,9717,9882,9961,10036"}}]}]}